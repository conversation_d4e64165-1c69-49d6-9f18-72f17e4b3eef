#!/usr/bin/env python3
"""
Deployment script for ConTXT Authentication System.
Sets up database schema, creates admin user, and validates the system.
"""
import asyncio
import asyncpg
import bcrypt
import os
import sys
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from app.config.settings import settings
from app.db.postgres_client import PostgreSQLClient
from app.core.auth_service import AuthService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AuthDeployment:
    """Handles authentication system deployment."""
    
    def __init__(self):
        self.postgres_client = PostgreSQLClient()
        self.auth_service = None
    
    async def setup_database(self):
        """Set up database schema and initial data."""
        logger.info("🔧 Setting up authentication database...")
        
        try:
            # Connect to database
            await self.postgres_client.connect()
            pool = await self.postgres_client.get_pool()
            self.auth_service = AuthService(pool)
            
            # Execute schema creation script
            schema_path = Path(__file__).parent / "create_auth_tables.sql"
            if schema_path.exists():
                logger.info("📋 Executing database schema...")
                await self.postgres_client.execute_script(str(schema_path))
                logger.info("✅ Database schema created successfully")
            else:
                logger.error(f"❌ Schema file not found: {schema_path}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Database setup failed: {e}")
            return False
    
    async def create_admin_user(self):
        """Create default admin user if it doesn't exist."""
        logger.info("👤 Setting up admin user...")
        
        try:
            async with self.postgres_client.get_connection() as conn:
                # Check if admin user exists
                admin_exists = await conn.fetchrow(
                    "SELECT id FROM users WHERE email = $1", 
                    "<EMAIL>"
                )
                
                if admin_exists:
                    logger.info("✅ Admin user already exists")
                    return True
                
                # Create admin user
                admin_password = os.getenv("ADMIN_PASSWORD", "AdminPass123!")
                password_hash = bcrypt.hashpw(
                    admin_password.encode('utf-8'), 
                    bcrypt.gensalt()
                ).decode('utf-8')
                
                admin_id = await conn.fetchval("""
                    INSERT INTO users (
                        email, password_hash, first_name, last_name, 
                        is_active, is_verified, subscription_tier
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    RETURNING id
                """, "<EMAIL>", password_hash, "System", "Administrator", 
                    True, True, "enterprise")
                
                # Create admin preferences
                await conn.execute("""
                    INSERT INTO user_preferences (user_id, email_notifications, marketing_emails)
                    VALUES ($1, $2, $3)
                """, admin_id, True, False)
                
                logger.info("✅ Admin user created successfully")
                logger.info(f"📧 Admin email: <EMAIL>")
                logger.info(f"🔑 Admin password: {admin_password}")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ Admin user creation failed: {e}")
            return False
    
    async def validate_system(self):
        """Validate the authentication system is working."""
        logger.info("🔍 Validating authentication system...")
        
        try:
            # Test database connectivity
            if not await self.postgres_client.health_check():
                logger.error("❌ Database health check failed")
                return False
            
            # Test admin login
            from app.schemas.auth import UserLogin
            login_data = UserLogin(
                email="<EMAIL>",
                password=os.getenv("ADMIN_PASSWORD", "AdminPass123!")
            )
            
            try:
                token_response = await self.auth_service.login_user(login_data)
                if token_response.access_token:
                    logger.info("✅ Admin login test successful")
                else:
                    logger.error("❌ Admin login test failed - no token")
                    return False
            except Exception as e:
                logger.error(f"❌ Admin login test failed: {e}")
                return False
            
            # Test token validation
            try:
                import jwt
                payload = jwt.decode(
                    token_response.access_token,
                    settings.JWT_SECRET_KEY,
                    algorithms=[settings.JWT_ALGORITHM]
                )
                if payload.get("type") == "access":
                    logger.info("✅ JWT token validation successful")
                else:
                    logger.error("❌ JWT token validation failed")
                    return False
            except Exception as e:
                logger.error(f"❌ JWT token validation failed: {e}")
                return False
            
            logger.info("🎉 Authentication system validation complete!")
            return True
            
        except Exception as e:
            logger.error(f"❌ System validation failed: {e}")
            return False
    
    async def cleanup_expired_data(self):
        """Clean up expired tokens and sessions."""
        logger.info("🧹 Cleaning up expired data...")
        
        try:
            async with self.postgres_client.get_connection() as conn:
                # Clean up expired tokens
                await conn.execute("SELECT cleanup_expired_tokens()")
                logger.info("✅ Expired tokens cleaned up")
            
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")
    
    async def deploy(self):
        """Execute full deployment process."""
        logger.info("🚀 Starting ConTXT Authentication System deployment...")
        
        try:
            # Setup database
            if not await self.setup_database():
                logger.error("❌ Database setup failed")
                return False
            
            # Create admin user
            if not await self.create_admin_user():
                logger.error("❌ Admin user creation failed")
                return False
            
            # Validate system
            if not await self.validate_system():
                logger.error("❌ System validation failed")
                return False
            
            # Cleanup expired data
            await self.cleanup_expired_data()
            
            logger.info("🎉 ConTXT Authentication System deployed successfully!")
            logger.info("📋 System Summary:")
            logger.info("   • Database schema created")
            logger.info("   • Admin user configured")
            logger.info("   • JWT authentication working")
            logger.info("   • Rate limiting enabled")
            logger.info("   • Health checks active")
            logger.info("   • Metrics collection enabled")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Deployment failed: {e}")
            return False
        
        finally:
            await self.postgres_client.disconnect()

async def main():
    """Main deployment function."""
    deployment = AuthDeployment()
    
    # Check environment variables
    required_vars = ["DB_HOST", "DB_USERNAME", "DB_PASSWORD", "DB_NAME"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {missing_vars}")
        sys.exit(1)
    
    # Run deployment
    success = await deployment.deploy()
    
    if success:
        logger.info("✅ Deployment completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Deployment failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
