"""
Comprehensive authentication tests for ConTXT API.
Tests user registration, login, email verification, password management, and API key management.
"""
import pytest
import asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
import asyncpg
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import jwt
import bcrypt

from app.main import app
from app.core.auth_service import AuthService
from app.schemas.auth import UserRegistration, UserLogin
from app.config.settings import settings

# Test database setup
TEST_DATABASE_URL = "postgresql://test_user:test_pass@localhost/test_contxt"

@pytest.fixture
async def test_db():
    """Create test database connection."""
    try:
        db_pool = await asyncpg.create_pool(TEST_DATABASE_URL)
        yield db_pool
        await db_pool.close()
    except Exception:
        # Mock database if test DB not available
        mock_pool = AsyncMock()
        yield mock_pool

@pytest.fixture
def auth_service(test_db):
    """Create auth service instance."""
    return AuthService(test_db)

@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)

@pytest.fixture
async def async_client():
    """Create async test client."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

class TestUserRegistration:
    """Test user registration functionality."""
    
    async def test_register_valid_user(self, auth_service):
        """Test successful user registration."""
        user_data = UserRegistration(
            email="<EMAIL>",
            password="TestPass123",
            first_name="Test",
            last_name="User"
        )
        
        with patch.object(auth_service, '_send_verification_email') as mock_email:
            with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
                mock_conn = AsyncMock()
                mock_acquire.return_value.__aenter__.return_value = mock_conn
                mock_conn.fetchrow.return_value = None  # No existing user
                mock_conn.fetchval.return_value = "test-user-id"
                
                result = await auth_service.register_user(user_data)
                
                assert "user_id" in result
                assert "verification" in result["message"].lower()
                mock_email.assert_called_once()
    
    async def test_register_duplicate_email(self, auth_service):
        """Test registration with existing email."""
        user_data = UserRegistration(
            email="<EMAIL>",
            password="TestPass123"
        )
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = {"id": "existing-user-id"}
            
            with pytest.raises(Exception) as exc_info:
                await auth_service.register_user(user_data)
            
            assert "already registered" in str(exc_info.value.detail)
    
    def test_register_weak_password(self, client):
        """Test registration with weak password."""
        response = client.post("/auth/register", json={
            "email": "<EMAIL>",
            "password": "weak"
        })
        
        assert response.status_code == 422
        assert "at least 8 characters" in str(response.json())

class TestUserLogin:
    """Test user login functionality."""
    
    async def test_login_valid_credentials(self, auth_service):
        """Test successful login."""
        login_data = UserLogin(
            email="<EMAIL>",
            password="TestPass123"
        )
        
        # Mock user data
        mock_user = {
            'id': 'test-user-id',
            'email': '<EMAIL>',
            'password_hash': bcrypt.hashpw(b'TestPass123', bcrypt.gensalt()).decode('utf-8'),
            'is_active': True,
            'is_verified': True,
            'subscription_tier': 'free',
            'failed_login_attempts': 0,
            'locked_until': None
        }
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = mock_user
            
            result = await auth_service.login_user(login_data)
            
            assert result.access_token
            assert result.refresh_token
            assert result.token_type == "bearer"
            assert result.user_id
    
    async def test_login_invalid_credentials(self, auth_service):
        """Test login with invalid credentials."""
        login_data = UserLogin(
            email="<EMAIL>",
            password="WrongPass123"
        )
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = None
            
            with pytest.raises(Exception) as exc_info:
                await auth_service.login_user(login_data)
            
            assert exc_info.value.status_code == 401
            assert "Invalid email or password" in str(exc_info.value.detail)

class TestEmailVerification:
    """Test email verification functionality."""
    
    async def test_verify_valid_token(self, auth_service):
        """Test email verification with valid token."""
        token = "valid_verification_token"
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = {"user_id": "test-user-id"}
            
            result = await auth_service.verify_email(token)
            assert "verified successfully" in result["message"]
    
    async def test_verify_invalid_token(self, auth_service):
        """Test email verification with invalid token."""
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = None
            
            with pytest.raises(Exception) as exc_info:
                await auth_service.verify_email("invalid_token")
            
            assert exc_info.value.status_code == 400
            assert "Invalid or expired" in str(exc_info.value.detail)

class TestPasswordManagement:
    """Test password reset and change functionality."""
    
    async def test_password_reset_request(self, auth_service):
        """Test password reset request."""
        email = "<EMAIL>"
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = {"id": "test-user-id"}
            
            with patch.object(auth_service, '_send_password_reset_email') as mock_email:
                result = await auth_service.request_password_reset(email)
                
                assert "reset link has been sent" in result["message"]
                mock_email.assert_called_once()
    
    async def test_password_change(self, auth_service):
        """Test password change with valid current password."""
        user_id = "test-user-id"
        current_password = "OldPass123"
        new_password = "NewPass123"
        
        mock_user = {
            'password_hash': bcrypt.hashpw(current_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        }
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = mock_user
            
            from app.schemas.auth import PasswordChange
            password_data = PasswordChange(
                current_password=current_password,
                new_password=new_password
            )
            
            result = await auth_service.change_password(user_id, password_data)
            assert "Password changed successfully" in result["message"]

class TestJWTTokens:
    """Test JWT token functionality."""
    
    def test_create_access_token(self, auth_service):
        """Test access token creation."""
        data = {"sub": "test-user-id", "email": "<EMAIL>"}
        token = auth_service._create_access_token(data)
        
        # Decode token to verify
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        assert payload["sub"] == "test-user-id"
        assert payload["email"] == "<EMAIL>"
        assert payload["type"] == "access"
    
    def test_create_refresh_token(self, auth_service):
        """Test refresh token creation."""
        data = {"sub": "test-user-id"}
        token = auth_service._create_refresh_token(data)
        
        # Decode token to verify
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        assert payload["sub"] == "test-user-id"
        assert payload["type"] == "refresh"

class TestAPIIntegration:
    """Test API endpoint integration."""
    
    @pytest.mark.asyncio
    async def test_complete_user_flow(self, async_client):
        """Test complete user registration and login flow."""
        # Mock database operations
        with patch('app.core.auth_dependencies.get_db_pool') as mock_get_pool:
            mock_pool = AsyncMock()
            mock_get_pool.return_value = mock_pool
            
            with patch('app.core.auth_service.AuthService') as mock_auth_service:
                mock_service = AsyncMock()
                mock_auth_service.return_value = mock_service
                
                # Mock registration
                mock_service.register_user.return_value = {
                    "user_id": "test-user-id",
                    "message": "Registration successful. Please check your email for verification."
                }
                
                # Test registration
                register_response = await async_client.post("/auth/register", json={
                    "email": "<EMAIL>",
                    "password": "TestPass123",
                    "first_name": "Integration",
                    "last_name": "Test"
                })
                
                assert register_response.status_code == 200
                data = register_response.json()
                assert data["success"] is True
                assert "user_id" in data["data"]

class TestRateLimiting:
    """Test rate limiting functionality."""
    
    @pytest.mark.asyncio
    async def test_login_rate_limiting(self, async_client):
        """Test rate limiting on login endpoint."""
        with patch('app.core.auth_dependencies.get_db_pool') as mock_get_pool:
            mock_pool = AsyncMock()
            mock_get_pool.return_value = mock_pool
            
            # Attempt multiple rapid logins
            for i in range(3):
                response = await async_client.post("/auth/login", json={
                    "email": "<EMAIL>",
                    "password": "wrong_password"
                })
                
                # Should get auth errors, not rate limit errors for first few attempts
                assert response.status_code in [401, 422, 500]  # Auth errors

class TestSecurity:
    """Test security features."""
    
    async def test_password_hashing(self, auth_service):
        """Test password hashing security."""
        password = "TestPassword123"
        hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
        
        # Verify password can be checked
        assert bcrypt.checkpw(password.encode('utf-8'), hashed)
        assert not bcrypt.checkpw(b'wrong_password', hashed)
    
    def test_jwt_token_expiration(self, auth_service):
        """Test JWT token expiration."""
        data = {"sub": "test-user-id"}
        
        # Create token with short expiration
        with patch.object(auth_service, 'access_token_expire', 0):  # Immediate expiration
            token = auth_service._create_access_token(data)
            
            # Token should be expired
            with pytest.raises(jwt.ExpiredSignatureError):
                jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])

# Performance and Load Testing Helpers
class TestPerformance:
    """Test performance characteristics."""
    
    @pytest.mark.asyncio
    async def test_concurrent_registrations(self, auth_service):
        """Test handling concurrent user registrations."""
        async def register_user(email_suffix):
            user_data = UserRegistration(
                email=f"user{email_suffix}@example.com",
                password="TestPass123"
            )
            
            with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
                mock_conn = AsyncMock()
                mock_acquire.return_value.__aenter__.return_value = mock_conn
                mock_conn.fetchrow.return_value = None
                mock_conn.fetchval.return_value = f"user-id-{email_suffix}"
                
                with patch.object(auth_service, '_send_verification_email'):
                    return await auth_service.register_user(user_data)
        
        # Test concurrent registrations
        tasks = [register_user(i) for i in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed
        for result in results:
            assert not isinstance(result, Exception)
            assert "user_id" in result

# Fixtures for test data
@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "TestPass123",
        "first_name": "Test",
        "last_name": "User"
    }

@pytest.fixture
def sample_login_data():
    """Sample login data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "TestPass123"
    }

# Run tests with: pytest tests/test_auth.py -v
