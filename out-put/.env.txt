# =============================================================================
# API Keys - Multi-Provider Support (Required to enable respective provider)
# =============================================================================

# Text-to-Text Providers
XAI_API_KEY=************************************************************************************
OPENAI_API_KEY="your_openai_api_key_here"
COHERE_API_KEY="843iNFI4C7xM2DMLjuCss9EYsK1To4MTuoTOjJsf"
ANTHROPIC_API_KEY="your_anthropic_api_key_here"
OPENROUTER_API_KEY="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"
HUGGINGFACE_API_KEY="*************************************"
OLLAMA_API_KEY=""  # Optional for Ollama

# Additional API Keys (for other integrations)
PERPLEXITY_API_KEY="your_perplexity_api_key_here"
GOOGLE_API_KEY="your_google_api_key_here"
MISTRAL_API_KEY="your_mistral_key_here"
AZURE_OPENAI_API_KEY="your_azure_key_here"
GITHUB_API_KEY="your_github_api_key_here"

# =============================================================================
# Provider Selection Configuration
# =============================================================================

# Manual Provider Selection (optional - overrides auto-detection)
MANUAL_TEXT_PROVIDER=xai
MANUAL_EMBEDDING_PROVIDER=openai

# Auto-detected providers will be used if manual selection not set
# Priority order: xai, openai, cohere, anthropic, openrouter, huggingface, ollama

# =============================================================================
# Ollama Configuration (Endpoint Required, API Key Optional)
# =============================================================================
OLLAMA_ENDPOINT="http://localhost:11434"  # Required
OLLAMA_HOST="localhost:11434"  # Alternative to OLLAMA_ENDPOINT
OLLAMA_TEXT_MODEL="llama3.1"
OLLAMA_EMBEDDING_MODEL="nomic-embed-text"

# For Docker environments (uncomment if using Docker)
# OLLAMA_ENDPOINT="http://ollama:11434"

# =============================================================================
# Cognee Configuration (Updated for Multi-Provider)
# =============================================================================

# LLM Configuration (will use selected text provider)
LLM_API_KEY=${XAI_API_KEY}  # Default to XAI key
LLM_PROVIDER="openai"  # xAI uses OpenAI-compatible API
LLM_MODEL="grok-beta"  # or "grok-4" based on your preference

# =============================================================================
# Email Service API Keys
# =============================================================================
RESEND_API_KEY=re_d6hJvXS3_8oNvYzbdz8vs3eSPCy8vUzH3

LLM_ENDPOINT="https://api.x.ai/v1"

# Embedding Configuration (separate from LLM - xAI doesn't have embeddings)
EMBEDDING_PROVIDER="openai"  # Required: xAI doesn't provide embeddings
EMBEDDING_MODEL="text-embedding-3-large"
EMBEDDING_API_KEY="your_openai_api_key_here"  # Required for embeddings

# Processing Configuration
CHUNK_SIZE=1024
CHUNK_OVERLAP=128
USE_COGNEE=true
ENABLE_AI=true

# =============================================================================
# Graph Database Configuration (Neo4j)
# =============================================================================
GRAPH_DATABASE_PROVIDER=neo4j
NEO4J_URI=bolt://neo4j:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password

# Local development override (uncomment for local development)
# NEO4J_URI=bolt://localhost:7687

# =============================================================================
# Vector Database Configuration (Qdrant)
# =============================================================================
VECTOR_DB_PROVIDER=qdrant
VECTOR_DB_URL=http://qdrant:6333
VECTOR_DB_KEY=""  # Empty for local development

# Local development override (uncomment for local development)
# VECTOR_DB_URL=http://localhost:6333

# =============================================================================
# Relational Database Configuration (PostgreSQL)
# =============================================================================
DB_PROVIDER=postgres
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_NAME=document_processor
DB_SSL_MODE=disable

# Local development override (uncomment for local development)
# DB_HOST=localhost
# DB_USERNAME=cognee
# DB_PASSWORD=cognee
# DB_NAME=cognee_db

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=""

# Local development override (uncomment for local development)
# REDIS_URL=redis://localhost:6379/0

# =============================================================================
# FastAPI Application Configuration
# =============================================================================
DEBUG=true
LOG_LEVEL=info
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://localhost:5173"]

# =============================================================================
# File Processing Configuration
# =============================================================================
MAX_FILE_SIZE=104857600  # 100MB in bytes
UPLOAD_PATH=/app/uploads
PROCESSED_PATH=/app/processed
MAX_CONCURRENT_UPLOADS=10
SUPPORTED_FILE_TYPES=["json", "csv", "txt", "md", "pdf", "png", "jpg", "jpeg"]

# =============================================================================
# Celery Configuration
# =============================================================================
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=UTC
CELERY_ENABLE_UTC=true

# =============================================================================
# Security Configuration
# =============================================================================
SECRET_KEY=UC38hxSfHk81WSvF0HGtkM2GY02lz6qeQN4wvtATJI8
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# =============================================================================
# Monitoring and Logging
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_FORMAT=json
LOG_FILE=/app/logs/app.log

# =============================================================================
# Provider Model Configurations (Optional Overrides)
# =============================================================================

# Text-to-Text Model Overrides
XAI_MODEL="grok-beta"
OPENAI_MODEL="gpt-4-turbo-preview"
COHERE_MODEL="command-r-plus"
ANTHROPIC_MODEL="claude-3-5-sonnet-20241022"
OPENROUTER_MODEL="meta-llama/llama-3.1-8b-instruct:free"
HUGGINGFACE_MODEL="microsoft/DialoGPT-medium"
OLLAMA_MODEL="llama3.1"

# Embedding Model Overrides
OPENAI_EMBEDDING_MODEL="text-embedding-3-large"
OPENROUTER_EMBEDDING_MODEL="text-embedding-3-large"
HUGGINGFACE_EMBEDDING_MODEL="sentence-transformers/all-MiniLM-L6-v2"
OLLAMA_EMBEDDING_MODEL="nomic-embed-text"

# =============================================================================
# Provider Endpoint Overrides (Optional)
# =============================================================================
XAI_ENDPOINT="https://api.x.ai/v1"
OPENAI_ENDPOINT="https://api.openai.com/v1"
COHERE_ENDPOINT="https://api.cohere.ai/v1"
ANTHROPIC_ENDPOINT="https://api.anthropic.com/v1"
OPENROUTER_ENDPOINT="https://openrouter.ai/api/v1"
HUGGINGFACE_ENDPOINT="https://api-inference.huggingface.co/models"

# =============================================================================
# Production Overrides (uncomment for production)
# =============================================================================
# DEBUG=false
# ENVIRONMENT=production
# LOG_LEVEL=warning
# NEO4J_URI=neo4j+s://your-production-instance.databases.neo4j.io:7687
# VECTOR_DB_URL=https://your-cluster.cloud.qdrant.io:6333
# VECTOR_DB_KEY=your-production-qdrant-key
# DB_HOST=your-production-db-host
# DB_SSL_MODE=require

# =============================================================================
# Development Features (uncomment for specific testing)
# =============================================================================
# ENABLE_DEBUG_LOGGING=true
# DISABLE_AUTHENTICATION=true
# MOCK_EXTERNAL_APIS=false
# ENABLE_PROFILING=false


# ====
# Resend for sending OTP
# ====

RESEND_API_KEY=re_d6hJvXS3_8oNvYzbdz8vs3eSPCy8vUzH3