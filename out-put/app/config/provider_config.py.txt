"""
Configuration for multi-provider LLM integration.

This module provides configuration settings for the multi-provider LLM system,
including provider-specific settings, cost tiers, and feature flags.
"""
import os
from typing import Dict, Any, Optional, List

# Provider-specific settings
PROVIDER_SETTINGS = {
    "openai": {
        "enabled": True,
        "models": {
            "chat": "gpt-4o",
            "fallback": "gpt-3.5-turbo",
            "embedding": "text-embedding-3-large"
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 10.0,  # $10 per 1M tokens for GPT-4o
        "context_length": 128000,  # GPT-4o context length
        "streaming_supported": True
    },
    "anthropic": {
        "enabled": True,
        "models": {
            "chat": "claude-3-sonnet-20240229",
            "fallback": "claude-3-haiku-20240307",
            "embedding": None  # Anthropic doesn't provide embeddings
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 15.0,  # $15 per 1M tokens for Claude 3 Sonnet
        "context_length": 200000,  # Claude 3 Sonnet context length
        "streaming_supported": True
    },
    "xai": {
        "enabled": True,
        "models": {
            "chat": "grok-beta",
            "fallback": "grok-beta",
            "embedding": None
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 8.0,  # $8 per 1M tokens for Grok
        "context_length": 8192,  # Grok context length
        "streaming_supported": True,
        "base_url": "https://api.x.ai/v1"
    },
    "google": {
        "enabled": True,
        "models": {
            "chat": "gemini-2.0-flash-exp",
            "fallback": "gemini-1.5-flash",
            "embedding": "models/embedding-001"
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 7.0,  # $7 per 1M tokens for Gemini Pro
        "context_length": 32768,  # Gemini Pro context length
        "streaming_supported": True
    },
    "groq": {
        "enabled": True,
        "models": {
            "chat": "llama-3.1-8b-instant",
            "fallback": "llama-3.1-8b-instant",
            "embedding": None
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 1.5,  # $1.5 per 1M tokens for LLama 3
        "context_length": 8192,  # LLama 3 context length
        "streaming_supported": True
    },
    "mistral": {
        "enabled": True,
        "models": {
            "chat": "mistral-large-latest",
            "fallback": "mistral-small-latest",
            "embedding": "mistral-embed"
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 5.0,  # $5 per 1M tokens for Mistral Large
        "context_length": 32768,  # Mistral Large context length
        "streaming_supported": True
    },
    "together": {
        "enabled": True,
        "models": {
            "chat": "meta-llama/Llama-3-70b-chat-hf",
            "fallback": "meta-llama/Llama-2-70b-chat-hf",
            "embedding": "togethercomputer/m2-bert-80M-8k-retrieval"
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 2.0,  # $2 per 1M tokens
        "context_length": 8192,  # Llama 3 context length
        "streaming_supported": True
    },
    "perplexity": {
        "enabled": True,
        "models": {
            "chat": "pplx-70b-online",
            "fallback": "pplx-7b-online",
            "embedding": None
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 10.0,  # $10 per 1M tokens
        "context_length": 4096,  # Perplexity context length
        "streaming_supported": True
    },
    "azure": {
        "enabled": True,
        "models": {
            "chat": os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
            "fallback": os.getenv("AZURE_OPENAI_FALLBACK_DEPLOYMENT", "gpt-35-turbo"),
            "embedding": os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT", "text-embedding-ada-002")
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 10.0,  # $10 per 1M tokens (varies by deployment)
        "context_length": 8192,  # Varies by deployment
        "streaming_supported": True,
        "endpoint": os.getenv("AZURE_OPENAI_ENDPOINT", "")
    },
    "ollama": {
        "enabled": True,
        "models": {
            "chat": "llama3",
            "fallback": "llama2",
            "embedding": "nomic-embed-text"
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 0.0,  # Free (local)
        "context_length": 4096,  # Varies by model
        "streaming_supported": True,
        "base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    },
    "openrouter": {
        "enabled": True,
        "models": {
            "chat": "anthropic/claude-3-sonnet",
            "fallback": "meta/llama-3-8b-instruct",
            "embedding": None  # Uses provider-specific embeddings
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 10.0,  # Varies by selected model
        "context_length": 200000,  # Varies by selected model
        "streaming_supported": True,
        "base_url": "https://openrouter.ai/api/v1",
        "default_headers": {
            "HTTP-Referer": "https://contxt.ai",
            "X-Title": "ConTXT AI"
        }
    }
}

# Provider tier definitions
PROVIDER_TIERS = {
    "speed": ["groq", "ollama"],  # Ultra-fast, affordable providers
    "quality": ["openai", "anthropic", "xai"],  # Premium quality providers
    "balanced": ["mistral", "together", "google"],  # Mid-tier cost-effective providers
    "research": ["perplexity"]  # Specialized for research and fact-checking
}

# User tier rate limits
USER_TIER_LIMITS = {
    "free_tier": {
        "requests_per_minute": 10,
        "monthly_tokens": 100000,  # 100K tokens per month
        "monthly_cost_limit": 0.0,  # $0 monthly limit
        "allowed_providers": ["ollama", "together", "groq"],  # Limited to cost-effective providers
        "default_priority": "speed"  # Optimize for speed
    },
    "pro_tier": {
        "requests_per_minute": 100,
        "monthly_tokens": 1000000,  # 1M tokens per month
        "monthly_cost_limit": 50.0,  # $50 monthly limit
        "allowed_providers": ["openai", "anthropic", "xai", "google", "groq", "mistral", "together", "perplexity", "ollama"],
        "default_priority": "balanced"  # Balanced approach
    },
    "enterprise": {
        "requests_per_minute": 1000,
        "monthly_tokens": 10000000,  # 10M tokens per month
        "monthly_cost_limit": 500.0,  # $500 monthly limit
        "allowed_providers": ["openai", "anthropic", "xai", "google", "groq", "mistral", "together", "perplexity", "azure", "ollama"],
        "default_priority": "quality"  # Optimize for quality
    }
}

# Task-specific provider recommendations
TASK_PROVIDER_RECOMMENDATIONS = {
    "chat": ["openai", "anthropic", "google"],
    "analysis": ["openai", "anthropic", "xai"],
    "summarization": ["groq", "mistral", "together"],
    "extraction": ["openai", "anthropic", "mistral"],
    "classification": ["mistral", "together", "google"],
    "generation": ["openai", "anthropic", "xai"],
    "translation": ["google", "openai", "mistral"],
    "research": ["perplexity", "openai", "anthropic"]
}

# Default embedding providers by priority
DEFAULT_EMBEDDING_PROVIDERS = ["openai", "google", "mistral", "together"]

# Feature flags
ENABLE_PROVIDER_FALLBACK = True  # Enable automatic fallback to alternative providers
ENABLE_COST_OPTIMIZATION = True  # Enable cost optimization strategies
ENABLE_STREAMING = True  # Enable streaming responses when supported
ENABLE_PROVIDER_HEALTH_MONITORING = True  # Enable health monitoring for providers

# Default settings
DEFAULT_PROVIDER = "openrouter"  # Default provider to use
DEFAULT_PRIORITY = "balanced"  # Default priority for provider selection
DEFAULT_USER_TIER = "pro_tier"  # Default user tier
DEFAULT_TASK_TYPE = "chat"  # Default task type

# Function to get provider setting
def get_provider_setting(
    provider: str, 
    setting: str, 
    default: Any = None
) -> Any:
    """
    Get a setting for a provider.
    
    Args:
        provider: Provider name
        setting: Setting name
        default: Default value if setting not found
        
    Returns:
        Setting value or default
    """
    provider_config = PROVIDER_SETTINGS.get(provider, {})
    return provider_config.get(setting, default)

# Function to get model for provider and task
def get_provider_model(
    provider: str, 
    model_type: str = "chat"
) -> Optional[str]:
    """
    Get the model for a provider and model type.
    
    Args:
        provider: Provider name
        model_type: Model type (chat, fallback, embedding)
        
    Returns:
        Model name or None if not available
    """
    provider_config = PROVIDER_SETTINGS.get(provider, {})
    models = provider_config.get("models", {})
    return models.get(model_type)

# Function to get recommended providers for a task
def get_recommended_providers(
    task_type: str, 
    user_tier: str = "pro_tier"
) -> List[str]:
    """
    Get recommended providers for a task type.
    
    Args:
        task_type: Task type
        user_tier: User subscription tier
        
    Returns:
        List of recommended provider names
    """
    # Get task-specific recommendations
    recommended = TASK_PROVIDER_RECOMMENDATIONS.get(task_type, [])
    
    # Filter by allowed providers for user tier
    allowed = USER_TIER_LIMITS.get(user_tier, {}).get("allowed_providers", [])
    
    # Return intersection of recommended and allowed providers
    return [provider for provider in recommended if provider in allowed] 