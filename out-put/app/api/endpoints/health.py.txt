from fastapi import APIRouter, Depends, HTTPException, status
import asyncpg

from app.db.database import get_db_pool

router = APIRouter()

AUTH_TABLES = [
    "users",
    "otps",
    "otp_rate_limits",
    "auth_events",
    "user_sessions",
    "api_keys",
    "user_preferences",
]

@router.get("/auth-db", status_code=status.HTTP_200_OK, summary="Check Authentication Database Health")
async def check_auth_database_health(db_pool: asyncpg.Pool = Depends(get_db_pool)):
    """
    Checks the health of the authentication database.

    Verifies that all required tables for the authentication system exist.
    """
    missing_tables = []
    async with db_pool.acquire() as conn:
        for table in AUTH_TABLES:
            try:
                exists = await conn.fetchval(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = '{table}'
                    );
                """)
                if not exists:
                    missing_tables.append(table)
            except asyncpg.PostgresError as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Database error: {e}"
                )

    if missing_tables:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Missing required authentication tables: {', '.join(missing_tables)}"
        )

    return {"status": "ok", "message": "All authentication tables are present."}
