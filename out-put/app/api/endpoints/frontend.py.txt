"""
Frontend-specific endpoints for theme management and UI state.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

from ..deps import get_current_user, UserInfo

router = APIRouter()

# Pydantic models
class ThemeRequest(BaseModel):
    theme: str  # "dark-oled" or "snow-white"

class ThemeResponse(BaseModel):
    theme: str
    updated_at: datetime

class LayoutState(BaseModel):
    sidebar_open: bool = True
    terminal_open: bool = False
    terminal_height: int = 300
    sidebar_width: int = 300
    active_tab: str = "overview"

class LayoutRequest(BaseModel):
    layout: LayoutState

class LayoutResponse(BaseModel):
    layout: LayoutState
    updated_at: datetime

class NodeDetailsResponse(BaseModel):
    id: str
    title: Optional[str] = None
    type: Optional[str] = None
    properties: Dict[str, Any] = {}
    metadata: Dict[str, Any] = {}
    content: Optional[str] = None
    relationships: list = []
    neighbors: list = []
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

# Mock storage for user preferences (replace with database in production)
USER_THEMES = {}
USER_LAYOUTS = {}

@router.get("/theme", response_model=ThemeResponse)
async def get_user_theme(current_user: UserInfo = Depends(get_current_user)):
    """Get user theme preference"""
    user_id = current_user.user_id
    theme_data = USER_THEMES.get(user_id, {
        "theme": "dark-oled",  # Default theme
        "updated_at": datetime.utcnow()
    })
    
    return ThemeResponse(
        theme=theme_data["theme"],
        updated_at=theme_data["updated_at"]
    )

@router.post("/theme", response_model=ThemeResponse)
async def set_user_theme(
    theme_request: ThemeRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """Set user theme preference"""
    user_id = current_user.user_id
    
    # Validate theme
    valid_themes = ["dark-oled", "snow-white"]
    if theme_request.theme not in valid_themes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid theme. Must be one of: {valid_themes}"
        )
    
    # Store theme preference
    theme_data = {
        "theme": theme_request.theme,
        "updated_at": datetime.utcnow()
    }
    USER_THEMES[user_id] = theme_data
    
    return ThemeResponse(
        theme=theme_data["theme"],
        updated_at=theme_data["updated_at"]
    )

@router.get("/layout", response_model=LayoutResponse)
async def get_layout_state(current_user: UserInfo = Depends(get_current_user)):
    """Get user layout state"""
    user_id = current_user.user_id
    layout_data = USER_LAYOUTS.get(user_id, {
        "layout": LayoutState(),
        "updated_at": datetime.utcnow()
    })
    
    return LayoutResponse(
        layout=layout_data["layout"],
        updated_at=layout_data["updated_at"]
    )

@router.post("/layout", response_model=LayoutResponse)
async def set_layout_state(
    layout_request: LayoutRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """Set user layout state"""
    user_id = current_user.user_id
    
    # Store layout state
    layout_data = {
        "layout": layout_request.layout,
        "updated_at": datetime.utcnow()
    }
    USER_LAYOUTS[user_id] = layout_data
    
    return LayoutResponse(
        layout=layout_data["layout"],
        updated_at=layout_data["updated_at"]
    )

@router.get("/node/{node_id}/details", response_model=NodeDetailsResponse)
async def get_node_details(
    node_id: str,
    current_user: UserInfo = Depends(get_current_user)
):
    """Get comprehensive node information for terminal display"""
    try:
        # Import here to avoid circular imports
        from app.core.knowledge_graph import KnowledgeGraph
        
        kg = KnowledgeGraph()
        
        # Get node details
        node_query = """
        MATCH (n) WHERE elementId(n) = $node_id OR n.id = $node_id
        RETURN n, labels(n) as labels, properties(n) as props
        """
        node_result = await kg.neo4j_client.run_query(node_query, {"node_id": node_id})
        
        if not node_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Node with ID {node_id} not found"
            )
        
        node_data = node_result[0]
        node = node_data["n"]
        labels = node_data["labels"]
        props = node_data["props"]
        
        # Get relationships
        relationships_query = """
        MATCH (n)-[r]-(m) 
        WHERE elementId(n) = $node_id OR n.id = $node_id
        RETURN type(r) as rel_type, 
               CASE WHEN startNode(r) = n THEN 'outgoing' ELSE 'incoming' END as direction,
               m.title as target_title,
               elementId(m) as target_id,
               properties(r) as rel_props
        LIMIT 50
        """
        relationships_result = await kg.neo4j_client.run_query(
            relationships_query, {"node_id": node_id}
        )
        
        relationships = []
        for rel in relationships_result:
            relationships.append({
                "type": rel["rel_type"],
                "direction": rel["direction"],
                "target_title": rel["target_title"],
                "target_id": rel["target_id"],
                "properties": rel["rel_props"]
            })
        
        # Get neighbors
        neighbors_query = """
        MATCH (n)-[r]-(m) 
        WHERE elementId(n) = $node_id OR n.id = $node_id
        RETURN DISTINCT m.title as neighbor_title,
               elementId(m) as neighbor_id,
               labels(m) as neighbor_labels
        LIMIT 20
        """
        neighbors_result = await kg.neo4j_client.run_query(
            neighbors_query, {"node_id": node_id}
        )
        
        neighbors = []
        for neighbor in neighbors_result:
            neighbors.append({
                "id": neighbor["neighbor_id"],
                "title": neighbor["neighbor_title"],
                "labels": neighbor["neighbor_labels"]
            })
        
        # Extract metadata and content
        metadata = {}
        content = None
        
        # Common metadata fields
        metadata_fields = ["created_at", "updated_at", "source", "url", "file_path", "chunk_index"]
        for field in metadata_fields:
            if field in props:
                metadata[field] = props[field]
        
        # Content fields
        content_fields = ["content", "text", "raw_data", "summary", "description"]
        for field in content_fields:
            if field in props and props[field]:
                content = props[field]
                break
        
        return NodeDetailsResponse(
            id=node_id,
            title=props.get("title", props.get("name", node_id)),
            type=labels[0] if labels else "Unknown",
            properties=props,
            metadata=metadata,
            content=content,
            relationships=relationships,
            neighbors=neighbors,
            created_at=props.get("created_at"),
            updated_at=props.get("updated_at")
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching node details: {str(e)}"
        )
