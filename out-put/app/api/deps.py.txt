from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2P<PERSON>wordBearer
from jose import jwt, JWTError
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.config.settings import settings
from app.db.session import SessionLocal
from app.crud.crud_user import get_user_by_email, create_user
from app.models.user import User

reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login"
)

class TokenData(BaseModel):
    username: Optional[str] = None
    
class UserInfo(BaseModel):
    user_id: str
    email: str
    is_active: bool = True
    is_superuser: bool = False

def get_db() -> Generator:
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(reusable_oauth2)
) -> UserInfo:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    
    user = get_user_by_email(db, email=token_data.username)
    if user is None:
        raise credentials_exception
    
    return UserInfo(
        user_id=str(user.id),
        email=user.email,
        is_active=user.is_active,
        is_superuser=user.is_superuser if hasattr(user, 'is_superuser') else False
    )


def get_current_active_user(
    current_user: UserInfo = Depends(get_current_user),
) -> UserInfo:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
