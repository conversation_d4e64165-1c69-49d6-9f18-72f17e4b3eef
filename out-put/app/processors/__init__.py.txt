"""
Document processors for various content types.

This module provides processors for different content types,
including text, markdown, JSON, CSV, PDF, HTML, images, and code.
"""
import os

# Set environment variables for Cognee before importing
# Ensure LOG_LEVEL is uppercase for Cogne<PERSON>
if 'LOG_LEVEL' in os.environ:
    log_level = os.environ['LOG_LEVEL']
    if log_level.lower() in ['info', 'debug', 'warning', 'error', 'critical']:
        os.environ['LOG_LEVEL'] = log_level.upper()

# Now import the processor components
# Define this at the module level before imports
MULTI_PROVIDER_AVAILABLE = False

# Import all processor classes
from .base import BaseProcessor, DatabaseAdapter, AIEnhancementLayer
from .factory import ProcessorFactory
from .text_processor import TextProcessor
from .pdf_processor import PDFProcessor
from .image_processor import ImageProcessor
from .html_processor import HTMLProcessor
from .markdown_processor import MarkdownProcessor
from .code_processor import CodeProcessor
from .csv_processor import CsvProcessor as CSVProcessor
from .json_processor import JsonProcessor as JSONProcessor
from .privacy_processor import PrivacyCompliantProcessor as PrivacyProcessor

try:
    from .processors_updated import MultiProviderProcessor
    MULTI_PROVIDER_AVAILABLE = True
except ImportError:
    # MultiProviderProcessor is optional
    pass

__all__ = [
    'BaseProcessor',
    'DatabaseAdapter',
    'AIEnhancementLayer',
    'ProcessorFactory',
    'TextProcessor',
    'PDFProcessor',
    'ImageProcessor',
    'HTMLProcessor',
    'MarkdownProcessor',
    'CodeProcessor',
    'CSVProcessor',
    'JSONProcessor',
    'PrivacyProcessor',
]

# Add MultiProviderProcessor to exports if available
if MULTI_PROVIDER_AVAILABLE:
    __all__.append("MultiProviderProcessor")

# Create convenience functions for processor factory
def get_processor(file_path=None, content_type=None, content=None, **kwargs):
    """
    Get the appropriate processor for the given file path, content type, or content.
    
    Args:
        file_path: Path to the file (if processing a file)
        content_type: MIME content type (if known)
        content: Content to process (if not processing a file)
        **kwargs: Additional options for the processor
        
    Returns:
        Appropriate processor instance
        
    Raises:
        ValueError: If no processor is available for the file type or content type
    """
    if file_path:
        return ProcessorFactory.get_processor_for_file(file_path, **kwargs)
    elif content_type:
        return ProcessorFactory.get_processor_for_content_type(content_type, **kwargs)
    elif content:
        return ProcessorFactory.get_optimal_processor(content, **kwargs)
    else:
        raise ValueError("At least one of file_path, content_type, or content must be provided")

def get_enhanced_processor(file_path=None, content_type=None, content=None, **kwargs):
    """
    Get an enhanced processor with AI capabilities.
    
    This is a convenience method that enables AI enhancements
    and Cognee integration by default.
    
    Args:
        file_path: Path to the file (if processing a file)
        content_type: MIME content type (if known)
        content: Content to process (if not processing a file)
        **kwargs: Additional options for the processor
        
    Returns:
        Enhanced processor instance
    """
    return ProcessorFactory.get_enhanced_processor(
        file_path=file_path,
        content_type=content_type,
        content=content,
        **kwargs
    ) 