"""
Authentication service for ConTXT API.
Handles user registration, login, email verification, password reset, and JWT token management.
"""
import asyncio
import secrets
import logging
import bcrypt

logger = logging.getLogger(__name__)
import jwt
from datetime import datetime, timedelta, timezone
import json
from typing import Dict, Any, Optional, List
from fastapi import HTT<PERSON>Ex<PERSON>, status
import asyncpg
import logging

from app.config.settings import settings
from app.schemas.auth import (
    UserRegistration, UserLogin, TokenResponse, PasswordReset, 
    PasswordChange, UserProfile, UserProfileUpdate, ApiKeyCreate, ApiKeyResponse,
    OTPVerification, OTPRequest, PasswordResetOTP, RefreshTokenRequest
)
from app.core.otp_service import OTPService
from app.core.email_service import EmailService

logger = logging.getLogger(__name__)

class AuthService:
    """Authentication service with comprehensive user management."""
    
    def __init__(self, db_pool: asyncpg.Pool, otp_service: OTPService, email_service: EmailService):
        self.db_pool = db_pool
        self.otp_service = otp_service
        self.email_service = email_service
        self.jwt_secret = settings.JWT_SECRET_KEY
        self.jwt_algorithm = settings.JWT_ALGORITHM
        self.access_token_expire = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    async def register_user(self, user_data: UserRegistration) -> Dict[str, Any]:
        """Register new user with email verification."""
        async with self.db_pool.acquire() as conn:
            # Check if user exists
            existing_user = await conn.fetchrow(
                "SELECT id FROM users WHERE email = $1", user_data.email
            )
            if existing_user:
                await self._log_auth_event(conn, None, "registration", "failure", additional_data={"reason": "email_already_registered"})
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
            
            # Hash password
            password_hash = bcrypt.hashpw(
                user_data.password.encode('utf-8'), 
                bcrypt.gensalt()
            ).decode('utf-8')
            
            # Create user as inactive until verified
            user_id = await conn.fetchval("""
                INSERT INTO users (email, password_hash, first_name, last_name, is_active, is_verified)
                VALUES ($1, $2, $3, $4, false, false)
                RETURNING id
            """, user_data.email, password_hash, user_data.first_name, user_data.last_name)
            
            # Create default user preferences
            await conn.execute("""
                INSERT INTO user_preferences (user_id)
                VALUES ($1)
            """, user_id)

            try:
                # Request OTP via OTPService
                await self.otp_service.send_verification_otp(
                    email=user_data.email, user_id=user_id
                )
                
                # Log registration event
                await self._log_auth_event(
                    conn, user_id, "registration", "success", 
                    additional_data={"email": user_data.email}
                )
                
                return {
                    "user_id": str(user_id),
                    "message": "Registration successful. Please check your email for verification.",
                    "verification_required": True
                }
            except Exception as e:
                logger.error(f"Error during OTP request for {user_data.email}: {e}")
                # Rollback user creation by deleting the user
                await conn.execute("DELETE FROM users WHERE id = $1", user_id)
                # Log the detailed error
                logger.error(f"Registration failed for {user_data.email}: {e}", exc_info=True)
                # Re-raise with a more informative message for debugging
                await self._log_auth_event(conn, None, "registration", "failure", additional_data={"reason": "otp_request_error", "error": str(e)})
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to send verification email: {str(e)}",
                )
    
    async def login_user(self, login_data: UserLogin, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> TokenResponse:
        """Authenticate user and return JWT tokens."""
        async with self.db_pool.acquire() as conn:
            user = await conn.fetchrow("""
                SELECT id, email, password_hash, is_active, is_verified, 
                       first_name, subscription_tier, failed_login_attempts, locked_until
                FROM users WHERE email = $1
            """, login_data.email)
            
            if not user:
                await self._log_auth_event(
                    conn, None, "login", "failure", 
                    additional_data={"email": login_data.email, "reason": "user_not_found"}
                )
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )
            
            # Check if account is locked
            if user['locked_until'] and user['locked_until'] > datetime.now(timezone.utc):
                await self._log_auth_event(
                    conn, user['id'], "login", "blocked", 
                    additional_data={"reason": "account_locked"}
                )
                raise HTTPException(
                    status_code=status.HTTP_423_LOCKED,
                    detail="Account is temporarily locked due to failed login attempts"
                )
            
            # Verify password
            if not bcrypt.checkpw(
                login_data.password.encode('utf-8'), 
                user['password_hash'].encode('utf-8')
            ):
                # Increment failed attempts
                failed_attempts = user['failed_login_attempts'] + 1
                locked_until = None
                
                if failed_attempts >= 5:
                    locked_until = datetime.now(timezone.utc) + timedelta(minutes=30)
                    # Send account locked email
                    try:
                        await self.email_service.send_account_locked_email(user['email'], user['first_name'])
                    except Exception as e:
                        logger.error(f"Failed to send account locked email to {user['email']}: {e}")

                await conn.execute("""
                    UPDATE users 
                    SET failed_login_attempts = $1, locked_until = $2
                    WHERE id = $3
                """, failed_attempts, locked_until, user['id'])
                
                await self._log_auth_event(
                    conn, user['id'], "login", "failure", 
                    additional_data={"reason": "invalid_password", "failed_attempts": failed_attempts}
                )
                
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )
            
            if not user['is_active']:
                await self._log_auth_event(
                    conn, user['id'], "login", "blocked", 
                    additional_data={"reason": "account_inactive"}
                )
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Account is deactivated"
                )
            
            # Reset failed attempts on successful login
            await conn.execute("""
                UPDATE users 
                SET failed_login_attempts = 0, locked_until = NULL, 
                    last_login_at = now(), login_count = login_count + 1
                WHERE id = $1
            """, user['id'])
            
            # Generate tokens
            access_token = self._create_access_token(
                data={"sub": str(user['id']), "email": user['email']}
            )
            refresh_token = self._create_refresh_token(
                data={"sub": str(user['id'])}
            )
            
            # Store refresh token session
            refresh_token_hash = bcrypt.hashpw(
                refresh_token.encode('utf-8'), bcrypt.gensalt()
            ).decode('utf-8')
            
            await conn.execute("""
                INSERT INTO user_sessions (user_id, refresh_token_hash, ip_address, user_agent, expires_at)
                VALUES ($1, $2, $3, $4, $5)
            """, user['id'], refresh_token_hash, ip_address, user_agent, 
                datetime.now(timezone.utc) + timedelta(days=self.refresh_token_expire))
            
            # Log successful login
            await self._log_auth_event(
                conn, user['id'], "login", "success", 
                additional_data={"ip_address": ip_address}
            )
            
            return TokenResponse(
                access_token=access_token,
                refresh_token=refresh_token,
                expires_in=self.access_token_expire * 60,
                user_id=str(user['id'])
            )
    
    async def verify_email(self, verification_data: OTPVerification) -> Dict[str, str]:
        """Verify user's email using an OTP and activate the account."""
        async with self.db_pool.acquire() as conn:
            user = await conn.fetchrow("SELECT id FROM users WHERE email = $1", verification_data.email)
            if not user:
                await self._log_auth_event(conn, None, "email_verification", "failure", additional_data={"reason": "user_not_found"})
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

            try:
                verification_result = await self.otp_service.verify_email_otp(
                    email=verification_data.email,
                    otp_code=verification_data.otp_code
                )
                if not verification_result.get("success"):
                    raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=verification_result.get("message"))
            except HTTPException as e:
                await self._log_auth_event(conn, user['id'], "email_verification", "failure", additional_data={"reason": e.detail})
                raise e

            # If verification is successful, proceed
                await self._log_auth_event(conn, user['id'], "email_verification", "failure", additional_data={"reason": "invalid_otp"})
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired OTP"
                )

            await conn.execute(
                "UPDATE users SET is_verified = true, is_active = true WHERE email = $1",
                verification_data.email
            )
            await self._log_auth_event(conn, user['id'], "email_verification", "success")

        return {"message": "Email verified successfully. Your account is now active."}
    
    async def request_password_reset(self, otp_request: OTPRequest) -> Dict[str, str]:
        """Request a password reset OTP."""
        async with self.db_pool.acquire() as conn:
            # We don't check for user existence to prevent email enumeration attacks
            await self.otp_service.send_password_reset_otp(email=otp_request.email)
            await self._log_auth_event(conn, None, "password_reset_request", "success", additional_data={"email": otp_request.email})
        return {"message": "If an account with that email exists, an OTP has been sent."}
    
    async def reset_password_with_otp(self, reset_data: PasswordResetOTP) -> Dict[str, str]:
        """Reset password using an OTP."""
        async with self.db_pool.acquire() as conn:
            user = await conn.fetchrow("SELECT id FROM users WHERE email = $1", reset_data.email)
            # First, verify the OTP is valid
            try:
                verification_result = await self.otp_service.verify_password_reset_otp(
                    email=reset_data.email,
                    otp_code=reset_data.otp_code
                )
                if not verification_result.get("success"):
                    raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=verification_result.get("message"))
            except HTTPException as e:
                if user:
                    await self._log_auth_event(conn, user['id'], "password_reset", "failure", additional_data={"reason": e.detail})
                raise e

            # If OTP is valid, proceed
                if user:
                    await self._log_auth_event(conn, user['id'], "password_reset", "failure", additional_data={"reason": "invalid_otp"})
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired OTP."
                )

            # If OTP is valid, proceed with password reset
            if not user:
                # This case should ideally not be hit if OTP verification passed
                await self._log_auth_event(conn, None, "password_reset", "failure", additional_data={"reason": "user_not_found_post_otp"})
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

            # Hash new password
            password_hash = bcrypt.hashpw(
                reset_data.new_password.encode('utf-8'), 
                bcrypt.gensalt()
            ).decode('utf-8')
            
            # Update password and reset failed attempts
            await conn.execute("""
                UPDATE users 
                SET password_hash = $1, failed_login_attempts = 0, locked_until = NULL
                WHERE id = $2
            """, password_hash, user['id'])
            
            # Invalidate all user sessions for security
            await conn.execute("""
                UPDATE user_sessions SET is_active = false WHERE user_id = $1
            """, user['id'])
            
            await self._log_auth_event(conn, user['id'], "password_reset", "success")
            
            return {"message": "Password has been reset successfully."}
    
    async def change_password(self, user_id: str, password_data: PasswordChange) -> Dict[str, str]:
        """Change user password."""
        async with self.db_pool.acquire() as conn:
            # Get current password hash
            user = await conn.fetchrow(
                "SELECT password_hash FROM users WHERE id = $1", user_id
            )
            
            if not user:
                await self._log_auth_event(conn, None, "password_change", "failure", additional_data={"reason": "user_not_found"})
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            # Verify current password
            if not bcrypt.checkpw(
                password_data.current_password.encode('utf-8'),
                user['password_hash'].encode('utf-8')
            ):
                await self._log_auth_event(conn, user_id, "password_change", "failure", additional_data={"reason": "incorrect_password"})
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Current password is incorrect"
                )
            
            # Hash new password
            new_password_hash = bcrypt.hashpw(
                password_data.new_password.encode('utf-8'),
                bcrypt.gensalt()
            ).decode('utf-8')
            
            # Update password
            await conn.execute("""
                UPDATE users SET password_hash = $1 WHERE id = $2
            """, new_password_hash, user_id)
            
            # Log password change
            await self._log_auth_event(
                conn, user_id, "password_change", "success"
            )
            
            return {"message": "Password changed successfully"}
    
    async def refresh_access_token(self, refresh_token: str) -> TokenResponse:
        """Refresh JWT access token."""
        try:
            payload = jwt.decode(
                refresh_token, self.jwt_secret, algorithms=[self.jwt_algorithm]
            )
            
            if payload.get("type") != "refresh":
                # No user_id available here, but we can log the attempt
                async with self.db_pool.acquire() as conn:
                    await self._log_auth_event(conn, None, "refresh_token", "failure", additional_data={"reason": "invalid_token_type"})
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token type"
                )
            
            user_id = payload.get("sub")
            if not user_id:
                async with self.db_pool.acquire() as conn:
                    await self._log_auth_event(conn, None, "refresh_token", "failure", additional_data={"reason": "missing_sub_in_payload"})
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
            
            async with self.db_pool.acquire() as conn:
                # Verify refresh token exists and is active
                session = await conn.fetchrow("""
                    SELECT s.id, u.email, u.subscription_tier
                    FROM user_sessions s
                    JOIN users u ON s.user_id = u.id
                    WHERE s.user_id = $1 AND s.is_active = true AND s.expires_at > now()
                """, user_id)
                
                if not session:
                    await self._log_auth_event(conn, user_id, "refresh_token", "failure", additional_data={"reason": "session_not_found_or_inactive"})
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Invalid or expired refresh token"
                    )
                
                # Generate new access token
                access_token = self._create_access_token(
                    data={"sub": user_id, "email": session['email']}
                )
                
                # Update session last accessed
                await conn.execute("""
                    UPDATE user_sessions 
                    SET last_accessed_at = now()
                    WHERE id = $1
                """, session['id'])
                
                await self._log_auth_event(conn, user_id, "refresh_token", "success")
                return TokenResponse(
                    access_token=access_token,
                    refresh_token=refresh_token,  # Keep same refresh token
                    expires_in=self.access_token_expire * 60,
                    user_id=user_id
                )
                
        except jwt.ExpiredSignatureError as e:
            async with self.db_pool.acquire() as conn:
                await self._log_auth_event(conn, None, "refresh_token", "failure", additional_data={"reason": "expired_signature", "error": str(e)})
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Refresh token expired"
            )
        except jwt.JWTError as e:
            async with self.db_pool.acquire() as conn:
                await self._log_auth_event(conn, None, "refresh_token", "failure", additional_data={"reason": "jwt_error", "error": str(e)})
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )

    async def logout_user(self, user_id: str, refresh_token: Optional[str] = None) -> Dict[str, str]:
        """Logout user and invalidate sessions."""
        async with self.db_pool.acquire() as conn:
            if refresh_token:
                # Invalidate specific session by finding the matching hashed refresh token
                # This is inefficient and assumes we can find the salt. A better design would be a session ID.
                # For now, we will invalidate all sessions for simplicity when a token is provided.
                pass # Fall through to invalidate all for now

            # Invalidate all active sessions for the user
            await conn.execute("""
                UPDATE user_sessions 
                SET is_active = false
                WHERE user_id = $1 AND is_active = true
            """, user_id)
            
            await self._log_auth_event(conn, user_id, "logout", "success")
            
        return {"message": "Logged out successfully"}

    async def create_api_key(self, user_id: str, api_key_data: ApiKeyCreate) -> ApiKeyResponse:
        """Create a new API key for a user."""
        # Placeholder for API key creation logic
        raise NotImplementedError("API key creation is not yet implemented.")
    async def _log_auth_event(
        self,
        conn: asyncpg.Connection,
        user_id: Optional[str],
        event_type: str,
        status: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None,
    ):
        """Log an authentication event to the database."""
        try:
            await conn.execute(
                """
                INSERT INTO auth_events (user_id, event_type, status, ip_address, user_agent, additional_data)
                VALUES ($1, $2, $3, $4, $5, $6::jsonb)
                """,
                user_id,
                event_type,
                status,
                ip_address,
                user_agent,
                json.dumps(additional_data) if additional_data else None
            )
        except Exception as e:
            logger.error(f"Failed to log auth event for user {user_id}: {e}", exc_info=True)

    def _create_access_token(self, data: dict) -> str:
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(minutes=self.access_token_expire)
        to_encode.update({"exp": expire, "type": "access"})
        return jwt.encode(to_encode, self.jwt_secret, algorithm=self.jwt_algorithm)

    def _create_refresh_token(self, data: dict) -> str:
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(days=self.refresh_token_expire)
        to_encode.update({"exp": expire, "type": "refresh"})
        return jwt.encode(to_encode, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    async def _send_password_reset_email(self, email: str, token: str):
        """Send password reset email."""
        reset_url = f"https://contxt.ai/reset-password?token={token}"
        logger.info(f"Send reset email to {email}: {reset_url}")
        # TODO: Implement with SendGrid, AWS SES, or other email service
