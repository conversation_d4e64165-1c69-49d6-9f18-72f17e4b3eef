import resend
import asyncio
import logging
from typing import Optional, Dict
from datetime import datetime
from enum import Enum

from app.config.settings import settings

logger = logging.getLogger(__name__)

class EmailType(Enum):
    OTP_VERIFICATION = "otp_verification"
    PASSWORD_RESET = "password_reset"
    WELCOME = "welcome"
    ACCOUNT_LOCKED = "account_locked"

class EmailService:
    def __init__(self):
        if not settings.RESEND_API_KEY:
            raise ValueError("RESEND_API_KEY is not set in the environment.")
        resend.api_key = settings.RESEND_API_KEY
        
        self.from_address = "ConTXT <<EMAIL>>"
        self.support_email = "<EMAIL>"
        self.max_retries = 3
        self.base_delay = 1
        
    async def send_otp_email(self, email: str, otp_code: str, user_name: Optional[str] = None) -> Dict:
        """Send OTP verification email with security best practices"""
        
        html_template = self._generate_otp_email_html(otp_code, user_name)
        
        params: resend.Emails.SendParams = {
            "from": self.from_address,
            "to": [email],
            "subject": "ConTXT - Verify Your Account (Code Inside)",
            "html": html_template,
            "reply_to": self.support_email,
            "tags": [
                {"name": "type", "value": "otp_verification"},
                {"name": "user_email", "value": email},
                {"name": "timestamp", "value": datetime.now().isoformat()}
            ]
        }
        
        return await self._send_with_retry(params)
    
    async def send_password_reset_otp(self, email: str, otp_code: str, user_name: Optional[str] = None) -> Dict:
        """Send password reset OTP email with security best practices"""
        
        html_template = self._generate_password_reset_email_html(otp_code, user_name)
        
        params: resend.Emails.SendParams = {
            "from": self.from_address,
            "to": [email],
            "subject": "ConTXT - Password Reset Code",
            "html": html_template,
            "reply_to": self.support_email,
            "tags": [
                {"name": "type", "value": "password_reset"},
                {"name": "user_email", "value": email},
                {"name": "timestamp", "value": datetime.now().isoformat()}
            ]
        }
        
        return await self._send_with_retry(params)
    
    async def send_welcome_email(self, email: str, user_name: str) -> Dict:
        """Send welcome email to new users"""
        
        html_template = self._generate_welcome_email_html(user_name)
        
        params: resend.Emails.SendParams = {
            "from": self.from_address,
            "to": [email],
            "subject": "Welcome to ConTXT - Your AI Context Engineering Platform",
            "html": html_template,
            "reply_to": self.support_email,
            "tags": [
                {"name": "type", "value": "welcome"},
                {"name": "user_email", "value": email}
            ]
        }
        
        return await self._send_with_retry(params)
    
    async def send_account_locked_email(self, email: str, user_name: Optional[str] = None) -> Dict:
        """Send account locked notification email"""
        
        html_template = self._generate_account_locked_email_html(user_name)
        
        params: resend.Emails.SendParams = {
            "from": self.from_address,
            "to": [email],
            "subject": "ConTXT - Account Security Alert",
            "html": html_template,
            "reply_to": self.support_email,
            "tags": [
                {"name": "type", "value": "account_locked"},
                {"name": "user_email", "value": email}
            ]
        }
        
        return await self._send_with_retry(params)
    
    async def _send_with_retry(self, params: dict) -> Dict:
        """Send email with exponential backoff retry logic"""
        
        for attempt in range(self.max_retries):
            try:
                result = resend.Emails.send(params)
                logger.info(f"Email sent successfully: {result.id}")
                return {
                    "success": True, 
                    "message_id": result.id,
                    "timestamp": datetime.now().isoformat()
                }
                
            except Exception as e:
                if attempt == self.max_retries - 1:
                    logger.error(f"Final email send failure: {str(e)}")
                    return {
                        "success": False, 
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                    
                delay = self.base_delay * (2 ** attempt)
                logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {str(e)}")
                await asyncio.sleep(delay)
        
        return {"success": False, "error": "Max retries exceeded"}
    
    def _generate_otp_email_html(self, otp_code: str, user_name: Optional[str] = None) -> str:
        """Generate professional OTP email template with modern styling"""
        
        return f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>ConTXT - Verify Your Account</title>
            <style>
                * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }}
                .container {{ max-width: 600px; margin: 0 auto; background: #ffffff; }}
                .header {{ background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; padding: 40px 20px; text-align: center; }}
                .logo {{ font-size: 28px; font-weight: bold; margin-bottom: 10px; }}
                .content {{ padding: 40px 30px; }}
                .otp-container {{ text-align: center; margin: 30px 0; }}
                .otp-code {{ 
                    display: inline-block;
                    font-size: 36px; 
                    font-weight: bold; 
                    color: #2563eb; 
                    background: #f3f4f6; 
                    padding: 20px 30px; 
                    border-radius: 12px; 
                    letter-spacing: 8px;
                    border: 2px solid #e5e7eb;
                }}
                .security-notice {{ 
                    background: #fef3c7; 
                    border-left: 4px solid #f59e0b; 
                    padding: 15px; 
                    margin: 20px 0; 
                }}
                .footer {{ background: #f9fafb; padding: 30px; text-align: center; color: #6b7280; }}
                .button {{ 
                    display: inline-block; 
                    background: #2563eb; 
                    color: white; 
                    padding: 12px 24px; 
                    text-decoration: none; 
                    border-radius: 6px; 
                    margin: 20px 0; 
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">ConTXT</div>
                    <div>Secure Account Verification</div>
                </div>
                <div class="content">
                    <h2>Hello {user_name or 'there'}!</h2>
                    <p>You're almost ready to start using ConTXT. Please verify your email address with the code below:</p>
                    
                    <div class="otp-container">
                        <div class="otp-code">{otp_code}</div>
                        <p>Enter this code in the app to continue</p>
                    </div>
                    
                    <div class="security-notice">
                        <strong>Security Notice:</strong> This code expires in 10 minutes and can only be used once. 
                        Never share this code with anyone.
                    </div>
                    
                    <p>If you didn't create a ConTXT account, you can safely ignore this email.</p>
                    
                    <p>Need help? <a href="mailto:{self.support_email}">Contact our support team</a></p>
                </div>
                <div class="footer">
                    <p>© 2025 ConTXT AI - Intelligent Context Management</p>
                    <p>This is an automated message, please do not reply.</p>
                </div>
            </div>
        </body>
        </html>
        """

    def _generate_password_reset_email_html(self, otp_code: str, user_name: Optional[str] = None) -> str:
        """Generate professional password reset email template"""
        
        return f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>ConTXT - Password Reset</title>
            <style>
                * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }}
                .container {{ max-width: 600px; margin: 0 auto; background: #ffffff; }}
                .header {{ background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; padding: 40px 20px; text-align: center; }}
                .content {{ padding: 40px 30px; }}
                .otp-container {{ text-align: center; margin: 30px 0; }}
                .otp-code {{ 
                    display: inline-block;
                    font-size: 36px; 
                    font-weight: bold; 
                    color: #dc2626; 
                    background: #f3f4f6; 
                    padding: 20px 30px; 
                    border-radius: 12px; 
                    letter-spacing: 8px;
                    border: 2px solid #e5e7eb;
                }}
                .security-notice {{ background: #fef2f2; border-left: 4px solid #dc2626; padding: 15px; margin: 20px 0; }}
                .footer {{ background: #f9fafb; padding: 30px; text-align: center; color: #6b7280; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div style="font-size: 28px; font-weight: bold; margin-bottom: 10px;">ConTXT</div>
                    <div>Password Reset Request</div>
                </div>
                <div class="content">
                    <h2>Hello {user_name or 'there'}!</h2>
                    <p>We received a request to reset your ConTXT password. Use the code below to create a new password:</p>
                    
                    <div class="otp-container">
                        <div class="otp-code">{otp_code}</div>
                        <p>Enter this code in the app to reset your password</p>
                    </div>
                    
                    <div class="security-notice">
                        <strong>Security Notice:</strong> This code expires in 10 minutes. If you didn't request this reset, 
                        please ignore this email and contact support if you have concerns.
                    </div>
                    
                    <p>For security, this code can only be used once.</p>
                    
                    <p>Need help? <a href="mailto:{self.support_email}">Contact Support</a></p>
                </div>
                <div class="footer">
                    <p>© 2025 ConTXT AI</p>
                    <p>Need help? <a href="mailto:{self.support_email}">Contact Support</a></p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _generate_welcome_email_html(self, user_name: str) -> str:
        """Generate welcome email template"""
        
        return f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to ConTXT</title>
            <style>
                * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }}
                .container {{ max-width: 600px; margin: 0 auto; background: #ffffff; }}
                .header {{ background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 40px 20px; text-align: center; }}
                .content {{ padding: 40px 30px; }}
                .feature-list {{ margin: 20px 0; }}
                .feature-item {{ display: flex; align-items: center; margin: 10px 0; }}
                .feature-icon {{ width: 20px; height: 20px; background: #10b981; border-radius: 50%; margin-right: 15px; }}
                .cta-button {{ 
                    display: inline-block; 
                    background: #10b981; 
                    color: white; 
                    padding: 15px 30px; 
                    text-decoration: none; 
                    border-radius: 8px; 
                    font-weight: bold;
                    margin: 20px 0; 
                }}
                .footer {{ background: #f9fafb; padding: 30px; text-align: center; color: #6b7280; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div style="font-size: 28px; font-weight: bold; margin-bottom: 10px;">ConTXT</div>
                    <div>Welcome to AI Context Engineering</div>
                </div>
                <div class="content">
                    <h2>Welcome, {user_name}!</h2>
                    <p>Thank you for joining ConTXT, the intelligent context management platform that revolutionizes how you work with AI.</p>
                    
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="feature-icon"></div>
                            <span>Context Engineering workflows</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"></div>
                            <span>Knowledge Graph visualization</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"></div>
                            <span>Multi-provider AI integration</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"></div>
                            <span>Advanced content ingestion</span>
                        </div>
                    </div>
                    
                    <p>Your account is ready to use. Start by uploading your first content or exploring our knowledge graph features.</p>
                    
                    <div style="text-align: center;">
                        <a href="https://app.contxt.ai/dashboard" class="cta-button">Get Started</a>
                    </div>
                    
                    <p>If you have any questions, our support team is here to help.</p>
                </div>
                <div class="footer">
                    <p>© 2025 ConTXT AI - Intelligent Context Management</p>
                    <p>Questions? <a href="mailto:{self.support_email}">Contact Support</a></p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _generate_account_locked_email_html(self, user_name: Optional[str] = None) -> str:
        """Generate account locked notification email template"""
        
        return f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>ConTXT - Account Security Alert</title>
            <style>
                * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }}
                .container {{ max-width: 600px; margin: 0 auto; background: #ffffff; }}
                .header {{ background: linear-gradient(135deg, #f59e0b, #d97706); color: white; padding: 40px 20px; text-align: center; }}
                .content {{ padding: 40px 30px; }}
                .alert-box {{ 
                    background: #fef3c7; 
                    border-left: 4px solid #f59e0b; 
                    padding: 20px; 
                    margin: 20px 0; 
                    border-radius: 4px;
                }}
                .unlock-button {{ 
                    display: inline-block; 
                    background: #f59e0b; 
                    color: white; 
                    padding: 15px 30px; 
                    text-decoration: none; 
                    border-radius: 8px; 
                    font-weight: bold;
                    margin: 20px 0; 
                }}
                .footer {{ background: #f9fafb; padding: 30px; text-align: center; color: #6b7280; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div style="font-size: 28px; font-weight: bold; margin-bottom: 10px;">ConTXT</div>
                    <div>Account Security Alert</div>
                </div>
                <div class="content">
                    <h2>Hello {user_name or 'there'}!</h2>
                    <p>Your ConTXT account has been temporarily locked for security reasons due to multiple failed login attempts.</p>
                    
                    <div class="alert-box">
                        <strong>Security Alert:</strong> If this wasn't you, please contact our support team immediately. 
                        Your account security is our priority.
                    </div>
                    
                    <p>To unlock your account, please:</p>
                    <ol>
                        <li>Wait 15 minutes for the automatic unlock</li>
                        <li>Or contact our support team for immediate assistance</li>
                        <li>Consider updating your password for enhanced security</li>
                    </ol>
                    
                    <div style="text-align: center;">
                        <a href="mailto:{self.support_email}" class="unlock-button">Contact Support</a>
                    </div>
                    
                    <p>Thank you for helping us keep your account secure.</p>
                </div>
                <div class="footer">
                    <p>© 2025 ConTXT AI</p>
                    <p>Security Team: <a href="mailto:{self.support_email}">Contact Support</a></p>
                </div>
            </div>
        </body>
        </html>
        """
