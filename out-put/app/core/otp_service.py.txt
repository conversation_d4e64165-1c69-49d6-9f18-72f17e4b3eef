import secrets
import string
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional

import asyncpg
from fastapi import HTT<PERSON>Ex<PERSON>, status
import logging

from app.core.email_service import EmailService
from app.config.settings import settings

logger = logging.getLogger(__name__)


class OTPService:
    """Service for generating, sending, and verifying One-Time Passwords (OTPs)."""

    def __init__(self, db_pool: asyncpg.Pool, email_service: EmailService):
        self.db_pool = db_pool
        self.email_service = email_service
        self.otp_length = 6
        self.expiry_minutes = settings.OTP_EXPIRY_MINUTES
        self.rate_limit_window = settings.OTP_RATE_LIMIT_WINDOW
        self.max_attempts = settings.MAX_OTP_ATTEMPTS

    def _generate_otp(self) -> str:
        """Generate a secure OTP."""
        return ''.join(secrets.choice(string.digits) for _ in range(self.otp_length))

    async def _check_rate_limit(self, email: str, conn: asyncpg.Connection):
        """Check and enforce rate limits for OTP generation."""
        now = datetime.now(timezone.utc)
        window_start = now - timedelta(seconds=self.rate_limit_window)

        # Clean up old rate limit entries
        await conn.execute("DELETE FROM otp_rate_limits WHERE created_at < $1", window_start)

        # Count recent requests
        count = await conn.fetchval(
            "SELECT COUNT(*) FROM otp_rate_limits WHERE email = $1 AND created_at >= $2",
            email, window_start
        )

        if count and count >= self.max_attempts:
            logger.warning(f"OTP rate limit exceeded for {email}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Rate limit exceeded. Try again in {self.rate_limit_window} seconds."
            )

        # Record the current request
        await conn.execute(
            "INSERT INTO otp_rate_limits (email, created_at) VALUES ($1, $2)", email, now
        )

    async def send_verification_otp(self, email: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Send OTP for email verification"""
        async with self.db_pool.acquire() as conn:
            await self._check_rate_limit(email, conn)
            # The unified otps table is cleaned up by a central function, so no need for specific cleanup here.

            otp_code = self._generate_otp()
            # In a real app, you would hash the OTP before storing it.
            # For this example, we'll store it directly but acknowledge this is not best practice.
            otp_hash = otp_code # In a real implementation: await self.hash_otp(otp_code)
            expires_at = datetime.now(timezone.utc) + timedelta(minutes=self.expiry_minutes)

            await conn.execute(
                """INSERT INTO otps (user_id, otp_type, otp_hash, expires_at)
                   VALUES ($1, 'email_verification', $2, $3)""",
                user_id, otp_hash, expires_at
            )

            await self.email_service.send_otp_email(email, otp_code)

            return {
                "success": True,
                "message": "Verification code sent to your email",
                "expires_in": self.expiry_minutes * 60
            }

    async def verify_email_otp(self, email: str, otp_code: str) -> Dict[str, Any]:
        """Verify OTP for email verification"""
        async with self.db_pool.acquire() as conn:
            # In a real app, you would hash the provided otp_code to compare it with the stored hash
            otp_hash = otp_code

            otp_record = await conn.fetchrow(
                """SELECT id, user_id, is_used FROM otps
                   WHERE user_id = (SELECT id FROM users WHERE email = $1) 
                   AND otp_type = 'email_verification'
                   AND otp_hash = $2 
                   AND expires_at > now()
                   ORDER BY created_at DESC LIMIT 1""",
                email, otp_hash
            )

            if not otp_record:
                await self.increment_otp_attempt(email, otp_code)
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired verification code"
                )

            if otp_record['is_used']:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Verification code already used")

            # The new schema doesn't track attempts per OTP, but this could be added if needed.
            # For now, we'll remove this check to align with the current schema.


            await conn.execute("UPDATE otps SET is_used = true WHERE id = $1", otp_record['id'])

            if otp_record['user_id']:
                await conn.execute("UPDATE users SET is_verified = true, is_active = true WHERE id = $1", otp_record['user_id'])
                
                # Fetch user details to send welcome email
                user = await conn.fetchrow("SELECT email, first_name FROM users WHERE id = $1", otp_record['user_id'])
                if user:
                    try:
                        await self.email_service.send_welcome_email(user['email'], user['first_name'])
                    except Exception as e:
                        logger.error(f"Failed to send welcome email to {user['email']}: {e}")
                        # Do not block registration flow if email fails

            return {
                "success": True,
                "message": "Email verified successfully",
                "user_id": str(otp_record['user_id']) if otp_record['user_id'] else None
            }

    async def increment_otp_attempt(self, email: str, otp_code: str):
        """Increment failed attempt counter for a specific OTP, if it exists and is not expired."""
        async with self.db_pool.acquire() as conn:
            await conn.execute(
                """UPDATE email_verification_otps SET attempts = attempts + 1
                   WHERE email = $1 AND otp_code = $2 AND expires_at > now() AND is_used = false""",
                email, otp_code
            )

    async def send_password_reset_otp(self, email: str) -> Dict[str, Any]:
        """Send OTP for password reset"""
        async with self.db_pool.acquire() as conn:
            await self._check_rate_limit(email, conn)
            
            user = await conn.fetchrow("SELECT id, first_name FROM users WHERE email = $1 AND is_active = true", email)

            if not user:
                logger.warning(f"Password reset requested for non-existent or inactive user: {email}")
                return {"success": True, "message": "If an account with that email exists, a password reset code has been sent."}

            otp_code = self._generate_otp()
            expires_at = datetime.now(timezone.utc) + timedelta(minutes=self.expiry_minutes)

            await conn.execute(
                "INSERT INTO password_reset_otps (user_id, email, otp_code, expires_at, max_attempts) VALUES ($1, $2, $3, $4, $5)",
                user['id'], email, otp_code, expires_at, self.max_attempts
            )

            await self.email_service.send_password_reset_otp(email, otp_code, user['first_name'])

            return {
                "success": True,
                "message": "Reset code sent to your email",
                "expires_in": self.expiry_minutes * 60
            }

    async def verify_password_reset_otp(self, email: str, otp_code: str) -> Dict[str, Any]:
        """Verify OTP for password reset"""
        async with self.db_pool.acquire() as conn:
            otp_record = await conn.fetchrow(
                """SELECT id, is_used FROM password_reset_otps
                   WHERE email = $1 AND otp_code = $2 AND expires_at > now()
                   ORDER BY created_at DESC LIMIT 1""",
                email, otp_code
            )

            if not otp_record:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired password reset code"
                )

            if otp_record['is_used']:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Password reset code has already been used")

            await conn.execute("UPDATE password_reset_otps SET is_used = true WHERE id = $1", otp_record['id'])

            return {"success": True, "message": "Password reset OTP verified successfully."}
