"""
Multi-model LLM provider integration using LangChain.

This module provides a unified interface for multiple LLM providers,
including OpenRouter, XAI (Grok), OpenAI, Anthropic, Google, and others,
with intelligent provider selection and fallback mechanisms.
"""
import os
import logging
import time
from typing import Dict, List, Any, Optional, Union, Callable
from functools import wraps
from collections import defaultdict

logger = logging.getLogger(__name__)

# Import LangChain components with fallbacks
try:
    from langchain.schema import AIMessage, HumanMessage
    from langchain.schema.runnable import RunnableConfig
    LANGCHAIN_CORE_AVAILABLE = True
except ImportError:
    logger.warning("LangChain core not available. Install with 'pip install langchain-core'")
    LANGCHAIN_CORE_AVAILABLE = False

# Import LangChain provider integrations with fallbacks
providers = {
    "openrouter": False,
    "xai": False,
    "openai": False,
    "anthropic": False,
    "google": False,
    "groq": False,
    "mistral": False,
    "together": False,
    "perplexity": False,
    "azure": False,
    "ollama": False,
}

try:
    from langchain_openai import ChatOpenAI, OpenAIEmbeddings
    providers["openai"] = True
except ImportError:
    logger.warning("LangChain OpenAI integration not available. Install with 'pip install langchain-openai'")

try:
    from langchain_xai import ChatXAI
    providers["xai"] = True
except ImportError:
    logger.warning("LangChain XAI integration not available. Install with 'pip install langchain-xai'")

try:
    from langchain_anthropic import ChatAnthropic
    providers["anthropic"] = True
except ImportError:
    logger.warning("LangChain Anthropic integration not available. Install with 'pip install langchain-anthropic'")

try:
    from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings
    providers["google"] = True
except ImportError:
    logger.warning("LangChain Google AI integration not available. Install with 'pip install langchain-google-genai'")

try:
    from langchain_groq import ChatGroq
    providers["groq"] = True
except ImportError:
    logger.warning("LangChain Groq integration not available. Install with 'pip install langchain-groq'")

try:
    from langchain_mistral import ChatMistral
    providers["mistral"] = True
except ImportError:
    logger.warning("LangChain Mistral integration not available. Install with 'pip install langchain-mistral'")

try:
    from langchain_together import ChatTogether
    providers["together"] = True
except ImportError:
    logger.warning("LangChain Together integration not available. Install with 'pip install langchain-together'")

try:
    from langchain_perplexity import ChatPerplexity
    providers["perplexity"] = True
except ImportError:
    logger.warning("LangChain Perplexity integration not available. Install with 'pip install langchain-perplexity'")

try:
    from langchain_openai import AzureChatOpenAI
    providers["azure"] = True
except ImportError:
    logger.warning("LangChain Azure OpenAI integration not available. Install with 'pip install langchain-openai'")

try:
    from langchain_community.llms import Ollama
    providers["ollama"] = True
except ImportError:
    logger.warning("LangChain Ollama integration not available. Install with 'pip install langchain-community'")

# Try to import OpenRouter integration
try:
    from langchain_openai import ChatOpenAI as OpenRouterChat
    providers["openrouter"] = True
except ImportError:
    logger.warning("OpenRouter integration not available (uses langchain-openai with custom base_url)")


class SecureProviderManager:
    """
    Securely manages API keys and provider configuration.
    """
    
    def __init__(self):
        """Initialize the provider manager."""
        self.providers = {}
        self.provider_health = {}
        self._load_secure_credentials()
    
    def _load_secure_credentials(self) -> Dict[str, str]:
        """
        Load API keys from secure environment variables.
        
        Returns:
            Dictionary of provider credentials
        """
        credentials = {
            "openrouter": os.getenv("OPENROUTER_API_KEY", ""),
            "openai": os.getenv("OPENAI_API_KEY", ""),
            "anthropic": os.getenv("ANTHROPIC_API_KEY", ""),
            "google": os.getenv("GOOGLE_API_KEY", ""),
            "xai": os.getenv("XAI_API_KEY", ""),
            "groq": os.getenv("GROQ_API_KEY", ""),
            "mistral": os.getenv("MISTRAL_API_KEY", ""),
            "perplexity": os.getenv("PERPLEXITY_API_KEY", ""),
            "azure": os.getenv("AZURE_OPENAI_API_KEY", ""),
            "ollama": os.getenv("OLLAMA_API_KEY", "")
        }
        
        # Initialize provider health status
        for provider, api_key in credentials.items():
            self.provider_health[provider] = {
                "available": bool(api_key) and providers.get(provider, False),
                "healthy": bool(api_key) and providers.get(provider, False),
                "last_checked": time.time() if api_key else 0,
                "failure_count": 0
            }
        
        return credentials
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """
        Get API key for a provider.
        
        Args:
            provider: Provider name
            
        Returns:
            API key if available, None otherwise
        """
        return os.getenv(f"{provider.upper()}_API_KEY", "")
    
    def is_provider_available(self, provider: str) -> bool:
        """
        Check if a provider is available (has API key and integration).
        
        Args:
            provider: Provider name
            
        Returns:
            True if provider is available, False otherwise
        """
        return self.provider_health.get(provider, {}).get("available", False)
    
    def mark_provider_unhealthy(self, provider: str) -> None:
        """
        Mark a provider as unhealthy.
        
        Args:
            provider: Provider name
        """
        if provider in self.provider_health:
            self.provider_health[provider]["healthy"] = False
            self.provider_health[provider]["failure_count"] += 1
            self.provider_health[provider]["last_checked"] = time.time()
    
    def mark_provider_healthy(self, provider: str) -> None:
        """
        Mark a provider as healthy.
        
        Args:
            provider: Provider name
        """
        if provider in self.provider_health:
            self.provider_health[provider]["healthy"] = True
            self.provider_health[provider]["failure_count"] = 0
            self.provider_health[provider]["last_checked"] = time.time()
    
    def get_provider_health(self) -> Dict[str, Dict[str, Any]]:
        """
        Get health status of all providers.
        
        Returns:
            Dictionary of provider health status
        """
        return self.provider_health.copy()


class ProviderRateLimiter:
    """
    Rate limiting for LLM provider calls.
    """
    
    def __init__(self):
        """Initialize the rate limiter."""
        self.request_counts = defaultdict(list)
        self.limits = {
            "free_tier": {"requests_per_minute": 10, "monthly_cost_limit": 0},
            "pro_tier": {"requests_per_minute": 100, "monthly_cost_limit": 50},
            "enterprise": {"requests_per_minute": 1000, "monthly_cost_limit": 500}
        }
    
    def rate_limit(self, user_tier: str, provider: str):
        """
        Rate limiting decorator for provider calls.
        
        Args:
            user_tier: User subscription tier
            provider: Provider name
            
        Returns:
            Decorated function
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Check rate limits
                current_time = time.time()
                user_requests = self.request_counts[f"{user_tier}_{provider}"]
                
                # Remove old requests (older than 1 minute)
                user_requests[:] = [req_time for req_time in user_requests 
                                  if current_time - req_time < 60]
                
                # Check if rate limit is exceeded
                limit = self.limits.get(user_tier, {}).get("requests_per_minute", 10)
                if len(user_requests) >= limit:
                    raise ValueError(f"Rate limit exceeded for {provider} with {user_tier}")
                
                # Add current request
                user_requests.append(current_time)
                
                # Call the function
                return await func(*args, **kwargs)
            
            return wrapper
        
        return decorator


class MultiProviderOrchestrator:
    """
    Orchestrates multiple LLM providers with intelligent selection and fallback.
    """
    
    def __init__(self):
        """Initialize the orchestrator."""
        self.provider_manager = SecureProviderManager()
        self.rate_limiter = ProviderRateLimiter()
        
        # Provider tiers based on features and cost
        self.provider_tiers = {
            # Ultra-fast, affordable providers
            "speed": ["groq", "ollama"],
            
            # Premium quality providers
            "quality": ["openai", "anthropic", "xai"],
            
            # Mid-tier cost-effective providers
            "balanced": ["mistral", "together", "google"],
            
            # Specialized for research and fact-checking
            "research": ["perplexity"]
        }
        
        # Provider costs per 1M tokens (input + output combined)
        self.provider_costs = {
            "openai": 10.0,  # $10/1M tokens for GPT-4o
            "anthropic": 15.0,  # $15/1M tokens for Claude 3 Sonnet
            "xai": 8.0,  # $8/1M tokens for Grok
            "google": 7.0,  # $7/1M tokens for Gemini Pro
            "groq": 1.5,  # $1.5/1M tokens for LLama 3
            "mistral": 5.0,  # $5/1M tokens
            "together": 2.0,  # $2/1M tokens
            "perplexity": 10.0,  # $10/1M tokens
            "azure": 10.0,  # $10/1M tokens (varies by deployment)
            "ollama": 0.0,  # Free (local)
            "openrouter": 10.0  # Varies by selected model
        }
        
        # Provider latency characteristics (lower is faster)
        self.provider_latency = {
            "groq": 1,  # Ultra low latency
            "ollama": 2,  # Low latency (local)
            "openai": 3,  # Medium latency
            "anthropic": 3,  # Medium latency
            "google": 3,  # Medium latency
            "xai": 3,  # Medium latency
            "mistral": 3,  # Medium latency
            "together": 3,  # Medium latency
            "perplexity": 4,  # High latency (research optimized)
            "azure": 3,  # Medium latency
            "openrouter": 3  # Medium latency (varies by model)
        }
        
        # Initialize provider clients
        self.provider_clients = {}
        self._initialize_available_providers()
    
    def _initialize_available_providers(self) -> None:
        """Initialize all available provider clients."""
        # Attempt to initialize OpenRouter (preferred gateway)
        if providers["openrouter"] and self.provider_manager.is_provider_available("openrouter"):
            try:
                self.provider_clients["openrouter"] = OpenRouterChat(
                    base_url="https://openrouter.ai/api/v1",
                    api_key=self.provider_manager.get_api_key("openrouter"),
                    model="anthropic/claude-3-sonnet",
                    temperature=0.1,
                    default_headers={
                        "HTTP-Referer": "https://contxt.ai",
                        "X-Title": "ConTXT AI"
                    }
                )
                logger.info("Initialized OpenRouter client")
            except Exception as e:
                logger.error(f"Failed to initialize OpenRouter client: {e}")
        
        # Initialize direct provider clients for fallback
        self._initialize_direct_providers()
    
    def _initialize_direct_providers(self) -> None:
        """Initialize direct provider clients."""
        # Initialize OpenAI
        if providers["openai"] and self.provider_manager.is_provider_available("openai"):
            try:
                self.provider_clients["openai"] = ChatOpenAI(
                    api_key=self.provider_manager.get_api_key("openai"),
                    model="gpt-4o",
                    temperature=0.1
                )
                logger.info("Initialized OpenAI client")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI client: {e}")
        
        # Initialize XAI (Grok)
        if providers["xai"] and self.provider_manager.is_provider_available("xai"):
            try:
                self.provider_clients["xai"] = ChatXAI(
                    xai_api_key=self.provider_manager.get_api_key("xai"),
                    model="grok-beta",
                    temperature=0.1,
                    base_url="https://api.x.ai/v1"
                )
                logger.info("Initialized XAI client")
            except Exception as e:
                logger.error(f"Failed to initialize XAI client: {e}")
        
        # Initialize Anthropic
        if providers["anthropic"] and self.provider_manager.is_provider_available("anthropic"):
            try:
                self.provider_clients["anthropic"] = ChatAnthropic(
                    anthropic_api_key=self.provider_manager.get_api_key("anthropic"),
                    model="claude-3-sonnet-20240229",
                    temperature=0.1,
                    max_tokens=4096
                )
                logger.info("Initialized Anthropic client")
            except Exception as e:
                logger.error(f"Failed to initialize Anthropic client: {e}")
        
        # Initialize Google
        if providers["google"] and self.provider_manager.is_provider_available("google"):
            try:
                self.provider_clients["google"] = ChatGoogleGenerativeAI(
                    google_api_key=self.provider_manager.get_api_key("google"),
                    model="gemini-2.0-flash-exp",
                    temperature=0.1
                )
                logger.info("Initialized Google client")
            except Exception as e:
                logger.error(f"Failed to initialize Google client: {e}")
        
        # Initialize Groq
        if providers["groq"] and self.provider_manager.is_provider_available("groq"):
            try:
                self.provider_clients["groq"] = ChatGroq(
                    groq_api_key=self.provider_manager.get_api_key("groq"),
                    model="llama-3.1-8b-instant",
                    temperature=0.1
                )
                logger.info("Initialized Groq client")
            except Exception as e:
                logger.error(f"Failed to initialize Groq client: {e}")
        
        # Initialize Mistral
        if providers["mistral"] and self.provider_manager.is_provider_available("mistral"):
            try:
                self.provider_clients["mistral"] = ChatMistral(
                    mistral_api_key=self.provider_manager.get_api_key("mistral"),
                    model="mistral-large-latest",
                    temperature=0.1
                )
                logger.info("Initialized Mistral client")
            except Exception as e:
                logger.error(f"Failed to initialize Mistral client: {e}")
        
        # Initialize Together
        if providers["together"] and self.provider_manager.is_provider_available("together"):
            try:
                self.provider_clients["together"] = ChatTogether(
                    together_api_key=self.provider_manager.get_api_key("together"),
                    model="meta-llama/Llama-3-70b-chat-hf",
                    temperature=0.1
                )
                logger.info("Initialized Together client")
            except Exception as e:
                logger.error(f"Failed to initialize Together client: {e}")
        
        # Initialize Perplexity
        if providers["perplexity"] and self.provider_manager.is_provider_available("perplexity"):
            try:
                self.provider_clients["perplexity"] = ChatPerplexity(
                    api_key=self.provider_manager.get_api_key("perplexity"),
                    model="pplx-70b-online",
                    temperature=0.1
                )
                logger.info("Initialized Perplexity client")
            except Exception as e:
                logger.error(f"Failed to initialize Perplexity client: {e}")
        
        # Initialize Azure OpenAI
        if providers["azure"] and self.provider_manager.is_provider_available("azure"):
            try:
                azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT", "")
                if azure_endpoint:
                    self.provider_clients["azure"] = AzureChatOpenAI(
                        azure_endpoint=azure_endpoint,
                        api_key=self.provider_manager.get_api_key("azure"),
                        deployment_name=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
                        temperature=0.1
                    )
                    logger.info("Initialized Azure OpenAI client")
            except Exception as e:
                logger.error(f"Failed to initialize Azure OpenAI client: {e}")
        
        # Initialize Ollama
        if providers["ollama"]:
            try:
                ollama_base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
                self.provider_clients["ollama"] = Ollama(
                    base_url=ollama_base_url,
                    model="llama3",
                    temperature=0.1
                )
                logger.info("Initialized Ollama client")
            except Exception as e:
                logger.error(f"Failed to initialize Ollama client: {e}")
    
    def select_optimal_provider(
        self, 
        task_type: str, 
        user_tier: str = "pro_tier",
        priority: str = "balanced"
    ) -> str:
        """
        Select the optimal provider based on task type, user tier, and priority.
        
        Args:
            task_type: Type of task (e.g., 'chat', 'analysis', 'research')
            user_tier: User subscription tier
            priority: Priority for selection ('speed', 'quality', 'cost')
            
        Returns:
            Selected provider name
        """
        # Get available healthy providers
        available_providers = [
            provider for provider, client in self.provider_clients.items()
            if self.provider_manager.is_provider_available(provider)
        ]
        
        if not available_providers:
            raise ValueError("No available providers")
        
        # Select based on priority
        if priority == "speed" and any(p in available_providers for p in self.provider_tiers["speed"]):
            for provider in self.provider_tiers["speed"]:
                if provider in available_providers:
                    return provider
        
        if priority == "quality" and any(p in available_providers for p in self.provider_tiers["quality"]):
            for provider in self.provider_tiers["quality"]:
                if provider in available_providers:
                    return provider
        
        if task_type == "research" and "perplexity" in available_providers:
            return "perplexity"
        
        # Default to a balanced option based on user tier
        if user_tier == "free_tier" and "ollama" in available_providers:
            return "ollama"
        elif user_tier == "enterprise" and "openai" in available_providers:
            return "openai"
        elif "openrouter" in available_providers:
            # OpenRouter provides good balanced access to multiple models
            return "openrouter"
        
        # Fallback to first available provider
        return available_providers[0]
    
    def get_provider_client(self, provider: str):
        """
        Get the client for a provider.
        
        Args:
            provider: Provider name
            
        Returns:
            Provider client
        """
        if provider not in self.provider_clients:
            raise ValueError(f"Provider {provider} not initialized")
        
        return self.provider_clients[provider]
    
    async def execute_with_fallback(
        self, 
        prompt: str, 
        provider_chain: Optional[List[str]] = None,
        task_type: str = "chat",
        user_tier: str = "pro_tier",
        priority: str = "balanced"
    ):
        """
        Execute a prompt with automatic fallback on failure.
        
        Args:
            prompt: Prompt to execute
            provider_chain: List of providers to try in order (if None, select optimal)
            task_type: Type of task
            user_tier: User subscription tier
            priority: Priority for selection
            
        Returns:
            Result from the provider
        """
        # Determine provider chain if not provided
        if not provider_chain:
            selected_provider = self.select_optimal_provider(
                task_type=task_type,
                user_tier=user_tier,
                priority=priority
            )
            # Create fallback chain based on provider tiers
            provider_chain = [selected_provider]
            
            # Add fallbacks from different tiers
            for tier in ["quality", "balanced", "speed"]:
                for provider in self.provider_tiers[tier]:
                    if (provider in self.provider_clients and 
                        provider not in provider_chain and
                        self.provider_manager.is_provider_available(provider)):
                        provider_chain.append(provider)
            
            # Always add Ollama as last resort if available
            if "ollama" in self.provider_clients and "ollama" not in provider_chain:
                provider_chain.append("ollama")
        
        # Try each provider in the chain
        last_error = None
        for provider in provider_chain:
            try:
                client = self.get_provider_client(provider)
                
                # Apply rate limiting
                @self.rate_limiter.rate_limit(user_tier, provider)
                async def execute_with_rate_limit():
                    # Create messages for the provider
                    messages = [HumanMessage(content=prompt)]
                    
                    # Invoke the provider
                    result = await client.ainvoke(messages)
                    return result
                
                result = await execute_with_rate_limit()
                
                # Mark provider as healthy
                self.provider_manager.mark_provider_healthy(provider)
                
                return result
            except Exception as e:
                logger.error(f"Provider {provider} failed: {e}")
                self.provider_manager.mark_provider_unhealthy(provider)
                last_error = e
        
        # If we get here, all providers failed
        raise ValueError(f"All providers failed: {last_error}")
    
    def initialize_embedding_provider(
        self, 
        provider: str = "openai", 
        model: str = "text-embedding-3-large"
    ):
        """
        Initialize an embedding provider.
        
        Args:
            provider: Provider name
            model: Model name
            
        Returns:
            Embedding provider
        """
        if provider == "openai" and providers["openai"]:
            try:
                return OpenAIEmbeddings(
                    api_key=self.provider_manager.get_api_key("openai"),
                    model=model
                )
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI embeddings: {e}")
        
        if provider == "google" and providers["google"]:
            try:
                return GoogleGenerativeAIEmbeddings(
                    google_api_key=self.provider_manager.get_api_key("google"),
                    model="models/embedding-001"
                )
            except Exception as e:
                logger.error(f"Failed to initialize Google embeddings: {e}")
        
        logger.warning(f"Unsupported embedding provider: {provider}")
        return None


# Create a default instance for convenience
default_orchestrator = MultiProviderOrchestrator() 