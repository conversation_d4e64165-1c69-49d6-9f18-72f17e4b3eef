"""
Knowledge Graph Manager.

This module provides an interface to the Neo4j knowledge graph,
implementing operations for storing, retrieving, and querying
knowledge representations.
"""
import uuid
from typing import Dict, List, Optional, Any, Union

from app.db.neo4j_client import Neo4jClient

class KnowledgeGraph:
    """
    Manager for knowledge graph operations.
    """
    
    def __init__(self):
        """Initialize the knowledge graph manager.""" 
        self.neo4j_client = Neo4jClient()

    async def get_all_nodes(
        self,
        limit: int = 100,
        node_type: Optional[str] = None,
        search: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get all nodes with metadata for visualization"""
        query = "MATCH (n) "
        params: Dict[str, Any] = {}
        conditions = []

        if node_type:
            query += f"WHERE labels(n)[0] = $node_type "
            params["node_type"] = node_type

        if search:
            search_condition = "(n.name CONTAINS $search OR n.title CONTAINS $search OR n.content CONTAINS $search)"
            if conditions:
                query += "AND " + search_condition
            else:
                query += "WHERE " + search_condition
            params["search"] = search

        query += "RETURN elementId(n) as id, n as properties, labels(n) as labels LIMIT $limit"
        params["limit"] = limit

        result = await self.neo4j_client.run_query(query, params)
        
        nodes = []
        for record in result:
            properties = record["properties"]
            node = {
                "id": record["id"],
                "label": properties.get("title", properties.get("name", "Unknown")),
                "type": record["labels"][0] if record["labels"] else "Unknown",
                "content": properties.get("content", "")[:200] + "...",
                "metadata": dict(properties)
            }
            nodes.append(node)
        return nodes

    async def get_all_relationships(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get all relationships for visualization"""
        query = """
        MATCH (a)-[r]->(b)
        RETURN elementId(a) as source,
               elementId(b) as target,
               type(r) as type,
               properties(r) as properties
        LIMIT $limit
        """
        result = await self.neo4j_client.run_query(query, {"limit": limit})
        
        relationships = []
        for record in result:
            rel = {
                "source": record["source"],
                "target": record["target"],
                "type": record["type"],
                "weight": record["properties"].get("weight", 1.0),
                "metadata": record["properties"]
            }
            relationships.append(rel)
        return relationships

    async def get_neighbors(self, node_id: str, depth: int = 1) -> Dict[str, List]:
        """Get neighbors of a specific node up to a certain depth."""
        query = """
        MATCH (startNode)
        WHERE elementId(startNode) = $node_id
        CALL apoc.path.subgraphAll(startNode, {maxLevel: $depth})
        YIELD nodes, relationships
        RETURN nodes, relationships
        """
        params = {"node_id": node_id, "depth": depth}
        result = await self.neo4j_client.run_query(query, params)

        if not result:
            return {"nodes": [], "links": []}

        raw_nodes = result[0].get('nodes', [])
        raw_rels = result[0].get('relationships', [])

        nodes = [
            {
                "id": n.element_id,
                "label": n.get("title", n.get("name", "Unknown")),
                "type": list(n.labels)[0] if n.labels else "Unknown",
                "metadata": dict(n)
            }
            for n in raw_nodes
        ]

        links = [
            {
                "source": r.start_node.element_id,
                "target": r.end_node.element_id,
                "type": r.type,
                "metadata": dict(r)
            }
            for r in raw_rels
        ]
        
        return {"nodes": nodes, "links": links}

    async def get_graph_analytics(self) -> Dict[str, Any]:
        """Get graph analytics and statistics"""
        stats_query = """
        MATCH (n)
        OPTIONAL MATCH ()-[r]->()
        RETURN 
            count(DISTINCT n) as total_nodes,
            count(DISTINCT r) as total_relationships
        """
        stats_result = await self.neo4j_client.run_query(stats_query)
        stats: Dict[str, Any] = dict(stats_result[0]) if stats_result else {'total_nodes': 0, 'total_relationships': 0}

        node_dist_query = "MATCH (n) RETURN labels(n)[0] as node_type, count(n) as count ORDER BY count DESC"
        node_dist_result = await self.neo4j_client.run_query(node_dist_query)
        stats['node_distribution'] = [{'type': r['node_type'], 'count': r['count']} for r in node_dist_result]
        stats['node_types'] = [r['type'] for r in stats['node_distribution']]

        rel_dist_query = "MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count ORDER BY count DESC"
        rel_dist_result = await self.neo4j_client.run_query(rel_dist_query)
        stats['relationship_distribution'] = [{'type': r['rel_type'], 'count': r['count']} for r in rel_dist_result]
        stats['relationship_types'] = [r['type'] for r in stats['relationship_distribution']]

        return stats 