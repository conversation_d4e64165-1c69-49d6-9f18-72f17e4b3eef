"""
Main application module for the AI Context Engineering Agent.
Comprehensive backend with authentication, context engineering, and knowledge management.
"""
import logging
from typing import Optional
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import Counter, Histogram, generate_latest
import time
import sys

from app.config.settings import settings

# Configure logging
logging.basicConfig(
    level=settings.LOG_LEVEL.upper(),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stdout,
)
from app.api.router import api_router
from app.middleware.security import add_security_headers
# from app.api.endpoints.auth import router as auth_router
from app.db.postgres_client import get_postgres_client, close_postgres_client
from app.core.auth_dependencies import get_optional_user
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

# Metrics setup
request_count = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
request_duration = Histogram('http_request_duration_seconds', 'HTTP request duration')
auth_requests = Counter('auth_requests_total', 'Total auth requests', ['endpoint', 'status'])

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Application lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application startup and shutdown."""
    # Startup
    logging.info("Starting ConTXT Backend with Authentication...")
    
    try:
        # Initialize database connections
        postgres_client = await get_postgres_client()
        logging.info("✓ PostgreSQL connection established")
        
        # Test database connectivity
        if await postgres_client.health_check():
            logging.info("✓ Database health check passed")
        else:
            logging.error("✗ Database health check failed")
        
        logging.info("🚀 ConTXT Backend startup complete")
        
    except Exception as e:
        logging.error(f"❌ Startup failed: {e}")
        raise
    
    yield
    
    # Shutdown
    logging.info("Shutting down ConTXT Backend...")
    try:
        await close_postgres_client()
        logging.info("✓ Database connections closed")
    except Exception as e:
        logging.error(f"Error during shutdown: {e}")
    
    logging.info("👋 ConTXT Backend shutdown complete")

app = FastAPI(
    title="ConTXT - AI Context Engineering Agent",
    description="Enterprise-grade backend API for AI context engineering, knowledge management, and user authentication",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    openapi_components={
        "securitySchemes": {
            "JWTBearer": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
                "description": "Enter JWT token in the format: Bearer &lt;token&gt;"
            }
        }
    }
)

# Apply the security scheme globally to all endpoints
app.openapi_schema = app.openapi()
app.openapi_schema["security"] = [{"JWTBearer": []}]

# Add security headers middleware
app.middleware("http")(add_security_headers)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limiting middleware
app.state.limiter = limiter
app.add_middleware(SlowAPIMiddleware)
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Metrics middleware
@app.middleware("http")
async def metrics_middleware(request, call_next):
    """Collect metrics for all HTTP requests."""
    start_time = time.time()
    
    response = await call_next(request)
    
    # Record metrics
    duration = time.time() - start_time
    method = request.method
    endpoint = request.url.path
    status = str(response.status_code)
    
    request_count.labels(method=method, endpoint=endpoint, status=status).inc()
    request_duration.observe(duration)
    
    # Track auth-specific metrics
    if endpoint.startswith("/auth/"):
        auth_requests.labels(endpoint=endpoint, status=status).inc()
    
    return response

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Handle unexpected exceptions gracefully."""
    logging.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "success": False}
    )

# Include API routes
app.include_router(api_router, prefix="/api")
# app.include_router(auth_router, tags=["Authentication"]) 

class ConnectionManager:
    def __init__(self):
        self.active_connections: list[WebSocket] = []
        self.user_connections: dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, user_id: Optional[str] = None):
        await websocket.accept()
        self.active_connections.append(websocket)
        if user_id:
            self.user_connections[user_id] = websocket

    def disconnect(self, websocket: WebSocket, user_id: Optional[str] = None):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if user_id and user_id in self.user_connections:
            del self.user_connections[user_id]

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                disconnected.append(connection)
        
        # Clean up disconnected connections
        for conn in disconnected:
            if conn in self.active_connections:
                self.active_connections.remove(conn)
    
    async def send_to_user(self, user_id: str, message: dict):
        if user_id in self.user_connections:
            try:
                import json
                await self.user_connections[user_id].send_text(json.dumps(message))
            except:
                del self.user_connections[user_id]
    
    async def broadcast_graph_update(self, update_data: dict):
        import json
        message = json.dumps({
            "type": "graph_update",
            "data": update_data
        })
        await self.broadcast(message)

manager = ConnectionManager()

@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str, token: Optional[str] = None):
    """
    WebSocket endpoint for real-time communication.
    Requires a token for authentication.
    """
    if not token:
        await websocket.close(code=1008)
        return

    # Here you would typically validate the token.
    # For now, we'll just accept the connection if a token is present.

    await manager.connect(websocket, user_id)
    print(f"Client #{user_id} connected with token.")
    try:
        while True:
            # Keep the connection alive.
            # A more robust implementation would handle incoming messages.
            await websocket.receive_text() 
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
        print(f"Client #{user_id} disconnected: {websocket.client.host}")
    except Exception as e:
        print(f"WebSocket Error: {e}")
        manager.disconnect(websocket)

@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "AI Context Engineering Agent is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True) 