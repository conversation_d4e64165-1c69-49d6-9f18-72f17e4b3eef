# app/middleware/security.py
from fastapi import Request
from starlette.responses import Response
from app.config.settings import settings

async def add_security_headers(request: Request, call_next):
    response: Response = await call_next(request)
    
    # Default security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    
    # Content Security Policy (CSP)
    csp_policy = {
        "default-src": "'self'",
        "script-src": "'self' 'unsafe-inline' https://cdn.jsdelivr.net", # Allow inline scripts and scripts from jsdelivr
        "style-src": "'self' 'unsafe-inline' https://fonts.googleapis.com", # Allow inline styles and fonts from Google
        "font-src": "'self' https://fonts.gstatic.com",
        "img-src": "'self' data:",
        "connect-src": "'self'",
        "form-action": "'self'",
        "frame-ancestors": "'none'", # Disallow embedding in iframes
        "base-uri": "'self'",
        "object-src": "'none'", # Disallow plugins like Flash
        "upgrade-insecure-requests": "", # This will be handled by HSTS
    }
    
    # Allow overriding CSP from settings for different environments
    if settings.CSP_POLICY_OVERRIDES:
        csp_policy.update(settings.CSP_POLICY_OVERRIDES)
        
    csp_header_value = "; ".join([f"{key} {value}" for key, value in csp_policy.items() if value is not None])
    response.headers["Content-Security-Policy"] = csp_header_value
    
    # HTTP Strict Transport Security (HSTS)
    if settings.ENABLE_HSTS:
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains; preload"
        
    # Referrer-Policy
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    
    # Permissions-Policy
    response.headers["Permissions-Policy"] = "camera=(), microphone=(), geolocation=()"

    return response

