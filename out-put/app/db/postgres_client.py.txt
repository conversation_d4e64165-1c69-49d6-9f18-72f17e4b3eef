"""
PostgreSQL database client for ConTXT authentication system.
Provides connection pool management and database operations.
"""
import asyncio
import asyncpg
import logging
from typing import Optional
from contextlib import asynccontextmanager

from app.config.settings import settings

logger = logging.getLogger(__name__)

class PostgreSQLClient:
    """PostgreSQL client with connection pooling."""
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self._connection_string = self._build_connection_string()
    
    def _build_connection_string(self) -> str:
        """Build PostgreSQL connection string from settings."""
        return (
            f"postgresql://{settings.DB_USERNAME}:{settings.DB_PASSWORD}"
            f"@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
            f"?sslmode={settings.DB_SSL_MODE}"
        )
    
    async def connect(self) -> None:
        """Initialize database connection pool."""
        try:
            self.pool = await asyncpg.create_pool(
                self._connection_string,
                min_size=5,
                max_size=20,
                command_timeout=60,
                server_settings={
                    'jit': 'off'  # Disable JIT for better performance on small queries
                }
            )
            logger.info("PostgreSQL connection pool created successfully")
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
                logger.info("PostgreSQL connection test successful")
                
        except Exception as e:
            logger.error(f"Failed to create PostgreSQL connection pool: {e}")
            raise
    
    async def disconnect(self) -> None:
        """Close database connection pool."""
        if self.pool:
            await self.pool.close()
            logger.info("PostgreSQL connection pool closed")
    
    async def get_pool(self) -> asyncpg.Pool:
        """Get database connection pool."""
        if not self.pool:
            await self.connect()
        return self.pool
    
    @asynccontextmanager
    async def get_connection(self):
        """Get database connection from pool."""
        if not self.pool:
            await self.connect()
        
        async with self.pool.acquire() as connection:
            yield connection
    
    async def execute_script(self, script_path: str) -> None:
        """Execute SQL script file."""
        try:
            with open(script_path, 'r') as file:
                script = file.read()
            
            async with self.get_connection() as conn:
                await conn.execute(script)
                logger.info(f"Successfully executed script: {script_path}")
                
        except Exception as e:
            logger.error(f"Failed to execute script {script_path}: {e}")
            raise
    
    async def health_check(self) -> bool:
        """Check database health."""
        try:
            async with self.get_connection() as conn:
                await conn.fetchval("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False

# Global client instance
_postgres_client: Optional[PostgreSQLClient] = None

async def get_postgres_client() -> PostgreSQLClient:
    """Get global PostgreSQL client instance."""
    global _postgres_client
    if _postgres_client is None:
        _postgres_client = PostgreSQLClient()
        await _postgres_client.connect()
    return _postgres_client

async def get_db_pool() -> asyncpg.Pool:
    """Get database connection pool (dependency injection)."""
    client = await get_postgres_client()
    return await client.get_pool()

async def close_postgres_client() -> None:
    """Close global PostgreSQL client."""
    global _postgres_client
    if _postgres_client:
        await _postgres_client.disconnect()
        _postgres_client = None
