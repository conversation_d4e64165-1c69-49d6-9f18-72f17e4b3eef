# ConTXT Frontend Theme Documentation

## 🎨 Visual Design Language

### Brand Identity
ConTXT employs a sophisticated, AI-focused design language that emphasizes:
- **Intelligence & Precision**: Clean, geometric forms with purposeful animations
- **Connectivity**: Network-inspired visual metaphors throughout the interface
- **Premium Feel**: High-contrast dark theme with selective color accents
- **Accessibility**: WCAG-compliant color combinations and readable typography

### Color Philosophy
The design system uses a **red-dominant palette** to convey:
- **Energy & Innovation**: Red gradients for primary actions and highlights
- **Trust & Reliability**: Neutral grays for content and structure
- **Intelligence**: Subtle blue and green accents for secondary features
- **Depth & Sophistication**: Black backgrounds with layered transparency

## 🌈 Color System

### Primary Color Palette
The theme uses **OKLCH color space** for perceptually uniform color transitions:

#### Light Theme (`:root`)
```css
--background: oklch(1 0 0);           /* Pure white */
--foreground: oklch(0.145 0 0);       /* Near black */
--primary: oklch(0.205 0 0);          /* Dark gray */
--primary-foreground: oklch(0.985 0 0); /* Off white */
```

#### Dark Theme (`.dark`)
```css
--background: oklch(0.145 0 0);       /* Dark charcoal */
--foreground: oklch(0.985 0 0);       /* Off white */
--primary: oklch(0.985 0 0);          /* Light text */
--primary-foreground: oklch(0.205 0 0); /* Dark background */
```

### Brand Colors (Hardcoded)
The application uses specific brand colors outside the CSS variable system:

#### Red Gradient System
- **Primary Red**: `#ef4444` (red-500)
- **Dark Red**: `#dc2626` (red-600) 
- **Light Red**: `#f87171` (red-400)
- **Gradient**: `from-red-400 via-red-500 to-red-600`

#### Secondary Accents
- **Blue**: `#3b82f6` (blue-500) - For secondary features
- **Green**: `#10b981` (green-500) - For success states
- **Purple**: `#8b5cf6` (purple-500) - For premium features
- **Orange**: `#f59e0b` (orange-500) - For document processing

### Semantic Colors
```css
/* Destructive actions */
--destructive: oklch(0.577 0.245 27.325);     /* Light theme */
--destructive: oklch(0.396 0.141 25.723);     /* Dark theme */

/* Chart colors for data visualization */
--chart-1: oklch(0.646 0.222 41.116);
--chart-2: oklch(0.6 0.118 184.704);
--chart-3: oklch(0.398 0.07 227.392);
--chart-4: oklch(0.828 0.189 84.429);
--chart-5: oklch(0.769 0.188 70.08);
```

## 🕸️ Interactive Mesh Background System

### Architecture Overview
The mesh background is a **canvas-based particle system** that creates dynamic, interactive visualizations representing AI context connections.

### Node System
```typescript
interface MeshNode {
  id: string              // Unique identifier
  x: number              // Current X position
  y: number              // Current Y position
  originalX: number      // Base X position for floating
  originalY: number      // Base Y position for floating
  vx: number             // X velocity
  vy: number             // Y velocity
  radius: number         // Node size (0.8-2.0)
  connections: string[]  // Connected node IDs
  glowIntensity: number  // Glow effect strength (0.2-0.5)
  pulsePhase: number     // Animation phase offset
  gridX: number          // Spatial grid X coordinate
  gridY: number          // Spatial grid Y coordinate
  isVisible: boolean     // Viewport culling flag
}
```

### Connection System
```typescript
interface MeshConnection {
  source: string         // Source node ID
  target: string         // Target node ID
  distance: number       // Euclidean distance
  opacity: number        // Connection strength (0.1-0.4)
  isVisible: boolean     // Viewport culling flag
}
```

### Particle System
```typescript
interface Particle {
  x: number              // Current X position
  y: number              // Current Y position
  speed: number          // Movement speed (0.3-0.8)
  connection: string     // Associated connection ID
  progress: number       // Animation progress (0-1)
  size: number           // Particle radius (0.4-1.2)
  opacity: number        // Transparency (0-0.6)
  id: string             // Unique identifier
  isActive: boolean      // Object pool status
}
```

### Visual Properties

#### Node Rendering
- **Core Color**: `rgba(220, 38, 38, 0.8)` (red-600 with transparency)
- **Glow Gradient**: Radial gradient from red center to transparent edge
- **Size Range**: 0.8px to 2.0px radius
- **Glow Radius**: Node radius + 2px

#### Connection Rendering
- **Color**: `rgba(239, 68, 68, 0.2)` (red-500 with low opacity)
- **Line Width**: 1px
- **Opacity Range**: 0.1 to 0.4 based on distance
- **Max Distance**: 120px

#### Particle Rendering
- **Color**: `rgba(252, 165, 165, opacity)` (red-200 with variable opacity)
- **Size Range**: 0.4px to 1.2px radius
- **Opacity Curve**: `Math.sin(progress * π) * 0.6`

### Device-Responsive Configuration
```typescript
const deviceConfigs = {
  desktop: {
    nodeCount: 120,        // High density for powerful devices
    maxParticles: 50,      // Rich particle effects
    maxConnections: 80,    // Dense network
    connectionDensity: 0.5 // 50% connection probability
  },
  tablet: {
    nodeCount: 80,         // Moderate density
    maxParticles: 35,      // Reduced particles
    maxConnections: 60,    // Fewer connections
    connectionDensity: 0.4 // 40% connection probability
  },
  mobile: {
    nodeCount: 60,         // Optimized for mobile
    maxParticles: 25,      // Minimal particles
    maxConnections: 40,    // Sparse network
    connectionDensity: 0.3 // 30% connection probability
  }
}
```

### Animation Behaviors

#### Floating Motion
- **Base Movement**: Sinusoidal floating with unique phase offsets
- **X Amplitude**: 8px
- **Y Amplitude**: 6px
- **Time Multiplier**: 0.0005 for smooth, slow motion

#### Mouse Interaction
- **Gravity Strength**: 300px radius
- **Gravity Force**: 0.08 attraction coefficient
- **Return Force**: 0.008 restoration to original position
- **Damping**: 0.94 velocity reduction per frame

#### Particle Animation
- **Spawn Rate**: 6% chance per frame
- **Speed Range**: 0.3 to 0.8 units per frame
- **Lifespan**: Full connection traversal (progress 0→1)
- **Movement**: Linear interpolation along connections

## 🎭 Animation Patterns

### Motion Design Principles
1. **Purposeful Movement**: All animations serve functional or emotional purposes
2. **Organic Timing**: Natural easing curves and staggered delays
3. **Performance-First**: 60fps target with efficient rendering
4. **Contextual Feedback**: Animations respond to user interactions

### Transition Patterns

#### Hover Animations
```css
/* Standard hover transition */
transition-all duration-300

/* Enhanced hover with scale and shadow */
hover:scale-105 hover:-translate-y-2 hover:shadow-2xl
transition-all duration-700

/* Color transitions */
hover:text-red-300 transition-colors duration-300
```

#### Loading States
```css
/* Pulse animation for loading */
animate-pulse

/* Ping animation for notifications */
animate-ping

/* Bounce animation for emphasis */
animate-bounce
```

#### Staggered Animations
```typescript
// Staggered delays for list items
style={{ animationDelay: `${index * 0.1}s` }}

// Progressive reveals
opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-200
```

### Micro-Interactions

#### Button States
- **Default**: Solid background with subtle shadow
- **Hover**: Increased shadow, slight scale (1.02x)
- **Active**: Reduced scale (0.98x), darker background
- **Focus**: Ring outline for accessibility

#### Card Interactions
- **Hover**: Scale (1.05x), translate Y (-8px), enhanced shadow
- **Background**: Gradient overlay fade-in
- **Content**: Color shifts and icon animations

## 📝 Typography System

### Font Configuration
```typescript
// Primary font: Inter from Google Fonts
const inter = Inter({ 
  subsets: ["latin"],
  display: 'swap',      // Optimize loading
  preload: true         // Critical font
})
```

### Type Scale
- **Display**: `text-6xl md:text-8xl` (96px/128px) - Hero headlines
- **Heading 1**: `text-5xl md:text-6xl` (48px/60px) - Section titles
- **Heading 2**: `text-3xl` (30px) - Subsection titles
- **Heading 3**: `text-2xl` (24px) - Card titles
- **Heading 4**: `text-xl` (20px) - Feature titles
- **Body Large**: `text-xl md:text-2xl` (20px/24px) - Hero descriptions
- **Body**: `text-base` (16px) - Standard text
- **Small**: `text-sm` (14px) - Secondary text
- **Extra Small**: `text-xs` (12px) - Captions and metadata

### Font Weights
- **Bold**: `font-bold` (700) - Headlines and emphasis
- **Semibold**: `font-semibold` (600) - Subheadings
- **Medium**: `font-medium` (500) - UI elements
- **Regular**: `font-normal` (400) - Body text

### Text Colors
- **Primary**: `text-white` - Main content on dark backgrounds
- **Secondary**: `text-gray-300` - Supporting text
- **Muted**: `text-gray-400` - Metadata and captions
- **Accent**: `text-red-400` - Brand highlights
- **Success**: `text-green-400` - Positive states
- **Warning**: `text-orange-400` - Attention states

## 📐 Spacing System

### Spacing Scale (Tailwind CSS)
```css
/* Base spacing unit: 0.25rem (4px) */
--spacing-1: 0.25rem;    /* 4px */
--spacing-2: 0.5rem;     /* 8px */
--spacing-3: 0.75rem;    /* 12px */
--spacing-4: 1rem;       /* 16px */
--spacing-6: 1.5rem;     /* 24px */
--spacing-8: 2rem;       /* 32px */
--spacing-12: 3rem;      /* 48px */
--spacing-16: 4rem;      /* 64px */
--spacing-20: 5rem;      /* 80px */
--spacing-32: 8rem;      /* 128px */
```

### Layout Patterns
- **Container Max Width**: `max-w-7xl` (1280px)
- **Content Max Width**: `max-w-4xl` (896px) for text content
- **Card Padding**: `p-8` (32px) for feature cards
- **Section Padding**: `py-32` (128px vertical) for major sections
- **Grid Gaps**: `gap-8` (32px) for card grids

### Border Radius System
```css
--radius: 0.625rem;      /* 10px - Base radius */
--radius-sm: 0.375rem;   /* 6px - Small elements */
--radius-md: 0.5rem;     /* 8px - Medium elements */
--radius-lg: 0.625rem;   /* 10px - Large elements */
--radius-xl: 1rem;       /* 16px - Extra large */
```

## 🌙 Dark/Light Theme Implementation

### Theme Provider Setup
```typescript
// Root layout theme configuration
<ThemeProvider 
  attribute="class" 
  defaultTheme="dark" 
  enableSystem 
  disableTransitionOnChange
>
```

### Theme Detection
- **Default**: Dark theme (brand preference)
- **System**: Respects user's OS preference
- **Manual**: User can override system preference
- **Persistence**: Theme choice saved in localStorage

### Theme-Aware Components
```typescript
// CSS variables automatically switch based on .dark class
className="bg-background text-foreground"

// Manual theme-specific styling
className="bg-gray-900 dark:bg-gray-800"
```

### Transition Handling
- **Disabled Transitions**: `disableTransitionOnChange` prevents flash
- **Smooth Switching**: Instant theme changes without animation artifacts
- **Consistent State**: All components update simultaneously

---

*This theme documentation captures the current visual design system and interactive elements of the ConTXT frontend, providing a comprehensive reference for maintaining design consistency.*
