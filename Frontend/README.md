# ConTXT Frontend Documentation

## 🚀 Overview

ConTXT is an AI-powered context engineering platform that transforms unstructured data into intelligent, actionable context for AI tools. The frontend is built with Next.js 15, React 19, and TypeScript, featuring a modern, responsive design with advanced interactive visualizations.

## 🏗️ Architecture

### Tech Stack
- **Framework**: Next.js 15.2.4 with App Router
- **Language**: TypeScript 5
- **UI Framework**: React 19
- **Styling**: Tailwind CSS 4.1.9 with custom design system
- **Component Library**: Radix UI primitives
- **Icons**: Lucide React
- **Animations**: Tailwind CSS Animate + custom animations
- **Theme**: Next Themes with dark/light mode support
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts for data visualization

### Project Structure
```
Frontend/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles & CSS variables
│   ├── layout.tsx         # Root layout with theme provider
│   ├── page.tsx           # Home page (ConTXT Landing)
│   └── pricing/           # Pricing page route
├── components/            # React components
│   ├── ui/               # Reusable UI components (Radix-based)
│   └── theme-provider.tsx # Theme context provider
├── hooks/                # Custom React hooks
│   ├── use-mobile.ts     # Mobile detection hook
│   └── use-toast.ts      # Toast notification system
├── lib/                  # Utility libraries
│   └── utils.ts          # Tailwind class merging utilities
├── public/               # Static assets
├── ui/                   # Additional UI components
├── *.tsx                 # Feature components (landing, pricing, etc.)
└── configuration files   # Next.js, TypeScript, Tailwind configs
```

## 🎨 Design System

### Color Palette
The application uses a sophisticated color system with OKLCH color space for better perceptual uniformity:

**Primary Colors:**
- Red gradient: `from-red-400 via-red-500 to-red-600`
- Background: Dynamic dark/light theme support
- Accent colors: Contextual red variations

**Theme Variables:**
- CSS custom properties for consistent theming
- Dark mode optimized with proper contrast ratios
- Accessible color combinations meeting WCAG standards

### Typography
- **Font**: Inter (Google Fonts)
- **Scale**: Responsive typography with mobile-first approach
- **Weights**: Regular, medium, semibold, bold

### Component Variants
Built with `class-variance-authority` for consistent component APIs:
- Button variants: default, destructive, outline, secondary, ghost, link
- Size variants: sm, default, lg, icon
- Responsive breakpoints: mobile-first design

## 🧩 Key Components

### 1. Landing Page (`contxt-landing.tsx`)
**Purpose**: Main marketing page showcasing ConTXT's capabilities

**Features:**
- Interactive mesh background visualization
- Hero section with animated elements
- Feature showcase with metrics
- Pricing integration
- Email capture form
- Responsive design

**Key Sections:**
- Navigation with brand identity
- Hero with interactive background
- Features grid with icons and metrics
- Stats display
- Call-to-action buttons

### 2. Interactive Mesh Background (`interactive-mesh-background.tsx`)
**Purpose**: Advanced canvas-based visualization demonstrating AI context connections

**Technical Features:**
- Canvas-based particle system
- Mouse interaction with proximity effects
- Spatial grid optimization for performance
- Dynamic particle spawning and movement
- Depth-based sizing and opacity
- Responsive to viewport changes

**Performance Optimizations:**
- RequestAnimationFrame for smooth animations
- Spatial partitioning for collision detection
- Memory management for particles
- Debounced resize handling

### 3. Pricing Page (`pricing-page.tsx`)
**Purpose**: Subscription tiers and feature comparison

**Features:**
- Multiple pricing tiers (Free, Premium, Team, Enterprise)
- Annual/monthly toggle
- Feature comparison matrix
- Security compliance indicators
- Quota and limit displays
- Interactive tooltips

### 4. UI Component System (`ui/`)
**Purpose**: Consistent, accessible component library

**Components Include:**
- Form elements (Button, Input, Select, etc.)
- Layout components (Card, Sheet, Dialog)
- Navigation (Tabs, Breadcrumb, Menu)
- Feedback (Toast, Alert, Progress)
- Data display (Table, Badge, Avatar)

**Design Principles:**
- Accessibility-first with Radix UI primitives
- Consistent API patterns
- TypeScript support with proper typing
- Customizable through CSS variables

## 🔧 Configuration

### Next.js Configuration (`next.config.mjs`)
```javascript
const nextConfig = {
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
  images: { unoptimized: true }
}
```

### TypeScript Configuration (`tsconfig.json`)
- Strict mode enabled
- Path aliases configured (`@/*`)
- Modern ES features support
- Next.js plugin integration

### Tailwind Configuration
- Custom design tokens
- Dark mode support
- Animation utilities
- Component-specific utilities

## 🎯 Features & Functionality

### Core Features
1. **AI Context Engineering**: Transform unstructured data into AI-ready context
2. **Knowledge Graph Intelligence**: Dynamic relationship mapping with 99.9% accuracy
3. **Developer Integration**: APIs, CLI tools, and IDE extensions
4. **Interactive Visualizations**: Real-time mesh network demonstrations
5. **Responsive Design**: Mobile-first, cross-device compatibility

### User Experience
- **Performance**: Optimized loading and interactions
- **Accessibility**: WCAG compliant components
- **Theming**: Dark/light mode with system preference detection
- **Animations**: Smooth, purposeful motion design
- **Feedback**: Toast notifications and loading states

### Business Features
- **Pricing Tiers**: Flexible subscription options
- **Feature Gating**: Role-based access control
- **Analytics Ready**: Structured for tracking and metrics
- **SEO Optimized**: Meta tags and semantic HTML

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 640px
- **Tablet**: 640px - 1024px  
- **Desktop**: > 1024px
- **Large**: > 1280px

### Mobile Optimizations
- Touch-friendly interactions
- Optimized typography scaling
- Simplified navigation patterns
- Performance considerations for mobile devices

## 🔄 State Management

### Approach
- **Local State**: React useState for component-specific state
- **Theme State**: Next Themes for dark/light mode
- **Form State**: React Hook Form for complex forms
- **Toast State**: Custom reducer-based toast system

### Data Flow
- Props drilling for simple component communication
- Context providers for theme and global state
- Custom hooks for reusable stateful logic

## 🚀 Performance Considerations

### Optimization Strategies
- **Code Splitting**: Automatic with Next.js App Router
- **Image Optimization**: Next.js Image component
- **CSS Optimization**: Tailwind CSS purging
- **Bundle Analysis**: Built-in Next.js analyzer

### Monitoring
- **Core Web Vitals**: Optimized for LCP, FID, CLS
- **Performance Budget**: Monitored bundle sizes
- **Accessibility**: Automated testing integration

## 🔐 Security & Best Practices

### Security Measures
- **Input Validation**: Zod schema validation
- **XSS Prevention**: Proper data sanitization
- **CSRF Protection**: Next.js built-in protections
- **Content Security Policy**: Configured headers

### Code Quality
- **TypeScript**: Strict typing throughout
- **ESLint**: Code quality enforcement
- **Component Patterns**: Consistent architecture
- **Error Boundaries**: Graceful error handling

## 📦 Dependencies

### Core Dependencies
- `next`: 15.2.4 - React framework
- `react`: ^19 - UI library
- `typescript`: ^5 - Type safety
- `tailwindcss`: ^4.1.9 - Styling
- `@radix-ui/*`: Accessible primitives
- `lucide-react`: Icon library
- `zod`: Schema validation

### Development Dependencies
- `@types/*`: TypeScript definitions
- `postcss`: CSS processing
- `tw-animate-css`: Animation utilities

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- pnpm (recommended) or npm

### Installation
```bash
cd Frontend
pnpm install
```

### Development
```bash
pnpm dev
```

### Build
```bash
pnpm build
pnpm start
```

### Linting
```bash
pnpm lint
```

## 🔮 Future Enhancements

### Planned Features
- **Dashboard Interface**: User management and analytics
- **Real-time Collaboration**: Multi-user editing capabilities
- **Advanced Visualizations**: 3D knowledge graphs
- **API Integration**: Backend service connections
- **Progressive Web App**: Offline capabilities
- **Internationalization**: Multi-language support

### Technical Improvements
- **Testing Suite**: Unit and integration tests
- **Storybook**: Component documentation
- **Performance Monitoring**: Real-time metrics
- **Accessibility Audit**: Comprehensive compliance
- **Bundle Optimization**: Advanced code splitting

---

*This documentation reflects the current state of the ConTXT frontend codebase and will be updated as the platform evolves.*
