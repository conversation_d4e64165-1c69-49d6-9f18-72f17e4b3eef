/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4a02d526f4c6\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9ob21lL21ld3R3by9Db2RlL0dpdGh1Yi9Db25UWFQvRnJvbnRlbmQvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNGEwMmQ1MjZmNGM2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../theme-provider */ \"(rsc)/./theme-provider.tsx\");\n/* harmony import */ var _components_error_boundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/error-boundary */ \"(rsc)/./components/error-boundary.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"ConTXT - AI-Powered Context Management\",\n    description: \"Revolutionary AI platform for intelligent context management and analysis\",\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_error_boundary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"dark\",\n                    enableSystem: true,\n                    disableTransitionOnChange: true,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contxt_landing_complete__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contxt-landing-complete */ \"(rsc)/./contxt-landing-complete.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contxt_landing_complete__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/app/page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBOEQ7QUFFL0MsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELGdFQUFxQkE7Ozs7O0FBQy9CIiwic291cmNlcyI6WyIvaG9tZS9tZXd0d28vQ29kZS9HaXRodWIvQ29uVFhUL0Zyb250ZW5kL2FwcC9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ29uVFhUTGFuZGluZ0NvbXBsZXRlIGZyb20gXCIuLi9jb250eHQtbGFuZGluZy1jb21wbGV0ZVwiXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiA8Q29uVFhUTGFuZGluZ0NvbXBsZXRlIC8+XG59XG4iXSwibmFtZXMiOlsiQ29uVFhUTGFuZGluZ0NvbXBsZXRlIiwiSG9tZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/error-boundary.tsx":
/*!***************************************!*\
  !*** ./components/error-boundary.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),
/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useErrorHandler = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx",
"useErrorHandler",
);const withErrorBoundary = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call withErrorBoundary() from the server but withErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx",
"withErrorBoundary",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./contxt-landing-complete.tsx":
/*!*************************************!*\
  !*** ./contxt-landing-complete.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?df05\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/home/<USER>/Code/Github/ConTXT/Frontend/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/Code/Github/ConTXT/Frontend/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Code/Github/ConTXT/Frontend/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcomponents%2Ferror-boundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcomponents%2Ferror-boundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/error-boundary.tsx */ \"(rsc)/./components/error-boundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./theme-provider.tsx */ \"(rsc)/./theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcomponents%2Ferror-boundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcontxt-landing-complete.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcontxt-landing-complete.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contxt-landing-complete.tsx */ \"(rsc)/./contxt-landing-complete.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtZXd0d28lMkZDb2RlJTJGR2l0aHViJTJGQ29uVFhUJTJGRnJvbnRlbmQlMkZjb250eHQtbGFuZGluZy1jb21wbGV0ZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBc0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvaG9tZS9tZXd0d28vQ29kZS9HaXRodWIvQ29uVFhUL0Zyb250ZW5kL2NvbnR4dC1sYW5kaW5nLWNvbXBsZXRlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcontxt-landing-complete.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./theme-provider.tsx":
/*!****************************!*\
  !*** ./theme-provider.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Code/Github/ConTXT/Frontend/theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(ssr)/./components/error-boundary.tsx":
/*!***************************************!*\
  !*** ./components/error-boundary.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./components/ui/alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ useErrorHandler,withErrorBoundary,default auto */ \n\n\n\n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props), this.resetError = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined,\n                errorInfo: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n        // Call the onError callback if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // In production, you would send this to your error reporting service\n        if (false) {}\n    }\n    render() {\n        if (this.state.hasError) {\n            const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                error: this.state.error,\n                resetError: this.resetError,\n                errorInfo: this.state.errorInfo\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, resetError, errorInfo }) {\n    const isDevelopment = \"development\" === 'development';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-background p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                    className: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertTitle, {\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                            children: isDevelopment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: error.message\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this),\n                                    error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"cursor-pointer text-sm font-medium\",\n                                                children: \"Stack trace\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"mt-2 text-xs overflow-auto max-h-40 bg-muted p-2 rounded\",\n                                                children: error.stack\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 19\n                                    }, this),\n                                    errorInfo?.componentStack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"cursor-pointer text-sm font-medium\",\n                                                children: \"Component stack\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"mt-2 text-xs overflow-auto max-h-40 bg-muted p-2 rounded\",\n                                                children: errorInfo.componentStack\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"We apologize for the inconvenience. The error has been logged and our team has been notified.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: resetError,\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>window.location.href = '/',\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                isDevelopment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-muted-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This detailed error information is only shown in development mode.\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n// Hook for functional components to handle errors\nfunction useErrorHandler() {\n    return (error, errorInfo)=>{\n        console.error('Error caught by useErrorHandler:', error, errorInfo);\n        if (false) {}\n        throw error // Re-throw to be caught by ErrorBoundary\n        ;\n    };\n}\n// Higher-order component for wrapping components with error boundary\nfunction withErrorBoundary(Component, errorBoundaryProps) {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            ...errorBoundaryProps,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/error-boundary.tsx\",\n            lineNumber: 166,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/error-boundary.tsx\n");

/***/ }),

/***/ "(ssr)/./components/optimized-mesh-background.tsx":
/*!**************************************************!*\
  !*** ./components/optimized-mesh-background.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptimizedMeshBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Optimized constants for better performance\nconst GRAVITY_STRENGTH = 200;\nconst GRAVITY_FORCE = 0.05;\nconst DAMPING = 0.95;\nconst RETURN_FORCE = 0.01;\nconst MAX_CONNECTION_DISTANCE = 100;\nconst FRAME_RATE = 60;\nconst FRAME_INTERVAL = 1000 / FRAME_RATE;\nfunction OptimizedMeshBackground({ className = \"\", enableInteraction = true, particleCount, animationSpeed = 1 }) {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(undefined);\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0,\n        isActive: false\n    });\n    const nodesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const lastFrameTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    // Temporarily remove performance monitoring to fix build\n    const startMeasurement = ()=>{};\n    const endMeasurement = ()=>{};\n    const [dimensions, setDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [deviceType, setDeviceType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"desktop\");\n    // Detect device type and adjust performance accordingly\n    const detectDeviceType = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptimizedMeshBackground.useCallback[detectDeviceType]\": ()=>{\n            const width = window.innerWidth;\n            const userAgent = navigator.userAgent.toLowerCase();\n            const isMobile = /mobile|android|iphone|ipad|tablet/.test(userAgent);\n            if (isMobile || width < 768) return \"mobile\";\n            if (width < 1024) return \"tablet\";\n            return \"desktop\";\n        }\n    }[\"OptimizedMeshBackground.useCallback[detectDeviceType]\"], []);\n    // Get optimized configuration based on device\n    const getDeviceConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptimizedMeshBackground.useCallback[getDeviceConfig]\": ()=>{\n            const configs = {\n                desktop: {\n                    nodeCount: particleCount || 60,\n                    maxConnections: 40,\n                    connectionOpacity: 0.3,\n                    nodeOpacity: 0.8\n                },\n                tablet: {\n                    nodeCount: particleCount || 40,\n                    maxConnections: 25,\n                    connectionOpacity: 0.25,\n                    nodeOpacity: 0.7\n                },\n                mobile: {\n                    nodeCount: particleCount || 25,\n                    maxConnections: 15,\n                    connectionOpacity: 0.2,\n                    nodeOpacity: 0.6\n                }\n            };\n            return configs[deviceType];\n        }\n    }[\"OptimizedMeshBackground.useCallback[getDeviceConfig]\"], [\n        deviceType,\n        particleCount\n    ]);\n    // Initialize nodes with better distribution\n    const initializeNodes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptimizedMeshBackground.useCallback[initializeNodes]\": ()=>{\n            const config = getDeviceConfig();\n            const nodes = [];\n            const { width, height } = dimensions;\n            if (width === 0 || height === 0) return nodes;\n            // Create grid-based distribution for better performance\n            const cols = Math.ceil(Math.sqrt(config.nodeCount * (width / height)));\n            const rows = Math.ceil(config.nodeCount / cols);\n            const cellWidth = width / cols;\n            const cellHeight = height / rows;\n            for(let i = 0; i < config.nodeCount; i++){\n                const col = i % cols;\n                const row = Math.floor(i / cols);\n                // Add some randomness to grid positions\n                const x = col * cellWidth + cellWidth * (0.3 + Math.random() * 0.4);\n                const y = row * cellHeight + cellHeight * (0.3 + Math.random() * 0.4);\n                nodes.push({\n                    x,\n                    y,\n                    originalX: x,\n                    originalY: y,\n                    vx: (Math.random() - 0.5) * 0.5,\n                    vy: (Math.random() - 0.5) * 0.5,\n                    radius: 1 + Math.random() * 2,\n                    opacity: config.nodeOpacity * (0.5 + Math.random() * 0.5)\n                });\n            }\n            return nodes;\n        }\n    }[\"OptimizedMeshBackground.useCallback[initializeNodes]\"], [\n        dimensions,\n        getDeviceConfig\n    ]);\n    // Optimized animation loop with frame rate limiting\n    const animate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptimizedMeshBackground.useCallback[animate]\": (currentTime)=>{\n            if (currentTime - lastFrameTimeRef.current < FRAME_INTERVAL) {\n                animationRef.current = requestAnimationFrame(animate);\n                return;\n            }\n            lastFrameTimeRef.current = currentTime;\n            startMeasurement();\n            const canvas = canvasRef.current;\n            const ctx = canvas?.getContext('2d');\n            if (!canvas || !ctx || !isVisible) {\n                animationRef.current = requestAnimationFrame(animate);\n                return;\n            }\n            const { width, height } = canvas;\n            const config = getDeviceConfig();\n            const nodes = nodesRef.current;\n            // Clear canvas with optimized method\n            ctx.clearRect(0, 0, width, height);\n            // Update node positions with simplified physics\n            const mouse = mouseRef.current;\n            const adjustedAnimationSpeed = animationSpeed * 0.5 // Reduce default speed\n            ;\n            for(let i = 0; i < nodes.length; i++){\n                const node = nodes[i];\n                // Mouse interaction (only if enabled and mouse is active)\n                if (enableInteraction && mouse.isActive) {\n                    const dx = mouse.x - node.x;\n                    const dy = mouse.y - node.y;\n                    const distance = Math.sqrt(dx * dx + dy * dy);\n                    if (distance < GRAVITY_STRENGTH) {\n                        const force = (GRAVITY_STRENGTH - distance) / GRAVITY_STRENGTH;\n                        node.vx += dx / distance * force * GRAVITY_FORCE * adjustedAnimationSpeed;\n                        node.vy += dy / distance * force * GRAVITY_FORCE * adjustedAnimationSpeed;\n                    }\n                }\n                // Return to original position\n                const returnX = (node.originalX - node.x) * RETURN_FORCE * adjustedAnimationSpeed;\n                const returnY = (node.originalY - node.y) * RETURN_FORCE * adjustedAnimationSpeed;\n                node.vx += returnX;\n                node.vy += returnY;\n                // Apply damping\n                node.vx *= DAMPING;\n                node.vy *= DAMPING;\n                // Update position\n                node.x += node.vx;\n                node.y += node.vy;\n                // Boundary constraints\n                if (node.x < 0 || node.x > width) node.vx *= -0.5;\n                if (node.y < 0 || node.y > height) node.vy *= -0.5;\n                node.x = Math.max(0, Math.min(width, node.x));\n                node.y = Math.max(0, Math.min(height, node.y));\n            }\n            // Draw connections (optimized)\n            ctx.strokeStyle = `rgba(239, 68, 68, ${config.connectionOpacity})`;\n            ctx.lineWidth = 0.5;\n            ctx.beginPath();\n            let connectionCount = 0;\n            for(let i = 0; i < nodes.length && connectionCount < config.maxConnections; i++){\n                for(let j = i + 1; j < nodes.length && connectionCount < config.maxConnections; j++){\n                    const dx = nodes[i].x - nodes[j].x;\n                    const dy = nodes[i].y - nodes[j].y;\n                    const distance = Math.sqrt(dx * dx + dy * dy);\n                    if (distance < MAX_CONNECTION_DISTANCE) {\n                        const opacity = (1 - distance / MAX_CONNECTION_DISTANCE) * config.connectionOpacity;\n                        ctx.globalAlpha = opacity;\n                        ctx.moveTo(nodes[i].x, nodes[i].y);\n                        ctx.lineTo(nodes[j].x, nodes[j].y);\n                        connectionCount++;\n                    }\n                }\n            }\n            ctx.stroke();\n            // Draw nodes (batch rendering)\n            ctx.fillStyle = `rgba(239, 68, 68, ${config.nodeOpacity})`;\n            ctx.globalAlpha = 1;\n            for (const node of nodes){\n                ctx.beginPath();\n                ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);\n                ctx.fill();\n            }\n            endMeasurement();\n            animationRef.current = requestAnimationFrame(animate);\n        }\n    }[\"OptimizedMeshBackground.useCallback[animate]\"], [\n        isVisible,\n        enableInteraction,\n        animationSpeed,\n        getDeviceConfig,\n        startMeasurement,\n        endMeasurement\n    ]);\n    // Handle mouse movement (throttled)\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptimizedMeshBackground.useCallback[handleMouseMove]\": (e)=>{\n            if (!enableInteraction) return;\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const rect = canvas.getBoundingClientRect();\n            mouseRef.current = {\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top,\n                isActive: true\n            };\n        }\n    }[\"OptimizedMeshBackground.useCallback[handleMouseMove]\"], [\n        enableInteraction\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptimizedMeshBackground.useCallback[handleMouseLeave]\": ()=>{\n            mouseRef.current.isActive = false;\n        }\n    }[\"OptimizedMeshBackground.useCallback[handleMouseLeave]\"], []);\n    // Resize handler\n    const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OptimizedMeshBackground.useCallback[handleResize]\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const rect = canvas.getBoundingClientRect();\n            const dpr = window.devicePixelRatio || 1;\n            canvas.width = rect.width * dpr;\n            canvas.height = rect.height * dpr;\n            const ctx = canvas.getContext('2d');\n            if (ctx) {\n                ctx.scale(dpr, dpr);\n            }\n            setDimensions({\n                width: rect.width,\n                height: rect.height\n            });\n            setDeviceType(detectDeviceType());\n        }\n    }[\"OptimizedMeshBackground.useCallback[handleResize]\"], [\n        detectDeviceType\n    ]);\n    // Intersection Observer for performance optimization\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptimizedMeshBackground.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const observer = new IntersectionObserver({\n                \"OptimizedMeshBackground.useEffect\": ([entry])=>{\n                    setIsVisible(entry.isIntersecting);\n                }\n            }[\"OptimizedMeshBackground.useEffect\"], {\n                threshold: 0.1\n            });\n            observer.observe(canvas);\n            return ({\n                \"OptimizedMeshBackground.useEffect\": ()=>observer.disconnect()\n            })[\"OptimizedMeshBackground.useEffect\"];\n        }\n    }[\"OptimizedMeshBackground.useEffect\"], []);\n    // Initialize and start animation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptimizedMeshBackground.useEffect\": ()=>{\n            handleResize();\n            nodesRef.current = initializeNodes();\n            window.addEventListener('resize', handleResize);\n            if (enableInteraction) {\n                window.addEventListener('mousemove', handleMouseMove);\n                window.addEventListener('mouseleave', handleMouseLeave);\n            }\n            animationRef.current = requestAnimationFrame(animate);\n            return ({\n                \"OptimizedMeshBackground.useEffect\": ()=>{\n                    window.removeEventListener('resize', handleResize);\n                    if (enableInteraction) {\n                        window.removeEventListener('mousemove', handleMouseMove);\n                        window.removeEventListener('mouseleave', handleMouseLeave);\n                    }\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                }\n            })[\"OptimizedMeshBackground.useEffect\"];\n        }\n    }[\"OptimizedMeshBackground.useEffect\"], [\n        handleResize,\n        initializeNodes,\n        enableInteraction,\n        handleMouseMove,\n        handleMouseLeave,\n        animate\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        className: `absolute inset-0 ${className}`,\n        style: {\n            width: '100%',\n            height: '100%'\n        }\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/optimized-mesh-background.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/optimized-mesh-background.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/alert.tsx":
/*!*********************************!*\
  !*** ./components/ui/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\", {\n    variants: {\n        variant: {\n            default: \"bg-card text-card-foreground\",\n            destructive: \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Alert({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert\",\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/alert.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/alert.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2FsZXJ0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ21DO0FBRWpDO0FBRWhDLE1BQU1HLGdCQUFnQkYsNkRBQUdBLENBQ3ZCLHFPQUNBO0lBQ0VHLFVBQVU7UUFDUkMsU0FBUztZQUNQQyxTQUFTO1lBQ1RDLGFBQ0U7UUFDSjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmSCxTQUFTO0lBQ1g7QUFDRjtBQUdGLFNBQVNJLE1BQU0sRUFDYkMsU0FBUyxFQUNUTCxPQUFPLEVBQ1AsR0FBR00sT0FDOEQ7SUFDakUscUJBQ0UsOERBQUNDO1FBQ0NDLGFBQVU7UUFDVkMsTUFBSztRQUNMSixXQUFXUiw4Q0FBRUEsQ0FBQ0MsY0FBYztZQUFFRTtRQUFRLElBQUlLO1FBQ3pDLEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBRUEsU0FBU0ksV0FBVyxFQUFFTCxTQUFTLEVBQUUsR0FBR0MsT0FBb0M7SUFDdEUscUJBQ0UsOERBQUNDO1FBQ0NDLGFBQVU7UUFDVkgsV0FBV1IsOENBQUVBLENBQ1gsK0RBQ0FRO1FBRUQsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFQSxTQUFTSyxpQkFBaUIsRUFDeEJOLFNBQVMsRUFDVCxHQUFHQyxPQUN5QjtJQUM1QixxQkFDRSw4REFBQ0M7UUFDQ0MsYUFBVTtRQUNWSCxXQUFXUiw4Q0FBRUEsQ0FDWCxrR0FDQVE7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUU4QyIsInNvdXJjZXMiOlsiL2hvbWUvbWV3dHdvL0NvZGUvR2l0aHViL0NvblRYVC9Gcm9udGVuZC9jb21wb25lbnRzL3VpL2FsZXJ0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGFsZXJ0VmFyaWFudHMgPSBjdmEoXG4gIFwicmVsYXRpdmUgdy1mdWxsIHJvdW5kZWQtbGcgYm9yZGVyIHB4LTQgcHktMyB0ZXh0LXNtIGdyaWQgaGFzLVs+c3ZnXTpncmlkLWNvbHMtW2NhbGModmFyKC0tc3BhY2luZykqNClfMWZyXSBncmlkLWNvbHMtWzBfMWZyXSBoYXMtWz5zdmddOmdhcC14LTMgZ2FwLXktMC41IGl0ZW1zLXN0YXJ0IFsmPnN2Z106c2l6ZS00IFsmPnN2Z106dHJhbnNsYXRlLXktMC41IFsmPnN2Z106dGV4dC1jdXJyZW50XCIsXG4gIHtcbiAgICB2YXJpYW50czoge1xuICAgICAgdmFyaWFudDoge1xuICAgICAgICBkZWZhdWx0OiBcImJnLWNhcmQgdGV4dC1jYXJkLWZvcmVncm91bmRcIixcbiAgICAgICAgZGVzdHJ1Y3RpdmU6XG4gICAgICAgICAgXCJ0ZXh0LWRlc3RydWN0aXZlIGJnLWNhcmQgWyY+c3ZnXTp0ZXh0LWN1cnJlbnQgKjpkYXRhLVtzbG90PWFsZXJ0LWRlc2NyaXB0aW9uXTp0ZXh0LWRlc3RydWN0aXZlLzkwXCIsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmZ1bmN0aW9uIEFsZXJ0KHtcbiAgY2xhc3NOYW1lLFxuICB2YXJpYW50LFxuICAuLi5wcm9wc1xufTogUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJkaXZcIj4gJiBWYXJpYW50UHJvcHM8dHlwZW9mIGFsZXJ0VmFyaWFudHM+KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgZGF0YS1zbG90PVwiYWxlcnRcIlxuICAgICAgcm9sZT1cImFsZXJ0XCJcbiAgICAgIGNsYXNzTmFtZT17Y24oYWxlcnRWYXJpYW50cyh7IHZhcmlhbnQgfSksIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5mdW5jdGlvbiBBbGVydFRpdGxlKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9OiBSZWFjdC5Db21wb25lbnRQcm9wczxcImRpdlwiPikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGRhdGEtc2xvdD1cImFsZXJ0LXRpdGxlXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiY29sLXN0YXJ0LTIgbGluZS1jbGFtcC0xIG1pbi1oLTQgZm9udC1tZWRpdW0gdHJhY2tpbmctdGlnaHRcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn1cblxuZnVuY3Rpb24gQWxlcnREZXNjcmlwdGlvbih7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkNvbXBvbmVudFByb3BzPFwiZGl2XCI+KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgZGF0YS1zbG90PVwiYWxlcnQtZGVzY3JpcHRpb25cIlxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgY29sLXN0YXJ0LTIgZ3JpZCBqdXN0aWZ5LWl0ZW1zLXN0YXJ0IGdhcC0xIHRleHQtc20gWyZfcF06bGVhZGluZy1yZWxheGVkXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IEFsZXJ0LCBBbGVydFRpdGxlLCBBbGVydERlc2NyaXB0aW9uIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImN2YSIsImNuIiwiYWxlcnRWYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsImRlZmF1bHRWYXJpYW50cyIsIkFsZXJ0IiwiY2xhc3NOYW1lIiwicHJvcHMiLCJkaXYiLCJkYXRhLXNsb3QiLCJyb2xlIiwiQWxlcnRUaXRsZSIsIkFsZXJ0RGVzY3JpcHRpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.9_react@19.1.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n            destructive: \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"span\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"badge\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.9_react@19.1.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/components/ui/card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./contxt-landing-complete.tsx":
/*!*************************************!*\
  !*** ./contxt-landing-complete.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConTXTLandingComplete)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,Brain,Check,Code,Database,Github,Linkedin,Menu,Shield,Sparkles,Star,Twitter,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_optimized_mesh_background__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/optimized-mesh-background */ \"(ssr)/./components/optimized-mesh-background.tsx\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/validation */ \"(ssr)/./lib/validation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction ConTXTLandingComplete() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAnnual, setIsAnnual] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [emailError, setEmailError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitSuccess, setSubmitSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEmailSubmit = async (e)=>{\n        e.preventDefault();\n        setEmailError(\"\");\n        setIsSubmitting(true);\n        // Sanitize and validate email\n        const sanitizedEmail = (0,_lib_validation__WEBPACK_IMPORTED_MODULE_7__.sanitizeText)(email);\n        const validation = (0,_lib_validation__WEBPACK_IMPORTED_MODULE_7__.validateInput)(_lib_validation__WEBPACK_IMPORTED_MODULE_7__.emailSchema, sanitizedEmail);\n        if (!validation.success) {\n            setEmailError(validation.errors?.[0] || \"Invalid email\");\n            setIsSubmitting(false);\n            return;\n        }\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            console.log(\"Email submitted:\", validation.data);\n            setSubmitSuccess(true);\n            setEmail(\"\");\n            // Reset success message after 3 seconds\n            setTimeout(()=>setSubmitSuccess(false), 3000);\n        } catch (error) {\n            setEmailError(\"Failed to submit email. Please try again.\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const features = [\n        {\n            icon: _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"AI Context Engineering\",\n            description: \"Transform unstructured data into intelligent, actionable context for AI tools like Cursor, Windsurf, and Claude.\",\n            metric: \"10x faster\",\n            badge: \"Core Feature\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: \"Knowledge Graph Intelligence\",\n            description: \"Build dynamic knowledge graphs that understand relationships and provide semantic search capabilities.\",\n            metric: \"99.9% accuracy\",\n            badge: \"Advanced\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Developer-First Integration\",\n            description: \"Seamlessly integrate with your existing workflow through APIs, CLI tools, and IDE extensions.\",\n            metric: \"< 5min setup\",\n            badge: \"Developer Tools\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"Lightning Fast Processing\",\n            description: \"Process millions of documents and code files in seconds with our optimized AI pipeline.\",\n            metric: \"< 100ms\",\n            badge: \"Performance\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Enterprise Security\",\n            description: \"SOC 2 compliant with end-to-end encryption and role-based access control.\",\n            metric: \"99.9% uptime\",\n            badge: \"Enterprise\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Team Collaboration\",\n            description: \"Share knowledge graphs and AI agents across your team with real-time collaboration.\",\n            metric: \"500+ teams\",\n            badge: \"Collaboration\"\n        }\n    ];\n    const stats = [\n        {\n            value: \"50K+\",\n            label: \"Developers\"\n        },\n        {\n            value: \"1M+\",\n            label: \"Context Graphs\"\n        },\n        {\n            value: \"99.9%\",\n            label: \"Uptime\"\n        },\n        {\n            value: \"< 100ms\",\n            label: \"Response Time\"\n        }\n    ];\n    const pricingPlans = [\n        {\n            name: \"Free\",\n            price: \"$0\",\n            period: \"forever\",\n            description: \"Perfect for individual developers and small projects\",\n            features: [\n                \"1,000 API calls/month\",\n                \"Basic context engineering\",\n                \"Community support\",\n                \"Standard knowledge graphs\",\n                \"Basic integrations\"\n            ],\n            cta: \"Get Started Free\",\n            popular: false\n        },\n        {\n            name: \"Pro\",\n            price: isAnnual ? \"$29\" : \"$39\",\n            period: \"month\",\n            description: \"Ideal for professional developers and growing teams\",\n            features: [\n                \"50,000 API calls/month\",\n                \"Advanced AI features\",\n                \"Priority support\",\n                \"Advanced knowledge graphs\",\n                \"All integrations\",\n                \"Team collaboration\",\n                \"Custom AI agents\"\n            ],\n            cta: \"Start Pro Trial\",\n            popular: true\n        },\n        {\n            name: \"Enterprise\",\n            price: \"Custom\",\n            period: \"contact us\",\n            description: \"For large teams and organizations with custom needs\",\n            features: [\n                \"Unlimited API calls\",\n                \"Custom integrations\",\n                \"Dedicated support\",\n                \"On-premise deployment\",\n                \"Advanced security\",\n                \"SLA guarantees\",\n                \"Custom training\"\n            ],\n            cta: \"Contact Sales\",\n            popular: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative min-h-screen overflow-hidden bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_optimized_mesh_background__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"absolute inset-0 z-0\",\n                enableInteraction: true,\n                particleCount: 60,\n                animationSpeed: 0.8\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black/20 z-10\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-md border-b border-gray-800/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between h-20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-red-500/25\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"ConTXT\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex items-center space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#features\",\n                                                    className: \"text-gray-300 hover:text-red-400 transition-colors duration-200\",\n                                                    children: \"Features\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#pricing\",\n                                                    className: \"text-gray-300 hover:text-red-400 transition-colors duration-200\",\n                                                    children: \"Pricing\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#docs\",\n                                                    className: \"text-gray-300 hover:text-red-400 transition-colors duration-200\",\n                                                    children: \"Docs\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#about\",\n                                                    className: \"text-gray-300 hover:text-red-400 transition-colors duration-200\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-800 bg-transparent\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"sm\",\n                                                    className: \"bg-red-600 hover:bg-red-700 shadow-lg shadow-red-600/25\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"md:hidden text-white p-2\",\n                                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden bg-black/95 backdrop-blur-md border-t border-gray-800/50 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#features\",\n                                                className: \"block text-gray-300 hover:text-red-400 transition-colors duration-200 py-2\",\n                                                children: \"Features\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#pricing\",\n                                                className: \"block text-gray-300 hover:text-red-400 transition-colors duration-200 py-2\",\n                                                children: \"Pricing\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#docs\",\n                                                className: \"block text-gray-300 hover:text-red-400 transition-colors duration-200 py-2\",\n                                                children: \"Docs\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#about\",\n                                                className: \"block text-gray-300 hover:text-red-400 transition-colors duration-200 py-2\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pt-4 border-t border-gray-800 space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        className: \"w-full border-gray-600 text-gray-300 hover:bg-gray-800 bg-transparent\",\n                                                        children: \"Sign In\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"w-full bg-red-600 hover:bg-red-700 shadow-lg shadow-red-600/25\",\n                                                        children: \"Get Started\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"flex flex-col justify-center min-h-screen px-6 lg:px-12 pt-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto text-center text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"mb-8 bg-red-500/10 text-red-400 border-red-500/20 backdrop-blur-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Enhanced Interactive AI Context Engineering\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-6xl md:text-8xl font-bold mb-8 leading-tight\",\n                                    children: [\n                                        \"Transform Your Data Into\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-gradient-to-r from-red-400 via-red-500 to-red-600 bg-clip-text\",\n                                            children: \"AI-Ready Context\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed max-w-4xl mx-auto\",\n                                    children: \"Experience the enhanced power of dynamic knowledge graphs with our multi-layered interactive mesh visualization. Move your cursor to see how ConTXT's AI algorithms create intelligent connections with depth and dimension.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 p-4 bg-red-500/10 border border-red-500/20 rounded-lg backdrop-blur-sm max-w-2xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-300 text-sm\",\n                                        children: [\n                                            \"\\uD83D\\uDCA1 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Enhanced Experience:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" Move your mouse to see particles with random movement patterns, depth-based sizing, and evenly distributed spawning across the mesh network - just like ConTXT's intelligent data processing.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8 mb-12\",\n                                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl md:text-4xl font-bold text-red-400 mb-2\",\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-sm uppercase tracking-wide\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-red-600 hover:bg-red-700 text-lg px-10 py-4 shadow-2xl shadow-red-600/25 hover:shadow-red-600/40 transition-all duration-300\",\n                                            children: [\n                                                \"Start Building Context\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"ml-3 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            size: \"lg\",\n                                            className: \"text-lg px-10 py-4 border-gray-600 text-gray-300 hover:bg-gray-800/50 backdrop-blur-sm bg-transparent\",\n                                            children: \"View Interactive Demo\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleEmailSubmit,\n                                    className: \"max-w-lg mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        onChange: (e)=>{\n                                                            setEmail(e.target.value);\n                                                            if (emailError) setEmailError(\"\");\n                                                        },\n                                                        placeholder: \"Enter your email for early access\",\n                                                        className: `flex-1 px-6 py-4 rounded-xl border backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent transition-colors ${emailError ? 'border-red-500 bg-red-900/20 focus:ring-red-500' : 'border-gray-700 bg-gray-900/50 focus:ring-red-500'}`,\n                                                        required: true,\n                                                        disabled: isSubmitting\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"submit\",\n                                                        className: \"bg-red-600 hover:bg-red-700 px-8 shadow-lg shadow-red-600/25 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        disabled: isSubmitting,\n                                                        children: isSubmitting ? \"Joining...\" : \"Join Waitlist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            emailError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                                className: \"border-red-500/20 bg-red-500/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                                        className: \"text-red-300\",\n                                                        children: emailError\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this),\n                                            submitSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                                className: \"border-green-500/20 bg-green-500/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                                        className: \"text-green-300\",\n                                                        children: \"Successfully joined the waitlist! We'll be in touch soon.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm mt-6\",\n                                    children: \"Join 50,000+ developers already using ConTXT to accelerate their AI development workflow.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"features\",\n                        className: \"py-32 px-6 bg-gradient-to-b from-transparent to-gray-900/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"mb-8 bg-red-500/10 text-red-400 border-red-500/20 backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Core Features\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-5xl md:text-6xl font-bold text-white mb-8\",\n                                            children: [\n                                                \"Everything You Need for\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"block text-transparent bg-gradient-to-r from-red-400 via-red-500 to-red-600 bg-clip-text\",\n                                                    children: \"AI-Powered Development\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                            children: \"ConTXT provides a comprehensive suite of tools to transform your development workflow with intelligent context management and AI-powered insights.\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-8 mb-20\",\n                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group relative p-8 rounded-2xl border border-gray-800 bg-gray-900/30 backdrop-blur-sm hover:bg-gray-900/50 hover:border-red-500/30 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 rounded-2xl bg-gradient-to-br from-red-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-red-500/25 group-hover:shadow-red-500/40 transition-shadow duration-300\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                        className: \"w-8 h-8 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"border-red-500/30 text-red-400 bg-red-500/10\",\n                                                                    children: feature.badge\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-white mb-4 group-hover:text-red-300 transition-colors duration-300\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 leading-relaxed mb-6 group-hover:text-gray-200 transition-colors duration-300\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-red-400 font-medium group-hover:text-red-300 transition-colors duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-2\",\n                                                                    children: feature.metric\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 mb-6\",\n                                            children: \"Ready to transform your development workflow?\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"bg-red-600 hover:bg-red-700 px-8 py-3 shadow-lg shadow-red-600/25\",\n                                                    children: \"Start Free Trial\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-800/50 px-8 py-3\",\n                                                    children: \"Schedule Demo\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"pricing\",\n                        className: \"py-32 px-6 bg-gradient-to-b from-gray-900/50 to-black\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"mb-8 bg-red-500/10 text-red-400 border-red-500/20 backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Pricing Plans\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-5xl md:text-6xl font-bold text-white mb-8\",\n                                            children: \"Simple, Transparent Pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                            children: \"Choose the plan that fits your needs. Start free and scale as you grow.\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-4 mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm ${!isAnnual ? 'text-white' : 'text-gray-400'}`,\n                                                    children: \"Monthly\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsAnnual(!isAnnual),\n                                                    className: `relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${isAnnual ? 'bg-red-600' : 'bg-gray-600'}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${isAnnual ? 'translate-x-6' : 'translate-x-1'}`\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm ${isAnnual ? 'text-white' : 'text-gray-400'}`,\n                                                    children: [\n                                                        \"Annual \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            className: \"ml-2 bg-green-600 text-white\",\n                                                            children: \"Save 25%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 26\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-8 max-w-6xl mx-auto\",\n                                    children: pricingPlans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `relative p-8 rounded-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${plan.popular ? 'bg-red-500/10 border-red-500/30 shadow-lg shadow-red-500/20' : 'bg-gray-900/30 border-gray-800 hover:border-red-500/20'}`,\n                                            children: [\n                                                plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        className: \"bg-red-600 text-white px-4 py-1\",\n                                                        children: \"Most Popular\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-white mb-2\",\n                                                            children: plan.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mb-6\",\n                                                            children: plan.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-5xl font-bold text-white\",\n                                                                    children: plan.price\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 ml-1\",\n                                                                    children: [\n                                                                        \"/\",\n                                                                        plan.period\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                isAnnual && plan.name === \"Pro\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-green-400 mt-1\",\n                                                                    children: \"Save $120/year\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-4 mb-8 text-left\",\n                                                            children: plan.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-300\",\n                                                                            children: feature\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, featureIndex, true, {\n                                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            className: `w-full py-3 ${plan.popular ? 'bg-red-600 hover:bg-red-700 shadow-lg shadow-red-600/25' : 'bg-gray-700 hover:bg-gray-600'}`,\n                                                            children: plan.cta\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-20 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-3xl font-bold text-white mb-8\",\n                                            children: \"Enterprise Features\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid md:grid-cols-4 gap-6 max-w-4xl mx-auto\",\n                                            children: [\n                                                \"SSO Integration\",\n                                                \"Advanced Analytics\",\n                                                \"Custom Deployment\",\n                                                \"24/7 Support\",\n                                                \"Data Residency\",\n                                                \"Audit Logs\",\n                                                \"Custom Integrations\",\n                                                \"Training & Onboarding\"\n                                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-gray-900/30 rounded-lg border border-gray-800\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: feature\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-32 px-6 bg-gradient-to-b from-black to-gray-900/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"mb-8 bg-red-500/10 text-red-400 border-red-500/20 backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Testimonials\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-5xl md:text-6xl font-bold text-white mb-8\",\n                                            children: \"Loved by Developers Worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                            children: \"See what developers are saying about ConTXT and how it's transforming their workflow.\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-8\",\n                                    children: [\n                                        {\n                                            name: \"Sarah Chen\",\n                                            role: \"Senior Developer at TechCorp\",\n                                            avatar: \"SC\",\n                                            content: \"ConTXT has revolutionized how we handle context in our AI applications. The knowledge graphs are incredibly powerful and easy to use.\",\n                                            rating: 5\n                                        },\n                                        {\n                                            name: \"Marcus Rodriguez\",\n                                            role: \"AI Engineer at StartupXYZ\",\n                                            avatar: \"MR\",\n                                            content: \"The integration with our existing tools was seamless. We saw immediate improvements in our AI model performance.\",\n                                            rating: 5\n                                        },\n                                        {\n                                            name: \"Emily Johnson\",\n                                            role: \"CTO at InnovateLabs\",\n                                            avatar: \"EJ\",\n                                            content: \"Enterprise-grade security with developer-friendly APIs. ConTXT checks all the boxes for our team.\",\n                                            rating: 5\n                                        }\n                                    ].map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"bg-gray-900/30 border-gray-800 backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mb-4\",\n                                                        children: [\n                                                            ...Array(testimonial.rating)\n                                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-5 h-5 text-yellow-400 fill-current\"\n                                                            }, i, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 italic\",\n                                                        children: [\n                                                            '\"',\n                                                            testimonial.content,\n                                                            '\"'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-red-600 rounded-full flex items-center justify-center text-white font-bold mr-4\",\n                                                                children: testimonial.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-white font-semibold\",\n                                                                        children: testimonial.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: testimonial.role\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                        lineNumber: 587,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-32 px-6 bg-gradient-to-b from-gray-900/50 to-black\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"mb-8 bg-red-500/10 text-red-400 border-red-500/20 backdrop-blur-sm\",\n                                            children: \"FAQ\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-5xl md:text-6xl font-bold text-white mb-8\",\n                                            children: \"Frequently Asked Questions\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        {\n                                            question: \"What is ConTXT and how does it work?\",\n                                            answer: \"ConTXT is an AI-powered context engineering platform that transforms unstructured data into intelligent, queryable knowledge graphs. It integrates with your existing tools and provides APIs for seamless integration.\"\n                                        },\n                                        {\n                                            question: \"How does pricing work?\",\n                                            answer: \"We offer a free tier for individual developers, Pro plans for teams, and custom Enterprise solutions. Pricing is based on API usage and features needed.\"\n                                        },\n                                        {\n                                            question: \"Is my data secure?\",\n                                            answer: \"Yes, we're SOC 2 compliant with end-to-end encryption, role-based access control, and enterprise-grade security measures. Your data is never used to train our models.\"\n                                        },\n                                        {\n                                            question: \"What integrations do you support?\",\n                                            answer: \"We support popular development tools like VS Code, Cursor, Windsurf, and provide REST APIs, CLI tools, and SDKs for custom integrations.\"\n                                        },\n                                        {\n                                            question: \"Can I try ConTXT for free?\",\n                                            answer: \"Yes! We offer a generous free tier with 1,000 API calls per month. No credit card required to get started.\"\n                                        }\n                                    ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                            className: \"bg-gray-900/30 border-gray-800 backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-3\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 leading-relaxed\",\n                                                        children: faq.answer\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"py-20 px-6 bg-black border-t border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-4 gap-8 mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"ConTXT\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 mb-6\",\n                                                    children: \"Transform your data into AI-ready context with the world's most advanced context engineering platform.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"border-gray-600 text-gray-400 hover:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 659,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"border-gray-600 text-gray-400 hover:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"border-gray-600 text-gray-400 hover:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_Brain_Check_Code_Database_Github_Linkedin_Menu_Shield_Sparkles_Star_Twitter_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-white font-semibold mb-4\",\n                                                    children: \"Product\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"Features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"Pricing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"API Docs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"Integrations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-white font-semibold mb-4\",\n                                                    children: \"Company\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"About\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"Blog\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"Careers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"Contact\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-white font-semibold mb-4\",\n                                                    children: \"Support\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"Help Center\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"Community\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: \"Security\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"\\xa9 2024 ConTXT. All rights reserved.\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-6 mt-4 md:mt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gray-400 hover:text-white text-sm transition-colors\",\n                                                    children: \"Cookie Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/contxt-landing-complete.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contxt-landing-complete.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyIvaG9tZS9tZXd0d28vQ29kZS9HaXRodWIvQ29uVFhUL0Zyb250ZW5kL2xpYi91dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./lib/validation.ts":
/*!***************************!*\
  !*** ./lib/validation.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiKeySchema: () => (/* binding */ apiKeySchema),\n/* harmony export */   emailSchema: () => (/* binding */ emailSchema),\n/* harmony export */   envSchema: () => (/* binding */ envSchema),\n/* harmony export */   fileUploadSchema: () => (/* binding */ fileUploadSchema),\n/* harmony export */   loginSchema: () => (/* binding */ loginSchema),\n/* harmony export */   passwordSchema: () => (/* binding */ passwordSchema),\n/* harmony export */   rateLimitSchema: () => (/* binding */ rateLimitSchema),\n/* harmony export */   registrationSchema: () => (/* binding */ registrationSchema),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   sanitizeHtml: () => (/* binding */ sanitizeHtml),\n/* harmony export */   sanitizeText: () => (/* binding */ sanitizeText),\n/* harmony export */   searchQuerySchema: () => (/* binding */ searchQuerySchema),\n/* harmony export */   urlSchema: () => (/* binding */ urlSchema),\n/* harmony export */   userInputSchema: () => (/* binding */ userInputSchema),\n/* harmony export */   validateInput: () => (/* binding */ validateInput)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/.pnpm/zod@3.25.76/node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/.pnpm/zod@3.25.76/node_modules/zod/v3/ZodError.js\");\n\n// Email validation schema\nconst emailSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Email is required').email('Invalid email address').max(254, 'Email too long');\n// Search query validation schema\nconst searchQuerySchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    query: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Query is required').max(1000, 'Query too long').refine((val)=>!/<script|javascript:|data:|vbscript:|onload|onerror/i.test(val), 'Invalid characters detected'),\n    filters: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.string()).optional(),\n    limit: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(1).max(100).default(20),\n    offset: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).default(0)\n});\n// User input validation schema\nconst userInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Name is required').max(100, 'Name too long').regex(/^[a-zA-Z\\s'-]+$/, 'Name contains invalid characters'),\n    email: emailSchema,\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Message is required').max(5000, 'Message too long')\n});\n// Password validation schema\nconst passwordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(8, 'Password must be at least 8 characters').max(128, 'Password too long').regex(/[A-Z]/, 'Password must contain at least one uppercase letter').regex(/[a-z]/, 'Password must contain at least one lowercase letter').regex(/[0-9]/, 'Password must contain at least one number').regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character');\n// Registration schema\nconst registrationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Name is required').max(100, 'Name too long').regex(/^[a-zA-Z\\s'-]+$/, 'Name contains invalid characters'),\n    email: emailSchema,\n    password: passwordSchema,\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.string()\n}).refine((data)=>data.password === data.confirmPassword, {\n    message: \"Passwords don't match\",\n    path: [\n        \"confirmPassword\"\n    ]\n});\n// Login schema\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: emailSchema,\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Password is required')\n});\n// HTML sanitization function (basic implementation)\nconst sanitizeHtml = (input)=>{\n    return input.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '').replace(/<iframe\\b[^<]*(?:(?!<\\/iframe>)<[^<]*)*<\\/iframe>/gi, '').replace(/javascript:/gi, '').replace(/on\\w+\\s*=/gi, '').replace(/<[^>]*>/g, (match)=>{\n        // Allow only safe tags\n        const safeTags = [\n            'b',\n            'i',\n            'em',\n            'strong',\n            'code',\n            'pre',\n            'p',\n            'br'\n        ];\n        const tagMatch = match.match(/<\\/?(\\w+)/);\n        if (tagMatch && safeTags.includes(tagMatch[1].toLowerCase())) {\n            return match;\n        }\n        return '';\n    });\n};\n// Text sanitization for plain text inputs\nconst sanitizeText = (input)=>{\n    return input.replace(/<[^>]*>/g, '') // Remove HTML tags\n    .replace(/[<>'\"&]/g, (match)=>{\n        const entities = {\n            '<': '&lt;',\n            '>': '&gt;',\n            '\"': '&quot;',\n            \"'\": '&#x27;',\n            '&': '&amp;'\n        };\n        return entities[match] || match;\n    }).trim();\n};\n// URL validation\nconst urlSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().url('Invalid URL').refine((url)=>{\n    try {\n        const parsed = new URL(url);\n        return [\n            'http:',\n            'https:'\n        ].includes(parsed.protocol);\n    } catch  {\n        return false;\n    }\n}, 'URL must use HTTP or HTTPS protocol');\n// File upload validation\nconst fileUploadSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'File name is required'),\n    size: zod__WEBPACK_IMPORTED_MODULE_0__.number().max(10 * 1024 * 1024, 'File size must be less than 10MB'),\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.string().refine((type)=>[\n            'application/pdf',\n            'text/plain',\n            'text/markdown',\n            'application/json',\n            'text/csv'\n        ].includes(type), 'File type not supported')\n});\n// Rate limiting validation\nconst rateLimitSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    requests: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(1).max(10000),\n    windowMs: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(1000).max(3600000)\n});\n// API key validation\nconst apiKeySchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(32, 'API key too short').max(128, 'API key too long').regex(/^[a-zA-Z0-9_-]+$/, 'API key contains invalid characters');\n// Validation helper functions\nconst validateInput = (schema, data)=>{\n    try {\n        const result = schema.parse(data);\n        return {\n            success: true,\n            data: result\n        };\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_1__.ZodError) {\n            return {\n                success: false,\n                errors: error.errors.map((err)=>err.message)\n            };\n        }\n        return {\n            success: false,\n            errors: [\n                'Validation failed'\n            ]\n        };\n    }\n};\n// Safe JSON parsing\nconst safeJsonParse = (json)=>{\n    try {\n        const data = JSON.parse(json);\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Invalid JSON'\n        };\n    }\n};\n// Environment variable validation\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'development',\n        'production',\n        'test'\n    ]),\n    NEXT_PUBLIC_API_URL: urlSchema,\n    NEXT_PUBLIC_APP_URL: urlSchema,\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(32),\n    REDIS_URL: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdmFsaWRhdGlvbi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF1QjtBQUV2QiwwQkFBMEI7QUFDbkIsTUFBTUMsY0FBY0QsdUNBQ2xCLEdBQ05HLEdBQUcsQ0FBQyxHQUFHLHFCQUNQQyxLQUFLLENBQUMseUJBQ05DLEdBQUcsQ0FBQyxLQUFLLGtCQUFpQjtBQUU3QixpQ0FBaUM7QUFDMUIsTUFBTUMsb0JBQW9CTix1Q0FBUSxDQUFDO0lBQ3hDUSxPQUFPUix1Q0FDRSxHQUNORyxHQUFHLENBQUMsR0FBRyxxQkFDUEUsR0FBRyxDQUFDLE1BQU0sa0JBQ1ZJLE1BQU0sQ0FDTCxDQUFDQyxNQUFRLENBQUMsc0RBQXNEQyxJQUFJLENBQUNELE1BQ3JFO0lBRUpFLFNBQVNaLHNDQUFPLENBQUNBLHVDQUFRLElBQUljLFFBQVE7SUFDckNDLE9BQU9mLHVDQUFRLEdBQUdHLEdBQUcsQ0FBQyxHQUFHRSxHQUFHLENBQUMsS0FBS1ksT0FBTyxDQUFDO0lBQzFDQyxRQUFRbEIsdUNBQVEsR0FBR0csR0FBRyxDQUFDLEdBQUdjLE9BQU8sQ0FBQztBQUNwQyxHQUFFO0FBRUYsK0JBQStCO0FBQ3hCLE1BQU1FLGtCQUFrQm5CLHVDQUFRLENBQUM7SUFDdENvQixNQUFNcEIsdUNBQ0csR0FDTkcsR0FBRyxDQUFDLEdBQUcsb0JBQ1BFLEdBQUcsQ0FBQyxLQUFLLGlCQUNUZ0IsS0FBSyxDQUFDLG1CQUFtQjtJQUM1QmpCLE9BQU9IO0lBQ1BxQixTQUFTdEIsdUNBQ0EsR0FDTkcsR0FBRyxDQUFDLEdBQUcsdUJBQ1BFLEdBQUcsQ0FBQyxNQUFNO0FBQ2YsR0FBRTtBQUVGLDZCQUE2QjtBQUN0QixNQUFNa0IsaUJBQWlCdkIsdUNBQ3JCLEdBQ05HLEdBQUcsQ0FBQyxHQUFHLDBDQUNQRSxHQUFHLENBQUMsS0FBSyxxQkFDVGdCLEtBQUssQ0FBQyxTQUFTLHVEQUNmQSxLQUFLLENBQUMsU0FBUyx1REFDZkEsS0FBSyxDQUFDLFNBQVMsNkNBQ2ZBLEtBQUssQ0FBQyxnQkFBZ0Isd0RBQXVEO0FBRWhGLHNCQUFzQjtBQUNmLE1BQU1HLHFCQUFxQnhCLHVDQUFRLENBQUM7SUFDekNvQixNQUFNcEIsdUNBQ0csR0FDTkcsR0FBRyxDQUFDLEdBQUcsb0JBQ1BFLEdBQUcsQ0FBQyxLQUFLLGlCQUNUZ0IsS0FBSyxDQUFDLG1CQUFtQjtJQUM1QmpCLE9BQU9IO0lBQ1B3QixVQUFVRjtJQUNWRyxpQkFBaUIxQix1Q0FBUTtBQUMzQixHQUFHUyxNQUFNLENBQUMsQ0FBQ2tCLE9BQVNBLEtBQUtGLFFBQVEsS0FBS0UsS0FBS0QsZUFBZSxFQUFFO0lBQzFESixTQUFTO0lBQ1RNLE1BQU07UUFBQztLQUFrQjtBQUMzQixHQUFFO0FBRUYsZUFBZTtBQUNSLE1BQU1DLGNBQWM3Qix1Q0FBUSxDQUFDO0lBQ2xDSSxPQUFPSDtJQUNQd0IsVUFBVXpCLHVDQUFRLEdBQUdHLEdBQUcsQ0FBQyxHQUFHO0FBQzlCLEdBQUU7QUFFRixvREFBb0Q7QUFDN0MsTUFBTTJCLGVBQWUsQ0FBQ0M7SUFDM0IsT0FBT0EsTUFDSkMsT0FBTyxDQUFDLHVEQUF1RCxJQUMvREEsT0FBTyxDQUFDLHVEQUF1RCxJQUMvREEsT0FBTyxDQUFDLGlCQUFpQixJQUN6QkEsT0FBTyxDQUFDLGVBQWUsSUFDdkJBLE9BQU8sQ0FBQyxZQUFZLENBQUNDO1FBQ3BCLHVCQUF1QjtRQUN2QixNQUFNQyxXQUFXO1lBQUM7WUFBSztZQUFLO1lBQU07WUFBVTtZQUFRO1lBQU87WUFBSztTQUFLO1FBQ3JFLE1BQU1DLFdBQVdGLE1BQU1BLEtBQUssQ0FBQztRQUM3QixJQUFJRSxZQUFZRCxTQUFTRSxRQUFRLENBQUNELFFBQVEsQ0FBQyxFQUFFLENBQUNFLFdBQVcsS0FBSztZQUM1RCxPQUFPSjtRQUNUO1FBQ0EsT0FBTztJQUNUO0FBQ0osRUFBQztBQUVELDBDQUEwQztBQUNuQyxNQUFNSyxlQUFlLENBQUNQO0lBQzNCLE9BQU9BLE1BQ0pDLE9BQU8sQ0FBQyxZQUFZLElBQUksbUJBQW1CO0tBQzNDQSxPQUFPLENBQUMsWUFBWSxDQUFDQztRQUNwQixNQUFNTSxXQUFtQztZQUN2QyxLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO1lBQ0wsS0FBSztRQUNQO1FBQ0EsT0FBT0EsUUFBUSxDQUFDTixNQUFNLElBQUlBO0lBQzVCLEdBQ0NPLElBQUk7QUFDVCxFQUFDO0FBRUQsaUJBQWlCO0FBQ1YsTUFBTUMsWUFBWXpDLHVDQUNoQixHQUNOMEMsR0FBRyxDQUFDLGVBQ0pqQyxNQUFNLENBQUMsQ0FBQ2lDO0lBQ1AsSUFBSTtRQUNGLE1BQU1DLFNBQVMsSUFBSUMsSUFBSUY7UUFDdkIsT0FBTztZQUFDO1lBQVM7U0FBUyxDQUFDTixRQUFRLENBQUNPLE9BQU9FLFFBQVE7SUFDckQsRUFBRSxPQUFNO1FBQ04sT0FBTztJQUNUO0FBQ0YsR0FBRyx1Q0FBc0M7QUFFM0MseUJBQXlCO0FBQ2xCLE1BQU1DLG1CQUFtQjlDLHVDQUFRLENBQUM7SUFDdkNvQixNQUFNcEIsdUNBQVEsR0FBR0csR0FBRyxDQUFDLEdBQUc7SUFDeEI0QyxNQUFNL0MsdUNBQVEsR0FBR0ssR0FBRyxDQUFDLEtBQUssT0FBTyxNQUFNO0lBQ3ZDMkMsTUFBTWhELHVDQUFRLEdBQUdTLE1BQU0sQ0FDckIsQ0FBQ3VDLE9BQVM7WUFDUjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0QsQ0FBQ1osUUFBUSxDQUFDWSxPQUNYO0FBRUosR0FBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNQyxrQkFBa0JqRCx1Q0FBUSxDQUFDO0lBQ3RDa0QsVUFBVWxELHVDQUFRLEdBQUdHLEdBQUcsQ0FBQyxHQUFHRSxHQUFHLENBQUM7SUFDaEM4QyxVQUFVbkQsdUNBQVEsR0FBR0csR0FBRyxDQUFDLE1BQU1FLEdBQUcsQ0FBQztBQUNyQyxHQUFFO0FBRUYscUJBQXFCO0FBQ2QsTUFBTStDLGVBQWVwRCx1Q0FDbkIsR0FDTkcsR0FBRyxDQUFDLElBQUkscUJBQ1JFLEdBQUcsQ0FBQyxLQUFLLG9CQUNUZ0IsS0FBSyxDQUFDLG9CQUFvQix1Q0FBc0M7QUFFbkUsOEJBQThCO0FBQ3ZCLE1BQU1nQyxnQkFBZ0IsQ0FBSUMsUUFBd0IzQjtJQUt2RCxJQUFJO1FBQ0YsTUFBTTRCLFNBQVNELE9BQU9FLEtBQUssQ0FBQzdCO1FBQzVCLE9BQU87WUFBRThCLFNBQVM7WUFBTTlCLE1BQU00QjtRQUFPO0lBQ3ZDLEVBQUUsT0FBT0csT0FBTztRQUNkLElBQUlBLGlCQUFpQjFELHlDQUFVLEVBQUU7WUFDL0IsT0FBTztnQkFDTHlELFNBQVM7Z0JBQ1RHLFFBQVFGLE1BQU1FLE1BQU0sQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLE1BQVFBLElBQUl4QyxPQUFPO1lBQy9DO1FBQ0Y7UUFDQSxPQUFPO1lBQ0xtQyxTQUFTO1lBQ1RHLFFBQVE7Z0JBQUM7YUFBb0I7UUFDL0I7SUFDRjtBQUNGLEVBQUM7QUFFRCxvQkFBb0I7QUFDYixNQUFNRyxnQkFBZ0IsQ0FBSUM7SUFLL0IsSUFBSTtRQUNGLE1BQU1yQyxPQUFPc0MsS0FBS1QsS0FBSyxDQUFDUTtRQUN4QixPQUFPO1lBQUVQLFNBQVM7WUFBTTlCO1FBQUs7SUFDL0IsRUFBRSxPQUFPK0IsT0FBTztRQUNkLE9BQU87WUFDTEQsU0FBUztZQUNUQyxPQUFPQSxpQkFBaUJRLFFBQVFSLE1BQU1wQyxPQUFPLEdBQUc7UUFDbEQ7SUFDRjtBQUNGLEVBQUM7QUFFRCxrQ0FBa0M7QUFDM0IsTUFBTTZDLFlBQVluRSx1Q0FBUSxDQUFDO0lBQ2hDb0UsVUFBVXBFLHdDQUFNLENBQUM7UUFBQztRQUFlO1FBQWM7S0FBTztJQUN0RHNFLHFCQUFxQjdCO0lBQ3JCOEIscUJBQXFCOUI7SUFDckIrQixjQUFjeEUsdUNBQVEsR0FBR0csR0FBRyxDQUFDO0lBQzdCc0UsWUFBWXpFLHVDQUFRLEdBQUdHLEdBQUcsQ0FBQztJQUMzQnVFLFdBQVcxRSx1Q0FBUSxHQUFHYyxRQUFRO0FBQ2hDLEdBQUUiLCJzb3VyY2VzIjpbIi9ob21lL21ld3R3by9Db2RlL0dpdGh1Yi9Db25UWFQvRnJvbnRlbmQvbGliL3ZhbGlkYXRpb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgeiB9IGZyb20gJ3pvZCdcblxuLy8gRW1haWwgdmFsaWRhdGlvbiBzY2hlbWFcbmV4cG9ydCBjb25zdCBlbWFpbFNjaGVtYSA9IHpcbiAgLnN0cmluZygpXG4gIC5taW4oMSwgJ0VtYWlsIGlzIHJlcXVpcmVkJylcbiAgLmVtYWlsKCdJbnZhbGlkIGVtYWlsIGFkZHJlc3MnKVxuICAubWF4KDI1NCwgJ0VtYWlsIHRvbyBsb25nJylcblxuLy8gU2VhcmNoIHF1ZXJ5IHZhbGlkYXRpb24gc2NoZW1hXG5leHBvcnQgY29uc3Qgc2VhcmNoUXVlcnlTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIHF1ZXJ5OiB6XG4gICAgLnN0cmluZygpXG4gICAgLm1pbigxLCAnUXVlcnkgaXMgcmVxdWlyZWQnKVxuICAgIC5tYXgoMTAwMCwgJ1F1ZXJ5IHRvbyBsb25nJylcbiAgICAucmVmaW5lKFxuICAgICAgKHZhbCkgPT4gIS88c2NyaXB0fGphdmFzY3JpcHQ6fGRhdGE6fHZic2NyaXB0OnxvbmxvYWR8b25lcnJvci9pLnRlc3QodmFsKSxcbiAgICAgICdJbnZhbGlkIGNoYXJhY3RlcnMgZGV0ZWN0ZWQnXG4gICAgKSxcbiAgZmlsdGVyczogei5hcnJheSh6LnN0cmluZygpKS5vcHRpb25hbCgpLFxuICBsaW1pdDogei5udW1iZXIoKS5taW4oMSkubWF4KDEwMCkuZGVmYXVsdCgyMCksXG4gIG9mZnNldDogei5udW1iZXIoKS5taW4oMCkuZGVmYXVsdCgwKSxcbn0pXG5cbi8vIFVzZXIgaW5wdXQgdmFsaWRhdGlvbiBzY2hlbWFcbmV4cG9ydCBjb25zdCB1c2VySW5wdXRTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIG5hbWU6IHpcbiAgICAuc3RyaW5nKClcbiAgICAubWluKDEsICdOYW1lIGlzIHJlcXVpcmVkJylcbiAgICAubWF4KDEwMCwgJ05hbWUgdG9vIGxvbmcnKVxuICAgIC5yZWdleCgvXlthLXpBLVpcXHMnLV0rJC8sICdOYW1lIGNvbnRhaW5zIGludmFsaWQgY2hhcmFjdGVycycpLFxuICBlbWFpbDogZW1haWxTY2hlbWEsXG4gIG1lc3NhZ2U6IHpcbiAgICAuc3RyaW5nKClcbiAgICAubWluKDEsICdNZXNzYWdlIGlzIHJlcXVpcmVkJylcbiAgICAubWF4KDUwMDAsICdNZXNzYWdlIHRvbyBsb25nJyksXG59KVxuXG4vLyBQYXNzd29yZCB2YWxpZGF0aW9uIHNjaGVtYVxuZXhwb3J0IGNvbnN0IHBhc3N3b3JkU2NoZW1hID0gelxuICAuc3RyaW5nKClcbiAgLm1pbig4LCAnUGFzc3dvcmQgbXVzdCBiZSBhdCBsZWFzdCA4IGNoYXJhY3RlcnMnKVxuICAubWF4KDEyOCwgJ1Bhc3N3b3JkIHRvbyBsb25nJylcbiAgLnJlZ2V4KC9bQS1aXS8sICdQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIHVwcGVyY2FzZSBsZXR0ZXInKVxuICAucmVnZXgoL1thLXpdLywgJ1Bhc3N3b3JkIG11c3QgY29udGFpbiBhdCBsZWFzdCBvbmUgbG93ZXJjYXNlIGxldHRlcicpXG4gIC5yZWdleCgvWzAtOV0vLCAnUGFzc3dvcmQgbXVzdCBjb250YWluIGF0IGxlYXN0IG9uZSBudW1iZXInKVxuICAucmVnZXgoL1teQS1aYS16MC05XS8sICdQYXNzd29yZCBtdXN0IGNvbnRhaW4gYXQgbGVhc3Qgb25lIHNwZWNpYWwgY2hhcmFjdGVyJylcblxuLy8gUmVnaXN0cmF0aW9uIHNjaGVtYVxuZXhwb3J0IGNvbnN0IHJlZ2lzdHJhdGlvblNjaGVtYSA9IHoub2JqZWN0KHtcbiAgbmFtZTogelxuICAgIC5zdHJpbmcoKVxuICAgIC5taW4oMSwgJ05hbWUgaXMgcmVxdWlyZWQnKVxuICAgIC5tYXgoMTAwLCAnTmFtZSB0b28gbG9uZycpXG4gICAgLnJlZ2V4KC9eW2EtekEtWlxccyctXSskLywgJ05hbWUgY29udGFpbnMgaW52YWxpZCBjaGFyYWN0ZXJzJyksXG4gIGVtYWlsOiBlbWFpbFNjaGVtYSxcbiAgcGFzc3dvcmQ6IHBhc3N3b3JkU2NoZW1hLFxuICBjb25maXJtUGFzc3dvcmQ6IHouc3RyaW5nKCksXG59KS5yZWZpbmUoKGRhdGEpID0+IGRhdGEucGFzc3dvcmQgPT09IGRhdGEuY29uZmlybVBhc3N3b3JkLCB7XG4gIG1lc3NhZ2U6IFwiUGFzc3dvcmRzIGRvbid0IG1hdGNoXCIsXG4gIHBhdGg6IFtcImNvbmZpcm1QYXNzd29yZFwiXSxcbn0pXG5cbi8vIExvZ2luIHNjaGVtYVxuZXhwb3J0IGNvbnN0IGxvZ2luU2NoZW1hID0gei5vYmplY3Qoe1xuICBlbWFpbDogZW1haWxTY2hlbWEsXG4gIHBhc3N3b3JkOiB6LnN0cmluZygpLm1pbigxLCAnUGFzc3dvcmQgaXMgcmVxdWlyZWQnKSxcbn0pXG5cbi8vIEhUTUwgc2FuaXRpemF0aW9uIGZ1bmN0aW9uIChiYXNpYyBpbXBsZW1lbnRhdGlvbilcbmV4cG9ydCBjb25zdCBzYW5pdGl6ZUh0bWwgPSAoaW5wdXQ6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gIHJldHVybiBpbnB1dFxuICAgIC5yZXBsYWNlKC88c2NyaXB0XFxiW148XSooPzooPyE8XFwvc2NyaXB0Pik8W148XSopKjxcXC9zY3JpcHQ+L2dpLCAnJylcbiAgICAucmVwbGFjZSgvPGlmcmFtZVxcYltePF0qKD86KD8hPFxcL2lmcmFtZT4pPFtePF0qKSo8XFwvaWZyYW1lPi9naSwgJycpXG4gICAgLnJlcGxhY2UoL2phdmFzY3JpcHQ6L2dpLCAnJylcbiAgICAucmVwbGFjZSgvb25cXHcrXFxzKj0vZ2ksICcnKVxuICAgIC5yZXBsYWNlKC88W14+XSo+L2csIChtYXRjaCkgPT4ge1xuICAgICAgLy8gQWxsb3cgb25seSBzYWZlIHRhZ3NcbiAgICAgIGNvbnN0IHNhZmVUYWdzID0gWydiJywgJ2knLCAnZW0nLCAnc3Ryb25nJywgJ2NvZGUnLCAncHJlJywgJ3AnLCAnYnInXVxuICAgICAgY29uc3QgdGFnTWF0Y2ggPSBtYXRjaC5tYXRjaCgvPFxcLz8oXFx3KykvKVxuICAgICAgaWYgKHRhZ01hdGNoICYmIHNhZmVUYWdzLmluY2x1ZGVzKHRhZ01hdGNoWzFdLnRvTG93ZXJDYXNlKCkpKSB7XG4gICAgICAgIHJldHVybiBtYXRjaFxuICAgICAgfVxuICAgICAgcmV0dXJuICcnXG4gICAgfSlcbn1cblxuLy8gVGV4dCBzYW5pdGl6YXRpb24gZm9yIHBsYWluIHRleHQgaW5wdXRzXG5leHBvcnQgY29uc3Qgc2FuaXRpemVUZXh0ID0gKGlucHV0OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICByZXR1cm4gaW5wdXRcbiAgICAucmVwbGFjZSgvPFtePl0qPi9nLCAnJykgLy8gUmVtb3ZlIEhUTUwgdGFnc1xuICAgIC5yZXBsYWNlKC9bPD4nXCImXS9nLCAobWF0Y2gpID0+IHtcbiAgICAgIGNvbnN0IGVudGl0aWVzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge1xuICAgICAgICAnPCc6ICcmbHQ7JyxcbiAgICAgICAgJz4nOiAnJmd0OycsXG4gICAgICAgICdcIic6ICcmcXVvdDsnLFxuICAgICAgICBcIidcIjogJyYjeDI3OycsXG4gICAgICAgICcmJzogJyZhbXA7JyxcbiAgICAgIH1cbiAgICAgIHJldHVybiBlbnRpdGllc1ttYXRjaF0gfHwgbWF0Y2hcbiAgICB9KVxuICAgIC50cmltKClcbn1cblxuLy8gVVJMIHZhbGlkYXRpb25cbmV4cG9ydCBjb25zdCB1cmxTY2hlbWEgPSB6XG4gIC5zdHJpbmcoKVxuICAudXJsKCdJbnZhbGlkIFVSTCcpXG4gIC5yZWZpbmUoKHVybCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwYXJzZWQgPSBuZXcgVVJMKHVybClcbiAgICAgIHJldHVybiBbJ2h0dHA6JywgJ2h0dHBzOiddLmluY2x1ZGVzKHBhcnNlZC5wcm90b2NvbClcbiAgICB9IGNhdGNoIHtcbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH1cbiAgfSwgJ1VSTCBtdXN0IHVzZSBIVFRQIG9yIEhUVFBTIHByb3RvY29sJylcblxuLy8gRmlsZSB1cGxvYWQgdmFsaWRhdGlvblxuZXhwb3J0IGNvbnN0IGZpbGVVcGxvYWRTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIG5hbWU6IHouc3RyaW5nKCkubWluKDEsICdGaWxlIG5hbWUgaXMgcmVxdWlyZWQnKSxcbiAgc2l6ZTogei5udW1iZXIoKS5tYXgoMTAgKiAxMDI0ICogMTAyNCwgJ0ZpbGUgc2l6ZSBtdXN0IGJlIGxlc3MgdGhhbiAxME1CJyksXG4gIHR5cGU6IHouc3RyaW5nKCkucmVmaW5lKFxuICAgICh0eXBlKSA9PiBbXG4gICAgICAnYXBwbGljYXRpb24vcGRmJyxcbiAgICAgICd0ZXh0L3BsYWluJyxcbiAgICAgICd0ZXh0L21hcmtkb3duJyxcbiAgICAgICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICd0ZXh0L2NzdicsXG4gICAgXS5pbmNsdWRlcyh0eXBlKSxcbiAgICAnRmlsZSB0eXBlIG5vdCBzdXBwb3J0ZWQnXG4gICksXG59KVxuXG4vLyBSYXRlIGxpbWl0aW5nIHZhbGlkYXRpb25cbmV4cG9ydCBjb25zdCByYXRlTGltaXRTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIHJlcXVlc3RzOiB6Lm51bWJlcigpLm1pbigxKS5tYXgoMTAwMDApLFxuICB3aW5kb3dNczogei5udW1iZXIoKS5taW4oMTAwMCkubWF4KDM2MDAwMDApLCAvLyAxIHNlY29uZCB0byAxIGhvdXJcbn0pXG5cbi8vIEFQSSBrZXkgdmFsaWRhdGlvblxuZXhwb3J0IGNvbnN0IGFwaUtleVNjaGVtYSA9IHpcbiAgLnN0cmluZygpXG4gIC5taW4oMzIsICdBUEkga2V5IHRvbyBzaG9ydCcpXG4gIC5tYXgoMTI4LCAnQVBJIGtleSB0b28gbG9uZycpXG4gIC5yZWdleCgvXlthLXpBLVowLTlfLV0rJC8sICdBUEkga2V5IGNvbnRhaW5zIGludmFsaWQgY2hhcmFjdGVycycpXG5cbi8vIFZhbGlkYXRpb24gaGVscGVyIGZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IHZhbGlkYXRlSW5wdXQgPSA8VD4oc2NoZW1hOiB6LlpvZFNjaGVtYTxUPiwgZGF0YTogdW5rbm93bik6IHtcbiAgc3VjY2VzczogYm9vbGVhblxuICBkYXRhPzogVFxuICBlcnJvcnM/OiBzdHJpbmdbXVxufSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gc2NoZW1hLnBhcnNlKGRhdGEpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogcmVzdWx0IH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiB6LlpvZEVycm9yKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3JzOiBlcnJvci5lcnJvcnMubWFwKChlcnIpID0+IGVyci5tZXNzYWdlKSxcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgZXJyb3JzOiBbJ1ZhbGlkYXRpb24gZmFpbGVkJ10sXG4gICAgfVxuICB9XG59XG5cbi8vIFNhZmUgSlNPTiBwYXJzaW5nXG5leHBvcnQgY29uc3Qgc2FmZUpzb25QYXJzZSA9IDxUPihqc29uOiBzdHJpbmcpOiB7XG4gIHN1Y2Nlc3M6IGJvb2xlYW5cbiAgZGF0YT86IFRcbiAgZXJyb3I/OiBzdHJpbmdcbn0gPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IGRhdGEgPSBKU09OLnBhcnNlKGpzb24pXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0ludmFsaWQgSlNPTicsXG4gICAgfVxuICB9XG59XG5cbi8vIEVudmlyb25tZW50IHZhcmlhYmxlIHZhbGlkYXRpb25cbmV4cG9ydCBjb25zdCBlbnZTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIE5PREVfRU5WOiB6LmVudW0oWydkZXZlbG9wbWVudCcsICdwcm9kdWN0aW9uJywgJ3Rlc3QnXSksXG4gIE5FWFRfUFVCTElDX0FQSV9VUkw6IHVybFNjaGVtYSxcbiAgTkVYVF9QVUJMSUNfQVBQX1VSTDogdXJsU2NoZW1hLFxuICBEQVRBQkFTRV9VUkw6IHouc3RyaW5nKCkubWluKDEpLFxuICBKV1RfU0VDUkVUOiB6LnN0cmluZygpLm1pbigzMiksXG4gIFJFRElTX1VSTDogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxufSlcblxuZXhwb3J0IHR5cGUgRW52Q29uZmlnID0gei5pbmZlcjx0eXBlb2YgZW52U2NoZW1hPlxuIl0sIm5hbWVzIjpbInoiLCJlbWFpbFNjaGVtYSIsInN0cmluZyIsIm1pbiIsImVtYWlsIiwibWF4Iiwic2VhcmNoUXVlcnlTY2hlbWEiLCJvYmplY3QiLCJxdWVyeSIsInJlZmluZSIsInZhbCIsInRlc3QiLCJmaWx0ZXJzIiwiYXJyYXkiLCJvcHRpb25hbCIsImxpbWl0IiwibnVtYmVyIiwiZGVmYXVsdCIsIm9mZnNldCIsInVzZXJJbnB1dFNjaGVtYSIsIm5hbWUiLCJyZWdleCIsIm1lc3NhZ2UiLCJwYXNzd29yZFNjaGVtYSIsInJlZ2lzdHJhdGlvblNjaGVtYSIsInBhc3N3b3JkIiwiY29uZmlybVBhc3N3b3JkIiwiZGF0YSIsInBhdGgiLCJsb2dpblNjaGVtYSIsInNhbml0aXplSHRtbCIsImlucHV0IiwicmVwbGFjZSIsIm1hdGNoIiwic2FmZVRhZ3MiLCJ0YWdNYXRjaCIsImluY2x1ZGVzIiwidG9Mb3dlckNhc2UiLCJzYW5pdGl6ZVRleHQiLCJlbnRpdGllcyIsInRyaW0iLCJ1cmxTY2hlbWEiLCJ1cmwiLCJwYXJzZWQiLCJVUkwiLCJwcm90b2NvbCIsImZpbGVVcGxvYWRTY2hlbWEiLCJzaXplIiwidHlwZSIsInJhdGVMaW1pdFNjaGVtYSIsInJlcXVlc3RzIiwid2luZG93TXMiLCJhcGlLZXlTY2hlbWEiLCJ2YWxpZGF0ZUlucHV0Iiwic2NoZW1hIiwicmVzdWx0IiwicGFyc2UiLCJzdWNjZXNzIiwiZXJyb3IiLCJab2RFcnJvciIsImVycm9ycyIsIm1hcCIsImVyciIsInNhZmVKc29uUGFyc2UiLCJqc29uIiwiSlNPTiIsIkVycm9yIiwiZW52U2NoZW1hIiwiTk9ERV9FTlYiLCJlbnVtIiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsIk5FWFRfUFVCTElDX0FQUF9VUkwiLCJEQVRBQkFTRV9VUkwiLCJKV1RfU0VDUkVUIiwiUkVESVNfVVJMIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/validation.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcomponents%2Ferror-boundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcomponents%2Ferror-boundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/error-boundary.tsx */ \"(ssr)/./components/error-boundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./theme-provider.tsx */ \"(ssr)/./theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcomponents%2Ferror-boundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcontxt-landing-complete.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcontxt-landing-complete.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contxt-landing-complete.tsx */ \"(ssr)/./contxt-landing-complete.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZtZXd0d28lMkZDb2RlJTJGR2l0aHViJTJGQ29uVFhUJTJGRnJvbnRlbmQlMkZjb250eHQtbGFuZGluZy1jb21wbGV0ZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBc0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvaG9tZS9tZXd0d28vQ29kZS9HaXRodWIvQ29uVFhUL0Zyb250ZW5kL2NvbnR4dC1sYW5kaW5nLWNvbXBsZXRlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fcontxt-landing-complete.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fnode_modules%2F.pnpm%2Fnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./theme-provider.tsx":
/*!****************************!*\
  !*** ./theme-provider.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/.pnpm/next-themes@0.4.6_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/Github/ConTXT/Frontend/theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi90aGVtZS1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU4QjtBQUlWO0FBRWIsU0FBU0MsY0FBYyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkI7SUFDdEUscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDIiwic291cmNlcyI6WyIvaG9tZS9tZXd0d28vQ29kZS9HaXRodWIvQ29uVFhUL0Zyb250ZW5kL3RoZW1lLXByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQge1xuICBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlcixcbiAgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMsXG59IGZyb20gJ25leHQtdGhlbWVzJ1xuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./theme-provider.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1","vendor-chunks/lucide-react@0.454.0_react@19.1.1","vendor-chunks/zod@3.25.76","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/tailwind-merge@2.6.0","vendor-chunks/next-themes@0.4.6_react-dom@19.1.1_react@19.1.1__react@19.1.1","vendor-chunks/clsx@2.1.1","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/@radix-ui+react-slot@1.2.3_@types+react@19.1.9_react@19.1.1","vendor-chunks/@radix-ui+react-compose-refs@1.1.2_@types+react@19.1.9_react@19.1.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmewtwo%2FCode%2FGithub%2FConTXT%2FFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();