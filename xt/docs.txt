--- docs/MULTI_PROVIDER_LLM.md.txt ---
# Multi-Provider LLM Integration

This document describes the multi-provider LLM integration system implemented in the ConTXT backend. The system provides a unified interface for multiple LLM providers, intelligent provider selection, automatic fallback mechanisms, and cost optimization strategies.

## Overview

The multi-provider LLM system integrates multiple LLM providers through LangChain, providing:

- **Unified Interface**: Consistent API for all providers
- **Intelligent Provider Selection**: Selects optimal provider based on task type, priority, and user tier
- **Automatic Fallback**: Falls back to alternative providers if the primary provider fails
- **Cost Optimization**: Optimizes provider selection based on cost efficiency
- **Security**: Secure credential management
- **Monitoring**: Provider health monitoring

## Supported Providers

The system supports the following LLM providers:

- **OpenRouter** - Primary unified API with access to 100+ models
- **XAI (Grok)** - Direct Grok API integration
- **OpenAI** - GPT models with embedding support
- **Anthropic** - Claude models
- **Google AI** - Gemini models with embedding support
- **Groq** - Ultra-fast inference
- **Mistral** - European AI models
- **Together AI** - Open source models
- **Perplexity** - Search-augmented AI
- **Azure OpenAI** - Enterprise OpenAI deployment
- **Ollama** - Local model serving

## Architecture

The multi-provider system consists of the following components:

### Core Components

- **`llm_providers.py`**: Contains the core provider orchestration logic
  - `SecureProviderManager`: Securely manages API keys and provider configuration
  - `ProviderRateLimiter`: Rate limiting for LLM provider calls
  - `MultiProviderOrchestrator`: Orchestrates multiple LLM providers with intelligent selection and fallback

- **`enhanced_ai_layer.py`**: Provides enhanced AI capabilities
  - `EnhancedAILayer`: Enhanced AI layer with multi-provider support

- **`provider_config.py`**: Configuration for the multi-provider system
  - Provider-specific settings
  - User tier definitions
  - Task-specific provider recommendations

- **`processors_updated.py`**: Updated processor implementation
  - `MultiProviderProcessor`: Enhanced document processor with multi-provider support

### Integration with Factory

The `ProcessorFactory` has been updated to support creating processors with multi-provider support:

- `get_processor_for_file()`: Now accepts `use_multi_provider` parameter
- `get_processor_for_content_type()`: Now accepts `use_multi_provider` parameter
- `get_optimal_processor()`: Now accepts `use_multi_provider` parameter
- `get_enhanced_processor()`: Uses multi-provider by default

## Usage

### Basic Usage

```python
from processors.factory import ProcessorFactory

# Create a processor with multi-provider support
processor = ProcessorFactory.get_enhanced_processor(
    content_type="text/plain",
    use_multi_provider=True,
    dataset_name="example_dataset",
    user_tier="pro_tier",
    priority="balanced"
)

# Process content with enhancements
result = await processor.process_with_enhancements(
    content="Content to process",
    enhancement_type="analysis"
)
```

### Advanced Usage

```python
from processors.processors_updated import MultiProviderProcessor
from core.enhanced_ai_layer import EnhancedAILayer

# Create a custom AI layer
ai_layer = EnhancedAILayer(
    default_provider="openai",
    default_priority="quality",
    user_tier="enterprise"
)

# Create a multi-provider processor with custom AI layer
processor = MultiProviderProcessor(
    enable_ai=True,
    ai_layer=ai_layer,
    user_tier="enterprise",
    priority="quality"
)

# Analyze a document
result = await processor.analyze_document(
    content="Document to analyze",
    content_type="text/plain",
    analysis_type="comprehensive"
)
```

### Direct Provider Access

```python
from core.llm_providers import default_orchestrator

# Execute with specific provider
result = await default_orchestrator.execute_with_fallback(
    prompt="Prompt to execute",
    provider_chain=["openai", "anthropic"],
    task_type="chat",
    user_tier="pro_tier",
    priority="quality"
)
```

## Configuration

### Environment Variables

The system requires API keys for the providers you want to use. Set the following environment variables:

```
OPENROUTER_API_KEY=your_openrouter_key
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_API_KEY=your_google_key
XAI_API_KEY=your_xai_key
GROQ_API_KEY=your_groq_key
MISTRAL_API_KEY=your_mistral_key
PERPLEXITY_API_KEY=your_perplexity_key
AZURE_OPENAI_API_KEY=your_azure_openai_key
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint
OLLAMA_API_KEY=your_ollama_key
OLLAMA_BASE_URL=your_ollama_base_url
```

### Provider Configuration

The provider configuration is defined in `provider_config.py`. You can customize:

- **Provider-specific settings**: Models, temperature, costs, etc.
- **Provider tiers**: Speed, quality, balanced, research
- **User tier limits**: Rate limits, token limits, allowed providers
- **Task-specific recommendations**: Recommended providers for different tasks

## Examples

See `examples/multi_provider_example.py` for complete usage examples.

## Integration with LangChain

The system integrates with LangChain, leveraging the following provider-specific packages:

```
langchain-openai        # OpenAI and OpenRouter
langchain-anthropic     # Anthropic Claude
langchain-google-genai  # Google Gemini
langchain-xai           # Grok API
langchain-groq          # Groq
langchain-mistral       # Mistral
langchain-together      # Together AI
langchain-perplexity    # Perplexity
```

## Error Handling and Fallback

The system includes robust error handling and automatic fallback mechanisms:

1. When a provider fails, it's marked as unhealthy
2. The system falls back to the next provider in the provider chain
3. If no provider chain is specified, it creates a fallback chain based on provider tiers
4. Providers are periodically marked as healthy again for retry

## Cost Optimization

The system optimizes costs by:

1. Selecting cost-effective providers for simpler tasks
2. Using premium providers only when needed for complex tasks
3. Limiting free-tier users to cost-effective providers
4. Using local models (Ollama) when possible for zero cost

## Security

The system includes security features:

1. Secure credential management through environment variables
2. Rate limiting to prevent abuse
3. User tier restrictions to limit access to expensive providers
4. API key redaction in logs and diagnostic output

## Future Enhancements

Planned enhancements for the multi-provider system:

1. **Streaming Support**: Add streaming support for providers that support it
2. **Token Counting**: Add token counting for cost tracking
3. **Caching**: Add result caching to reduce API calls
4. **Provider Performance Metrics**: Track latency and reliability metrics
5. **Specialized Providers**: Add support for specialized providers (e.g., code generation)
6. **Tool Usage**: Support for provider-specific tool calling capabilities 

