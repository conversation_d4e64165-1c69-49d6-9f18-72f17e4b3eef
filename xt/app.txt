--- app/api/deps.py.txt ---
from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2<PERSON>asswordBearer
from jose import jwt, JWTError
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.config.settings import settings
from app.db.session import SessionLocal
from app.crud.crud_user import get_user_by_email, create_user
from app.models.user import User

reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login"
)

class TokenData(BaseModel):
    username: Optional[str] = None
    
class UserInfo(BaseModel):
    user_id: str
    email: str
    is_active: bool = True
    is_superuser: bool = False

def get_db() -> Generator:
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

def get_current_user(
    db: Session = Depends(get_db), token: str = Depends(reusable_oauth2)
) -> UserInfo:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    
    user = get_user_by_email(db, email=token_data.username)
    if user is None:
        raise credentials_exception
    
    return UserInfo(
        user_id=str(user.id),
        email=user.email,
        is_active=user.is_active,
        is_superuser=user.is_superuser if hasattr(user, 'is_superuser') else False
    )


def get_current_active_user(
    current_user: UserInfo = Depends(get_current_user),
) -> UserInfo:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


--- app/api/endpoints/auth.py.txt ---
"""
Comprehensive Authentication API endpoints for ConTXT.
Provides user registration, login, email verification, password management, and API key management.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from fastapi.security import HTTPBearer
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from typing import List, Dict, Any
from datetime import datetime
import asyncpg
import logging

from app.core.auth_service import AuthService
from app.core.otp_service import OTPService
from app.core.email_service import EmailService
from app.core.auth_dependencies import (
    get_current_user, get_verified_user, get_premium_user, 
    get_client_ip, get_user_agent, get_db_pool
)
from app.schemas.auth import (
    UserRegistration, UserLogin, TokenResponse, RefreshTokenRequest,
    UserProfile, UserProfileUpdate, PasswordChange, PasswordResetOTP,
    OTPRequest, OTPVerification, ApiKeyCreate,
    ApiKeyResponse, ApiKeyList, AuthResponse, ErrorResponse
)
from app.config.settings import settings

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Authentication"])
security = HTTPBearer()

# Rate limiter setup
limiter = Limiter(key_func=get_remote_address)
app = None  # Will be set when router is included

# Dependency to get services
def get_email_service() -> EmailService:
    return EmailService()

def get_otp_service(db_pool: asyncpg.Pool = Depends(get_db_pool), email_service: EmailService = Depends(get_email_service)) -> OTPService:
    return OTPService(db_pool, email_service)

def get_auth_service(db_pool: asyncpg.Pool = Depends(get_db_pool), otp_service: OTPService = Depends(get_otp_service), email_service: EmailService = Depends(get_email_service)) -> AuthService:
    """Get authentication service instance with all dependencies."""
    return AuthService(db_pool, otp_service, email_service)

# ============================================================================
# REGISTRATION & LOGIN ENDPOINTS
# ============================================================================

@router.post("/register", response_model=AuthResponse, status_code=status.HTTP_201_CREATED)
@limiter.limit(settings.REGISTER_RATE_LIMIT if settings.RATE_LIMIT_ENABLED else "1000/minute")
async def register(
    request: Request,
    user_data: UserRegistration,
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Register new user with email verification.
    
    - **email**: Valid email address
    - **password**: Strong password (min 8 chars, uppercase, lowercase, digit)
    - **first_name**: Optional first name
    - **last_name**: Optional last name
    
    Returns registration confirmation with verification instructions.
    """
    try:
        result = await auth_service.register_user(user_data)
        return AuthResponse(
            message=result["message"],
            success=True,
            data={"user_id": result["user_id"]}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )

@router.post("/login", response_model=TokenResponse)
@limiter.limit(settings.LOGIN_RATE_LIMIT if settings.RATE_LIMIT_ENABLED else "1000/minute")
async def login(
    request: Request,
    login_data: UserLogin,
    auth_service: AuthService = Depends(get_auth_service)
) -> TokenResponse:
    """
    User login with JWT token generation.
    
    - **email**: User email address
    - **password**: User password
    
    Returns JWT access and refresh tokens.
    """
    try:
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)
        
        return await auth_service.login_user(login_data, ip_address, user_agent)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )

@router.post("/refresh-token", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    auth_service: AuthService = Depends(get_auth_service)
) -> TokenResponse:
    """
    Refresh JWT access token using refresh token.
    
    - **refresh_token**: Valid refresh token
    
    Returns new access token.
    """
    try:
        return await auth_service.refresh_access_token(refresh_data.refresh_token)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )

@router.post("/logout", response_model=AuthResponse)
async def logout(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Logout user and invalidate sessions.
    
    Invalidates all user sessions for security.
    """
    try:
        result = await auth_service.logout_user(current_user["id"])
        return AuthResponse(message=result["message"])
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )

# ============================================================================
# EMAIL VERIFICATION ENDPOINTS
# ============================================================================

@router.post("/verify-email-otp", response_model=AuthResponse)
@limiter.limit(settings.VERIFY_EMAIL_RATE_LIMIT if settings.RATE_LIMIT_ENABLED else "1000/minute")
async def verify_email_otp(
    request: Request,
    verification_data: OTPVerification,
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Verify user email with OTP.
    
    - **email**: User's email address
    - **otp**: 6-digit code from email
    
    Activates user account after successful verification.
    """
    try:
        result = await auth_service.verify_otp(verification_data)
        return AuthResponse(message=result["message"], success=True)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Email verification OTP error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed"
        )

@router.post("/resend-verification-otp", response_model=AuthResponse)
@limiter.limit("3/hour")
async def resend_verification_otp(
    request: Request,
    otp_request_data: OTPRequest,
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Resend email verification OTP.
    
    - **email**: User email address
    
    Sends a new verification OTP if the user exists and is unverified.
    """
    try:
        result = await auth_service.resend_verification_otp(otp_request_data)
        return AuthResponse(message=result["message"], success=True)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Resend verification OTP error for {otp_request_data.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing your request."
        )

# ============================================================================
# PASSWORD MANAGEMENT ENDPOINTS
# ============================================================================

@router.post("/request-password-reset-otp", response_model=AuthResponse)
@limiter.limit(settings.PASSWORD_RESET_RATE_LIMIT if settings.RATE_LIMIT_ENABLED else "1000/hour")
async def request_password_reset_otp(
    request: Request,
    otp_request_data: OTPRequest,
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Request a password reset OTP.

    - **email**: The user's email address.

    Sends a password reset OTP to the user's email if the account exists.
    """
    try:
        result = await auth_service.request_password_reset_otp(otp_request_data)
        return AuthResponse(message=result["message"], success=True)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset request error for email {otp_request_data.email}: {e}")
        # Generic response to prevent email enumeration
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing your request."
        )

@router.post("/reset-password-with-otp", response_model=AuthResponse)
async def reset_password_with_otp(
    request: Request,
    reset_data: PasswordResetOTP,
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Reset password with OTP.
    
    - **otp**: OTP code from email
    - **token**: Password reset token from email
    - **new_password**: New strong password
    
    Resets password and invalidates all user sessions.
    """
    try:
        result = await auth_service.reset_password_with_otp(reset_data)
        return AuthResponse(message=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )

@router.post("/change-password", response_model=AuthResponse)
async def change_password(
    password_data: PasswordChange,
    current_user: Dict[str, Any] = Depends(get_verified_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> AuthResponse:
    """
    Change user password (requires authentication).
    
    - **current_password**: Current password
    - **new_password**: New strong password
    
    Requires valid authentication and current password verification.
    """
    try:
        result = await auth_service.change_password(current_user["id"], password_data)
        return AuthResponse(message=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password change error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )

# ============================================================================
# USER PROFILE ENDPOINTS
# ============================================================================

@router.get("/me", response_model=UserProfile)
async def get_profile(
    current_user: Dict[str, Any] = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> UserProfile:
    """
    Get current user profile.
    
    Returns detailed user information for authenticated user.
    """
    try:
        return await auth_service.get_user_profile(current_user["id"])
    except Exception as e:
        logger.error(f"Get profile error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve profile"
        )

@router.put("/me", response_model=UserProfile)
async def update_profile(
    profile_data: UserProfileUpdate,
    current_user: Dict[str, Any] = Depends(get_verified_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> UserProfile:
    """
    Update user profile.
    
    - **first_name**: Updated first name
    - **last_name**: Updated last name
    
    Updates user profile information.
    """
    try:
        return await auth_service.update_user_profile(current_user["id"], profile_data)
    except Exception as e:
        logger.error(f"Update profile error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed"
        )

# ============================================================================
# API KEY MANAGEMENT ENDPOINTS
# ============================================================================

@router.post("/api-keys", response_model=ApiKeyResponse)
async def create_api_key(
    api_key_data: ApiKeyCreate,
    current_user: Dict[str, Any] = Depends(get_verified_user),
    auth_service: AuthService = Depends(get_auth_service)
) -> ApiKeyResponse:
    """
    Create new API key for programmatic access.
    
    - **key_name**: Descriptive name for the API key
    - **permissions**: List of permissions (read, write, etc.)
    - **expires_at**: Optional expiration date
    
    Returns API key details including the secret key (shown only once).
    """
    try:
        return await auth_service.create_api_key(current_user["id"], api_key_data)
    except Exception as e:
        logger.error(f"API key creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="API key creation failed"
        )

@router.get("/api-keys", response_model=List[ApiKeyList])
async def list_api_keys(
    current_user: Dict[str, Any] = Depends(get_verified_user),
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> List[ApiKeyList]:
    """
    List user's API keys (without secret keys).
    
    Returns list of API keys with metadata but not the actual keys.
    """
    try:
        async with db_pool.acquire() as conn:
            keys = await conn.fetch("""
                SELECT id, key_name, permissions, is_active, 
                       last_used_at, expires_at, created_at
                FROM user_api_keys 
                WHERE user_id = $1
                ORDER BY created_at DESC
            """, current_user["id"])
            
            return [ApiKeyList(**dict(key)) for key in keys]
    except Exception as e:
        logger.error(f"List API keys error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve API keys"
        )

@router.delete("/api-keys/{key_id}", response_model=AuthResponse)
async def delete_api_key(
    key_id: str,
    current_user: Dict[str, Any] = Depends(get_verified_user),
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> AuthResponse:
    """
    Delete/deactivate API key.
    
    - **key_id**: ID of the API key to delete
    
    Deactivates the specified API key.
    """
    try:
        async with db_pool.acquire() as conn:
            result = await conn.execute("""
                UPDATE user_api_keys 
                SET is_active = false
                WHERE id = $1 AND user_id = $2
            """, key_id, current_user["id"])
            
            if result == "UPDATE 0":
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="API key not found"
                )
            
            return AuthResponse(message="API key deleted successfully")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete API key error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="API key deletion failed"
        )

# ============================================================================
# HEALTH CHECK ENDPOINT
# ============================================================================

@router.get("/health", response_model=Dict[str, Any])
async def health_check(
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> Dict[str, Any]:
    """
    Health check for authentication service.
    
    Verifies database connectivity and service status.
    """
    try:
        async with db_pool.acquire() as conn:
            await conn.fetchval("SELECT 1")
        
        return {
            "status": "healthy",
            "service": "auth",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Database connection failed: {str(e)}"
        )


--- app/api/endpoints/context.py.txt ---
"""
API endpoints for context engineering operations.
"""
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional, Any

from app.core.context_engine import ContextEngine
from app.schemas.context import ContextRequest, ContextResponse, SystemPromptRequest

router = APIRouter()

@router.post("/build", response_model=ContextResponse)
async def build_context(request: ContextRequest):
    """
    Build engineered context from provided sources.
    
    This endpoint processes the input sources, analyzes their content,
    and curates an optimized context for AI agents.
    
    Context engineering steps:
    1. Select relevant knowledge from sources
    2. Compress content to fit within context window
    3. Order content by relevance and importance
    4. Structure output for optimal consumption
    """
    try:
        context_engine = ContextEngine()
        result = await context_engine.build_context(
            sources=request.sources,
            max_tokens=request.max_tokens or 128000,
            compression_ratio=request.compression_ratio or 0.5
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Context building failed: {str(e)}")

@router.post("/generate-system-prompt", response_model=Dict[str, str])
async def generate_system_prompt(request: SystemPromptRequest):
    """
    Generate a system prompt based on engineered context.
    
    This endpoint takes context information and generates a tailored 
    system prompt for specific AI tools like Cursor, Windsurf, etc.
    """
    try:
        context_engine = ContextEngine()
        system_prompt = await context_engine.generate_system_prompt(
            context_id=request.context_id,
            tool_type=request.tool_type,
            parameters=request.parameters
        )
        return {"system_prompt": system_prompt}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"System prompt generation failed: {str(e)}")

@router.get("/status/{context_id}", response_model=Dict[str, Any])
async def get_context_status(context_id: str):
    """Get the status and metadata of a context building operation."""
    try:
        context_engine = ContextEngine()
        status = await context_engine.get_context_status(context_id)
        return status
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Context not found: {str(e)}") 

--- app/api/endpoints/frontend.py.txt ---
"""
Frontend-specific endpoints for theme management and UI state.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

from ..deps import get_current_user, UserInfo

router = APIRouter()

# Pydantic models
class ThemeRequest(BaseModel):
    theme: str  # "dark-oled" or "snow-white"

class ThemeResponse(BaseModel):
    theme: str
    updated_at: datetime

class LayoutState(BaseModel):
    sidebar_open: bool = True
    terminal_open: bool = False
    terminal_height: int = 300
    sidebar_width: int = 300
    active_tab: str = "overview"

class LayoutRequest(BaseModel):
    layout: LayoutState

class LayoutResponse(BaseModel):
    layout: LayoutState
    updated_at: datetime

class NodeDetailsResponse(BaseModel):
    id: str
    title: Optional[str] = None
    type: Optional[str] = None
    properties: Dict[str, Any] = {}
    metadata: Dict[str, Any] = {}
    content: Optional[str] = None
    relationships: list = []
    neighbors: list = []
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

# Mock storage for user preferences (replace with database in production)
USER_THEMES = {}
USER_LAYOUTS = {}

@router.get("/theme", response_model=ThemeResponse)
async def get_user_theme(current_user: UserInfo = Depends(get_current_user)):
    """Get user theme preference"""
    user_id = current_user.user_id
    theme_data = USER_THEMES.get(user_id, {
        "theme": "dark-oled",  # Default theme
        "updated_at": datetime.utcnow()
    })
    
    return ThemeResponse(
        theme=theme_data["theme"],
        updated_at=theme_data["updated_at"]
    )

@router.post("/theme", response_model=ThemeResponse)
async def set_user_theme(
    theme_request: ThemeRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """Set user theme preference"""
    user_id = current_user.user_id
    
    # Validate theme
    valid_themes = ["dark-oled", "snow-white"]
    if theme_request.theme not in valid_themes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid theme. Must be one of: {valid_themes}"
        )
    
    # Store theme preference
    theme_data = {
        "theme": theme_request.theme,
        "updated_at": datetime.utcnow()
    }
    USER_THEMES[user_id] = theme_data
    
    return ThemeResponse(
        theme=theme_data["theme"],
        updated_at=theme_data["updated_at"]
    )

@router.get("/layout", response_model=LayoutResponse)
async def get_layout_state(current_user: UserInfo = Depends(get_current_user)):
    """Get user layout state"""
    user_id = current_user.user_id
    layout_data = USER_LAYOUTS.get(user_id, {
        "layout": LayoutState(),
        "updated_at": datetime.utcnow()
    })
    
    return LayoutResponse(
        layout=layout_data["layout"],
        updated_at=layout_data["updated_at"]
    )

@router.post("/layout", response_model=LayoutResponse)
async def set_layout_state(
    layout_request: LayoutRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """Set user layout state"""
    user_id = current_user.user_id
    
    # Store layout state
    layout_data = {
        "layout": layout_request.layout,
        "updated_at": datetime.utcnow()
    }
    USER_LAYOUTS[user_id] = layout_data
    
    return LayoutResponse(
        layout=layout_data["layout"],
        updated_at=layout_data["updated_at"]
    )

@router.get("/node/{node_id}/details", response_model=NodeDetailsResponse)
async def get_node_details(
    node_id: str,
    current_user: UserInfo = Depends(get_current_user)
):
    """Get comprehensive node information for terminal display"""
    try:
        # Import here to avoid circular imports
        from app.core.knowledge_graph import KnowledgeGraph
        
        kg = KnowledgeGraph()
        
        # Get node details
        node_query = """
        MATCH (n) WHERE elementId(n) = $node_id OR n.id = $node_id
        RETURN n, labels(n) as labels, properties(n) as props
        """
        node_result = await kg.neo4j_client.run_query(node_query, {"node_id": node_id})
        
        if not node_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Node with ID {node_id} not found"
            )
        
        node_data = node_result[0]
        node = node_data["n"]
        labels = node_data["labels"]
        props = node_data["props"]
        
        # Get relationships
        relationships_query = """
        MATCH (n)-[r]-(m) 
        WHERE elementId(n) = $node_id OR n.id = $node_id
        RETURN type(r) as rel_type, 
               CASE WHEN startNode(r) = n THEN 'outgoing' ELSE 'incoming' END as direction,
               m.title as target_title,
               elementId(m) as target_id,
               properties(r) as rel_props
        LIMIT 50
        """
        relationships_result = await kg.neo4j_client.run_query(
            relationships_query, {"node_id": node_id}
        )
        
        relationships = []
        for rel in relationships_result:
            relationships.append({
                "type": rel["rel_type"],
                "direction": rel["direction"],
                "target_title": rel["target_title"],
                "target_id": rel["target_id"],
                "properties": rel["rel_props"]
            })
        
        # Get neighbors
        neighbors_query = """
        MATCH (n)-[r]-(m) 
        WHERE elementId(n) = $node_id OR n.id = $node_id
        RETURN DISTINCT m.title as neighbor_title,
               elementId(m) as neighbor_id,
               labels(m) as neighbor_labels
        LIMIT 20
        """
        neighbors_result = await kg.neo4j_client.run_query(
            neighbors_query, {"node_id": node_id}
        )
        
        neighbors = []
        for neighbor in neighbors_result:
            neighbors.append({
                "id": neighbor["neighbor_id"],
                "title": neighbor["neighbor_title"],
                "labels": neighbor["neighbor_labels"]
            })
        
        # Extract metadata and content
        metadata = {}
        content = None
        
        # Common metadata fields
        metadata_fields = ["created_at", "updated_at", "source", "url", "file_path", "chunk_index"]
        for field in metadata_fields:
            if field in props:
                metadata[field] = props[field]
        
        # Content fields
        content_fields = ["content", "text", "raw_data", "summary", "description"]
        for field in content_fields:
            if field in props and props[field]:
                content = props[field]
                break
        
        return NodeDetailsResponse(
            id=node_id,
            title=props.get("title", props.get("name", node_id)),
            type=labels[0] if labels else "Unknown",
            properties=props,
            metadata=metadata,
            content=content,
            relationships=relationships,
            neighbors=neighbors,
            created_at=props.get("created_at"),
            updated_at=props.get("updated_at")
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching node details: {str(e)}"
        )


--- app/api/endpoints/health.py.txt ---
from fastapi import APIRouter, Depends, HTTPException, status
import asyncpg

from app.db.database import get_db_pool

router = APIRouter()

AUTH_TABLES = [
    "users",
    "otps",
    "otp_rate_limits",
    "auth_events",
    "user_sessions",
    "api_keys",
    "user_preferences",
]

@router.get("/auth-db", status_code=status.HTTP_200_OK, summary="Check Authentication Database Health")
async def check_auth_database_health(db_pool: asyncpg.Pool = Depends(get_db_pool)):
    """
    Checks the health of the authentication database.

    Verifies that all required tables for the authentication system exist.
    """
    missing_tables = []
    async with db_pool.acquire() as conn:
        for table in AUTH_TABLES:
            try:
                exists = await conn.fetchval(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = '{table}'
                    );
                """)
                if not exists:
                    missing_tables.append(table)
            except asyncpg.PostgresError as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Database error: {e}"
                )

    if missing_tables:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Missing required authentication tables: {', '.join(missing_tables)}"
        )

    return {"status": "ok", "message": "All authentication tables are present."}


--- app/api/endpoints/ingestion.py.txt ---
"""
API endpoints for data ingestion operations.

These endpoints support processing various types of content with
optional AI enhancements and Cognee integration for advanced analysis.
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from typing import Dict, List, Optional, Any

from app.core.ingestion import IngestionManager
from app.schemas.ingestion import (
    UrlIngestionRequest,
    FileIngestionRequest,
    TextIngestionRequest,
    PrivacyIngestionRequest,
    IngestionResponse,
    IngestionStatus
)

router = APIRouter()

@router.post("/url", response_model=IngestionResponse)
async def ingest_url(request: UrlIngestionRequest):
    """
    Ingest content from a URL.
    
    This endpoint processes content from a URL, extracts relevant information,
    and stores it in the knowledge graph and vector database.
    
    Optional AI enhancements can be enabled via the options field.
    """
    try:
        # Extract enhancement options
        options = request.options or {}
        use_cognee = options.get("use_cognee", False)
        enable_ai = options.get("enable_ai", False)
        
        # Initialize ingestion manager with options
        ingestion_manager = IngestionManager(use_cognee=use_cognee, enable_ai=enable_ai)
        
        job_id = await ingestion_manager.ingest_url(
            url=request.url,
            metadata=request.metadata,
            options=options
        )
        return IngestionResponse(job_id=job_id, status="processing")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"URL ingestion failed: {str(e)}")

@router.post("/file", response_model=IngestionResponse)
async def ingest_file(
    file: UploadFile = File(...),
    metadata: Optional[str] = Form(None),
    use_cognee: bool = Form(False),
    enable_ai: bool = Form(False),
    dataset_name: Optional[str] = Form(None)
):
    """
    Ingest content from an uploaded file.
    
    This endpoint processes uploaded files (PDF, text, etc.), extracts relevant
    information, and stores it in the knowledge graph and vector database.
    
    Optional parameters:
    - use_cognee: Whether to use Cognee for database operations
    - enable_ai: Whether to enable AI enhancements
    - dataset_name: Name of the dataset for Cognee integration
    """
    try:
        # Create options dictionary from form parameters
        options = {
            "use_cognee": use_cognee,
            "enable_ai": enable_ai
        }
        
        if dataset_name:
            options["dataset_name"] = dataset_name
        
        # Initialize ingestion manager with options
        ingestion_manager = IngestionManager(use_cognee=use_cognee, enable_ai=enable_ai)
        
        job_id = await ingestion_manager.ingest_file(
            file=file,
            metadata=metadata,
            options=options
        )
        return IngestionResponse(job_id=job_id, status="processing")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"File ingestion failed: {str(e)}")

@router.post("/text", response_model=IngestionResponse)
async def ingest_text(
    text: str = Form(...),
    metadata: Optional[str] = Form(None),
    use_cognee: bool = Form(False),
    enable_ai: bool = Form(False),
    dataset_name: Optional[str] = Form(None)
):
    """
    Ingest raw text content.
    
    This endpoint processes raw text, extracts relevant information,
    and stores it in the knowledge graph and vector database.
    
    Optional parameters:
    - use_cognee: Whether to use Cognee for database operations
    - enable_ai: Whether to enable AI enhancements
    - dataset_name: Name of the dataset for Cognee integration
    """
    try:
        # Create options dictionary from form parameters
        options = {
            "use_cognee": use_cognee,
            "enable_ai": enable_ai
        }
        
        if dataset_name:
            options["dataset_name"] = dataset_name
        
        # Initialize ingestion manager with options
        ingestion_manager = IngestionManager(use_cognee=use_cognee, enable_ai=enable_ai)
        
        job_id = await ingestion_manager.ingest_text(
            text=text,
            metadata=metadata,
            options=options
        )
        return IngestionResponse(job_id=job_id, status="processing")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Text ingestion failed: {str(e)}")

@router.post("/privacy", response_model=IngestionResponse)
async def ingest_with_privacy(request: PrivacyIngestionRequest):
    """
    Ingest content with privacy compliance.
    
    This endpoint processes content with privacy compliance, redacting
    personally identifiable information (PII) as specified.
    
    Optional AI enhancements can be enabled via the options field.
    """
    try:
        # Extract enhancement options
        options = request.options or {}
        use_cognee = options.get("use_cognee", False)
        enable_ai = options.get("enable_ai", False)
        
        # Initialize ingestion manager with options
        ingestion_manager = IngestionManager(use_cognee=use_cognee, enable_ai=enable_ai)
        
        job_id = await ingestion_manager.ingest_with_privacy(
            content=request.content,
            content_type=request.content_type,
            redact_pii=request.redact_pii,
            pii_types=request.pii_types,
            metadata=request.metadata,
            options=options
        )
        return IngestionResponse(job_id=job_id, status="processing")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Privacy-compliant ingestion failed: {str(e)}")

@router.get("/status/{job_id}", response_model=IngestionStatus)
async def get_ingestion_status(job_id: str):
    """Get the status of an ingestion job."""
    try:
        ingestion_manager = IngestionManager()
        status = await ingestion_manager.get_status(job_id)
        return status
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Ingestion job not found: {str(e)}")

@router.get("/enhancement-options", response_model=Dict[str, Any])
async def get_enhancement_options():
    """
    Get available enhancement options for document processing.
    
    Returns information about available AI models, database integrations,
    and other enhancement options.
    """
    try:
        # Try to import optional dependencies to check availability
        cognee_available = False
        xai_available = False
        openai_available = False
        
        try:
            import cognee
            cognee_available = True
        except ImportError:
            pass
            
        try:
            from langchain_xai import ChatXAI
            xai_available = True
        except ImportError:
            pass
            
        try:
            from langchain_openai import ChatOpenAI
            openai_available = True
        except ImportError:
            pass
        
        # Return available options
        return {
            "cognee_available": cognee_available,
            "ai_models": {
                "xai_available": xai_available,
                "openai_available": openai_available
            },
            "enhancement_types": [
                "analysis",
                "summary",
                "entity_extraction",
                "insight_generation"
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving enhancement options: {str(e)}") 

--- app/api/endpoints/knowledge.py.txt ---
"""
API endpoints for knowledge graph operations.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, List, Optional, Any

from app.core.knowledge_graph import KnowledgeGraph
from app.schemas.knowledge import (
    GraphQueryRequest,
    GraphQueryResponse,
    EntityRequest,
    RelationshipRequest
)

router = APIRouter()

@router.post("/query", response_model=GraphQueryResponse)
async def query_knowledge_graph(request: GraphQueryRequest):
    """
    Query the knowledge graph for information.
    
    This endpoint allows querying the Neo4j knowledge graph using
    natural language or Cypher queries.
    """
    try:
        kg = KnowledgeGraph()
        results = await kg.query(
            query_text=request.query,
            query_type=request.query_type,
            limit=request.limit or 10
        )
        return {"success": True, "data": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Knowledge graph query failed: {str(e)}")

@router.post("/entity", response_model=Dict[str, str])
async def add_entity(request: Dict[str, str]):
    """
    Add an entity to the knowledge graph.
    
    This endpoint adds a new entity node to the Neo4j knowledge graph.
    """
    try:
        kg = KnowledgeGraph()
        entity_id = await kg.add_entity(
            entity_type=request.get('entity_type'),
            properties=request.get('properties')
        )
        return {"entity_id": entity_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add entity: {str(e)}")

@router.post("/relationship", response_model=Dict[str, str])
async def add_relationship(request: Dict[str, str]):
    """
    Add a relationship between entities in the knowledge graph.
    
    This endpoint creates a relationship between two entities in the Neo4j graph.
    """
    try:
        kg = KnowledgeGraph()
        relationship_id = await kg.add_relationship(
            source_id=request.get('source_id'),
            target_id=request.get('target_id'),
            relationship_type=request.get('relationship_type'),
            properties=request.get('properties')
        )
        return {"relationship_id": relationship_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add relationship: {str(e)}")

@router.get("/stats", response_model=Dict[str, Any])
async def get_knowledge_graph_stats():
    """Get statistics about the knowledge graph."""
    try:
        kg = KnowledgeGraph()
        stats = await kg.get_stats()
        return {"success": True, "data": stats}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get graph statistics: {str(e)}")

# Graph visualization endpoints

@router.get("/graph/nodes")
async def get_all_nodes(
    limit: int = Query(100, le=1000),
    node_type: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    kg: KnowledgeGraph = Depends(KnowledgeGraph)
):
    """Get all nodes with metadata for visualization"""
    try:
        nodes = await kg.get_all_nodes(limit=limit, node_type=node_type, search=search)
        return {"success": True, "data": nodes}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/graph/relationships")
async def get_all_relationships(limit: int = Query(100, le=1000), kg: KnowledgeGraph = Depends(KnowledgeGraph)):
    """Get all relationships for visualization"""
    try:
        relationships = await kg.get_all_relationships(limit=limit)
        return {"success": True, "data": relationships}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/graph/full")
async def get_full_graph(limit: int = Query(50, le=200), kg: KnowledgeGraph = Depends(KnowledgeGraph)):
    """Get complete graph data for visualization"""
    try:
        nodes = await kg.get_all_nodes(limit=limit)
        links = await kg.get_all_relationships(limit=limit)

        graph_data = {
            "nodes": nodes,
            "links": links,
            "metadata": {
                "total_nodes": len(nodes),
                "total_relationships": len(links),
                "node_types": list(set(node.get("type", "Unknown") for node in nodes))
            }
        }
        
        return {"success": True, "data": graph_data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/graph/node/{node_id}/neighbors")
async def get_node_neighbors(node_id: str, depth: int = Query(1, le=3), kg: KnowledgeGraph = Depends(KnowledgeGraph)):
    """Get neighbors of a specific node"""
    try:
        neighbor_data = await kg.get_neighbors(node_id, depth=depth)
        if not neighbor_data or not neighbor_data.get('nodes'):
             raise HTTPException(status_code=404, detail=f"Node with id {node_id} not found or has no neighbors.")
        return {"success": True, "data": neighbor_data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics/stats")
async def get_graph_analytics(kg: KnowledgeGraph = Depends(KnowledgeGraph)):
    """Get graph analytics and statistics"""
    try:
        analytics = await kg.get_graph_analytics()
        return {"success": True, "data": analytics}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

--- app/api/router.py.txt ---
"""
Main API router that includes all endpoint routers.
"""
from fastapi import APIRouter

from app.api.endpoints import context, knowledge, ingestion, auth, frontend

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(frontend.router, prefix="/frontend", tags=["frontend"])
api_router.include_router(ingestion.router, prefix="/ingestion", tags=["ingestion"])
api_router.include_router(context.router, prefix="/context", tags=["context"])
api_router.include_router(knowledge.router, prefix="/knowledge", tags=["knowledge"]) 

--- app/config/__init__.py.txt ---
"""
Configuration for the application.

This module provides configuration settings for the application,
including application settings and provider configuration.
"""
from .settings import Settings

# Import provider configuration if available
try:
    from .provider_config import (
        PROVIDER_SETTINGS,
        PROVIDER_TIERS,
        USER_TIER_LIMITS,
        DEFAULT_PROVIDER,
        DEFAULT_PRIORITY,
        DEFAULT_USER_TIER,
        get_provider_setting,
        get_provider_model,
        get_recommended_providers
    )
    PROVIDER_CONFIG_AVAILABLE = True
except ImportError:
    PROVIDER_CONFIG_AVAILABLE = False

# Export main classes
__all__ = ["Settings"]

# Add provider configuration exports if available
if PROVIDER_CONFIG_AVAILABLE:
    __all__.extend([
        "PROVIDER_SETTINGS",
        "PROVIDER_TIERS",
        "USER_TIER_LIMITS",
        "DEFAULT_PROVIDER",
        "DEFAULT_PRIORITY",
        "DEFAULT_USER_TIER",
        "get_provider_setting",
        "get_provider_model",
        "get_recommended_providers"
    ])

# Create a default settings instance
settings = Settings() 

--- app/config/provider_config.py.txt ---
"""
Configuration for multi-provider LLM integration.

This module provides configuration settings for the multi-provider LLM system,
including provider-specific settings, cost tiers, and feature flags.
"""
import os
from typing import Dict, Any, Optional, List

# Provider-specific settings
PROVIDER_SETTINGS = {
    "openai": {
        "enabled": True,
        "models": {
            "chat": "gpt-4o",
            "fallback": "gpt-3.5-turbo",
            "embedding": "text-embedding-3-large"
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 10.0,  # $10 per 1M tokens for GPT-4o
        "context_length": 128000,  # GPT-4o context length
        "streaming_supported": True
    },
    "anthropic": {
        "enabled": True,
        "models": {
            "chat": "claude-3-sonnet-20240229",
            "fallback": "claude-3-haiku-20240307",
            "embedding": None  # Anthropic doesn't provide embeddings
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 15.0,  # $15 per 1M tokens for Claude 3 Sonnet
        "context_length": 200000,  # Claude 3 Sonnet context length
        "streaming_supported": True
    },
    "xai": {
        "enabled": True,
        "models": {
            "chat": "grok-beta",
            "fallback": "grok-beta",
            "embedding": None
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 8.0,  # $8 per 1M tokens for Grok
        "context_length": 8192,  # Grok context length
        "streaming_supported": True,
        "base_url": "https://api.x.ai/v1"
    },
    "google": {
        "enabled": True,
        "models": {
            "chat": "gemini-2.0-flash-exp",
            "fallback": "gemini-1.5-flash",
            "embedding": "models/embedding-001"
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 7.0,  # $7 per 1M tokens for Gemini Pro
        "context_length": 32768,  # Gemini Pro context length
        "streaming_supported": True
    },
    "groq": {
        "enabled": True,
        "models": {
            "chat": "llama-3.1-8b-instant",
            "fallback": "llama-3.1-8b-instant",
            "embedding": None
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 1.5,  # $1.5 per 1M tokens for LLama 3
        "context_length": 8192,  # LLama 3 context length
        "streaming_supported": True
    },
    "mistral": {
        "enabled": True,
        "models": {
            "chat": "mistral-large-latest",
            "fallback": "mistral-small-latest",
            "embedding": "mistral-embed"
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 5.0,  # $5 per 1M tokens for Mistral Large
        "context_length": 32768,  # Mistral Large context length
        "streaming_supported": True
    },
    "together": {
        "enabled": True,
        "models": {
            "chat": "meta-llama/Llama-3-70b-chat-hf",
            "fallback": "meta-llama/Llama-2-70b-chat-hf",
            "embedding": "togethercomputer/m2-bert-80M-8k-retrieval"
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 2.0,  # $2 per 1M tokens
        "context_length": 8192,  # Llama 3 context length
        "streaming_supported": True
    },
    "perplexity": {
        "enabled": True,
        "models": {
            "chat": "pplx-70b-online",
            "fallback": "pplx-7b-online",
            "embedding": None
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 10.0,  # $10 per 1M tokens
        "context_length": 4096,  # Perplexity context length
        "streaming_supported": True
    },
    "azure": {
        "enabled": True,
        "models": {
            "chat": os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
            "fallback": os.getenv("AZURE_OPENAI_FALLBACK_DEPLOYMENT", "gpt-35-turbo"),
            "embedding": os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT", "text-embedding-ada-002")
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 10.0,  # $10 per 1M tokens (varies by deployment)
        "context_length": 8192,  # Varies by deployment
        "streaming_supported": True,
        "endpoint": os.getenv("AZURE_OPENAI_ENDPOINT", "")
    },
    "ollama": {
        "enabled": True,
        "models": {
            "chat": "llama3",
            "fallback": "llama2",
            "embedding": "nomic-embed-text"
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 0.0,  # Free (local)
        "context_length": 4096,  # Varies by model
        "streaming_supported": True,
        "base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    },
    "openrouter": {
        "enabled": True,
        "models": {
            "chat": "anthropic/claude-3-sonnet",
            "fallback": "meta/llama-3-8b-instruct",
            "embedding": None  # Uses provider-specific embeddings
        },
        "temperature": 0.1,
        "cost_per_1m_tokens": 10.0,  # Varies by selected model
        "context_length": 200000,  # Varies by selected model
        "streaming_supported": True,
        "base_url": "https://openrouter.ai/api/v1",
        "default_headers": {
            "HTTP-Referer": "https://contxt.ai",
            "X-Title": "ConTXT AI"
        }
    }
}

# Provider tier definitions
PROVIDER_TIERS = {
    "speed": ["groq", "ollama"],  # Ultra-fast, affordable providers
    "quality": ["openai", "anthropic", "xai"],  # Premium quality providers
    "balanced": ["mistral", "together", "google"],  # Mid-tier cost-effective providers
    "research": ["perplexity"]  # Specialized for research and fact-checking
}

# User tier rate limits
USER_TIER_LIMITS = {
    "free_tier": {
        "requests_per_minute": 10,
        "monthly_tokens": 100000,  # 100K tokens per month
        "monthly_cost_limit": 0.0,  # $0 monthly limit
        "allowed_providers": ["ollama", "together", "groq"],  # Limited to cost-effective providers
        "default_priority": "speed"  # Optimize for speed
    },
    "pro_tier": {
        "requests_per_minute": 100,
        "monthly_tokens": 1000000,  # 1M tokens per month
        "monthly_cost_limit": 50.0,  # $50 monthly limit
        "allowed_providers": ["openai", "anthropic", "xai", "google", "groq", "mistral", "together", "perplexity", "ollama"],
        "default_priority": "balanced"  # Balanced approach
    },
    "enterprise": {
        "requests_per_minute": 1000,
        "monthly_tokens": 10000000,  # 10M tokens per month
        "monthly_cost_limit": 500.0,  # $500 monthly limit
        "allowed_providers": ["openai", "anthropic", "xai", "google", "groq", "mistral", "together", "perplexity", "azure", "ollama"],
        "default_priority": "quality"  # Optimize for quality
    }
}

# Task-specific provider recommendations
TASK_PROVIDER_RECOMMENDATIONS = {
    "chat": ["openai", "anthropic", "google"],
    "analysis": ["openai", "anthropic", "xai"],
    "summarization": ["groq", "mistral", "together"],
    "extraction": ["openai", "anthropic", "mistral"],
    "classification": ["mistral", "together", "google"],
    "generation": ["openai", "anthropic", "xai"],
    "translation": ["google", "openai", "mistral"],
    "research": ["perplexity", "openai", "anthropic"]
}

# Default embedding providers by priority
DEFAULT_EMBEDDING_PROVIDERS = ["openai", "google", "mistral", "together"]

# Feature flags
ENABLE_PROVIDER_FALLBACK = True  # Enable automatic fallback to alternative providers
ENABLE_COST_OPTIMIZATION = True  # Enable cost optimization strategies
ENABLE_STREAMING = True  # Enable streaming responses when supported
ENABLE_PROVIDER_HEALTH_MONITORING = True  # Enable health monitoring for providers

# Default settings
DEFAULT_PROVIDER = "openrouter"  # Default provider to use
DEFAULT_PRIORITY = "balanced"  # Default priority for provider selection
DEFAULT_USER_TIER = "pro_tier"  # Default user tier
DEFAULT_TASK_TYPE = "chat"  # Default task type

# Function to get provider setting
def get_provider_setting(
    provider: str, 
    setting: str, 
    default: Any = None
) -> Any:
    """
    Get a setting for a provider.
    
    Args:
        provider: Provider name
        setting: Setting name
        default: Default value if setting not found
        
    Returns:
        Setting value or default
    """
    provider_config = PROVIDER_SETTINGS.get(provider, {})
    return provider_config.get(setting, default)

# Function to get model for provider and task
def get_provider_model(
    provider: str, 
    model_type: str = "chat"
) -> Optional[str]:
    """
    Get the model for a provider and model type.
    
    Args:
        provider: Provider name
        model_type: Model type (chat, fallback, embedding)
        
    Returns:
        Model name or None if not available
    """
    provider_config = PROVIDER_SETTINGS.get(provider, {})
    models = provider_config.get("models", {})
    return models.get(model_type)

# Function to get recommended providers for a task
def get_recommended_providers(
    task_type: str, 
    user_tier: str = "pro_tier"
) -> List[str]:
    """
    Get recommended providers for a task type.
    
    Args:
        task_type: Task type
        user_tier: User subscription tier
        
    Returns:
        List of recommended provider names
    """
    # Get task-specific recommendations
    recommended = TASK_PROVIDER_RECOMMENDATIONS.get(task_type, [])
    
    # Filter by allowed providers for user tier
    allowed = USER_TIER_LIMITS.get(user_tier, {}).get("allowed_providers", [])
    
    # Return intersection of recommended and allowed providers
    return [provider for provider in recommended if provider in allowed] 

--- app/config/settings.py.txt ---
"""
Configuration settings for the AI Context Engineering Agent.
"""
import os
import json
from typing import List, Optional, Union, Any, Dict

from pydantic import AnyHttpUrl, Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "AI Context Engineering Agent"
    
    # CORS Configuration
    CORS_ORIGINS: List[Union[str, AnyHttpUrl]] = ["http://localhost:3000", "http://localhost:8000"]
    
    # Environment
    ENV: str = Field(default="dev", env="ENV")
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    LOG_LEVEL: str = Field(default="info", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")
    LOG_FILE: str = Field(default="/app/logs/app.log", env="LOG_FILE")
    
    # Neo4j Configuration
    NEO4J_HOST: str = Field(default="neo4j", env="NEO4J_HOST")
    NEO4J_PORT: int = Field(default=7687, env="NEO4J_PORT")
    NEO4J_USER: str = Field(default="neo4j", env="NEO4J_USER")
    NEO4J_USERNAME: str = Field(default="neo4j", env="NEO4J_USERNAME")
    NEO4J_PASSWORD: str = Field(default="password", env="NEO4J_PASSWORD")
    NEO4J_URI: str = Field(default="bolt://neo4j:7687", env="NEO4J_URI")
    GRAPH_DATABASE_PROVIDER: str = Field(default="neo4j", env="GRAPH_DATABASE_PROVIDER")
    
    # Qdrant Configuration
    QDRANT_HOST: str = Field(default="qdrant", env="QDRANT_HOST")
    QDRANT_PORT: int = Field(default=6333, env="QDRANT_PORT")
    QDRANT_COLLECTION: str = Field(default="context_vectors", env="QDRANT_COLLECTION")
    VECTOR_DB_PROVIDER: str = Field(default="qdrant", env="VECTOR_DB_PROVIDER")
    VECTOR_DB_URL: str = Field(default="http://qdrant:6333", env="VECTOR_DB_URL")
    
    # Database Configuration
    DB_PROVIDER: str = Field(default="postgres", env="DB_PROVIDER")
    DB_HOST: str = Field(default="postgres", env="DB_HOST")
    DB_PORT: str = Field(default="5432", env="DB_PORT")
    DB_USERNAME: str = Field(default="postgres", env="DB_USERNAME")
    DB_PASSWORD: str = Field(default="postgres", env="DB_PASSWORD")
    DB_NAME: str = Field(default="document_processor", env="DB_NAME")
    DB_SSL_MODE: str = Field(default="disable", env="DB_SSL_MODE")
    
    # Redis Configuration
    REDIS_URL: str = Field(default="redis://redis:6379/0", env="REDIS_URL")
    
    # Celery Configuration
    CELERY_BROKER_URL: str = Field(default="redis://redis:6379/0", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://redis:6379/0", env="CELERY_RESULT_BACKEND")
    CELERY_TASK_SERIALIZER: str = Field(default="json", env="CELERY_TASK_SERIALIZER")
    CELERY_RESULT_SERIALIZER: str = Field(default="json", env="CELERY_RESULT_SERIALIZER")
    CELERY_ACCEPT_CONTENT: List[str] = Field(default=["json"], env="CELERY_ACCEPT_CONTENT")
    CELERY_TIMEZONE: str = Field(default="UTC", env="CELERY_TIMEZONE")
    CELERY_ENABLE_UTC: bool = Field(default=True, env="CELERY_ENABLE_UTC")
    
    # LLM Configuration
    OPENAI_API_KEY: Optional[str] = Field(None, env="OPENAI_API_KEY")
    ANTHROPIC_API_KEY: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    PERPLEXITY_API_KEY: Optional[str] = Field(None, env="PERPLEXITY_API_KEY")
    GOOGLE_API_KEY: Optional[str] = Field(None, env="GOOGLE_API_KEY")
    MISTRAL_API_KEY: Optional[str] = Field(None, env="MISTRAL_API_KEY")
    XAI_API_KEY: Optional[str] = Field(None, env="XAI_API_KEY")
    AZURE_OPENAI_API_KEY: Optional[str] = Field(None, env="AZURE_OPENAI_API_KEY")
    OLLAMA_API_KEY: Optional[str] = Field(None, env="OLLAMA_API_KEY")
    GITHUB_API_KEY: Optional[str] = Field(None, env="GITHUB_API_KEY")
    LLM_API_KEY: Optional[str] = Field(None, env="LLM_API_KEY")
    LLM_PROVIDER: str = Field(default="openai", env="LLM_PROVIDER")
    LLM_MODEL: str = Field(default="gpt-4o", env="LLM_MODEL")
    LLM_ENDPOINT: Optional[str] = Field(None, env="LLM_ENDPOINT")
    DEFAULT_LLM_MODEL: str = Field(default="gpt-4o", env="DEFAULT_LLM_MODEL")
    EMBEDDING_MODEL: str = Field(default="text-embedding-3-large", env="EMBEDDING_MODEL")
    
    # Context Engineering Configuration
    MAX_CONTEXT_WINDOW: int = Field(default=128000, env="MAX_CONTEXT_WINDOW")
    COMPRESSION_RATIO: float = Field(default=0.5, env="COMPRESSION_RATIO")
    CHUNK_SIZE: int = Field(default=1024, env="CHUNK_SIZE")
    CHUNK_OVERLAP: int = Field(default=128, env="CHUNK_OVERLAP")
    
    # File Processing
    MAX_FILE_SIZE: int = Field(default=104857600, env="MAX_FILE_SIZE")  # 100 MB
    UPLOAD_PATH: str = Field(default="/app/uploads", env="UPLOAD_PATH")
    PROCESSED_PATH: str = Field(default="/app/processed", env="PROCESSED_PATH")
    MAX_CONCURRENT_UPLOADS: int = Field(default=10, env="MAX_CONCURRENT_UPLOADS")
    SUPPORTED_FILE_TYPES: List[str] = Field(
        default=["json", "csv", "txt", "md", "pdf", "png", "jpg", "jpeg"], 
        env="SUPPORTED_FILE_TYPES"
    )
    
    # Security & Authentication
    SECRET_KEY: str = Field(default="UC38hxSfHk81WSvF0HGtkM2GY02lz6qeQN4wvtATJI8", env="SECRET_KEY")
    JWT_SECRET_KEY: str = Field(default="UC38hxSfHk81WSvF0HGtkM2GY02lz6qeQN4wvtATJI8", env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    ALGORITHM: str = Field(default="HS256", env="ALGORITHM")
    ENABLE_HSTS: bool = Field(default=False, env="ENABLE_HSTS")
    CSP_POLICY_OVERRIDES: Dict[str, Any] = Field(default_factory=dict, env="CSP_POLICY_OVERRIDES")
    
    # Email Configuration
    EMAIL_PROVIDER: str = Field(default="console", env="EMAIL_PROVIDER")  # console, sendgrid, ses
    SENDGRID_API_KEY: Optional[str] = Field(None, env="SENDGRID_API_KEY")
    SENDGRID_FROM_EMAIL: str = Field(default="<EMAIL>", env="SENDGRID_FROM_EMAIL")
    AWS_SES_REGION: str = Field(default="us-east-1", env="AWS_SES_REGION")
    AWS_ACCESS_KEY_ID: Optional[str] = Field(None, env="AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY: Optional[str] = Field(None, env="AWS_SECRET_ACCESS_KEY")
    RESEND_API_KEY: Optional[str] = Field(None, env="RESEND_API_KEY")

    # OTP settings
    OTP_EXPIRY_MINUTES: int = 10
    MAX_OTP_ATTEMPTS: int = 5
    OTP_RATE_LIMIT_WINDOW: int = 60  # in seconds
    MAX_OTP_REQUESTS_PER_HOUR: int = 5
    
    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    LOGIN_RATE_LIMIT: str = Field(default="5/minute", env="LOGIN_RATE_LIMIT")
    REGISTER_RATE_LIMIT: str = Field(default="3/hour", env="REGISTER_RATE_LIMIT")
    VERIFY_EMAIL_RATE_LIMIT: str = Field(default="5/minute", env="VERIFY_EMAIL_RATE_LIMIT")
    PASSWORD_RESET_RATE_LIMIT: str = Field(default="3/hour", env="PASSWORD_RESET_RATE_LIMIT")
    
    # Metrics
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=9090, env="METRICS_PORT")
    
    @validator("CSP_POLICY_OVERRIDES", pre=True)
    def assemble_csp_policy_overrides(cls, v: Union[str, Dict]) -> Dict:
        """Parse CSP policy overrides from a JSON string."""
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return {}
        return v

    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            if not v.startswith("["):
                return [i.strip() for i in v.split(",")]
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return [i.strip() for i in v.split(",")]
        return v

    @validator("SUPPORTED_FILE_TYPES", pre=True)
    def assemble_supported_file_types(cls, v: Union[str, List[str]]) -> List[str]:
        """Parse supported file types from string or list."""
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return [i.strip() for i in v.split(",")]
        return v

    @validator("CELERY_ACCEPT_CONTENT", pre=True)
    def assemble_celery_accept_content(cls, v: Union[str, List[str]]) -> List[str]:
        """Parse Celery accept content from string or list."""
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return [i.strip() for i in v.split(",")]
        return v

    class Config:
        """Pydantic config."""
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Allow extra fields


# Create settings instance
settings = Settings() 

--- app/core/auth_dependencies.py.txt ---
"""
Authentication dependencies for ConTXT API.
Provides dependency injection for user authentication and authorization.
"""
import jwt
import asyncpg
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from typing import Dict, Any, Optional
import logging

from app.config.settings import settings

logger = logging.getLogger(__name__)
security = HTTPBearer()

# Database connection dependency (to be implemented based on your DB setup)
async def get_db_pool() -> asyncpg.Pool:
    """Get database connection pool."""
    # This should return your actual database pool instance
    # You'll need to implement this based on your existing DB setup
    from app.db.postgres_client import get_db_pool as get_pool
    return await get_pool()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> Dict[str, Any]:
    """Get current authenticated user from JWT token."""
    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        if payload.get("type") != "access":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Get user from database
        async with db_pool.acquire() as conn:
            user = await conn.fetchrow("""
                SELECT id, email, first_name, last_name, full_name, 
                       subscription_tier, is_verified, is_active,
                       created_at, last_login_at
                FROM users 
                WHERE id = $1 AND is_active = true
            """, user_id)
            
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found or inactive",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            return dict(user)
            
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_verified_user(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """Require verified user."""
    if not current_user.get('is_verified'):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email verification required"
        )
    return current_user

async def get_premium_user(
    current_user: Dict[str, Any] = Depends(get_verified_user)
) -> Dict[str, Any]:
    """Require premium subscription."""
    if current_user.get('subscription_tier') == 'free':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Premium subscription required"
        )
    return current_user

async def get_enterprise_user(
    current_user: Dict[str, Any] = Depends(get_verified_user)
) -> Dict[str, Any]:
    """Require enterprise subscription."""
    if current_user.get('subscription_tier') != 'enterprise':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Enterprise subscription required"
        )
    return current_user

async def get_optional_user(
    request: Request,
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> Optional[Dict[str, Any]]:
    """Get user if authenticated, otherwise return None."""
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return None
        
        token = auth_header.split(" ")[1]
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        if payload.get("type") != "access":
            return None
        
        user_id = payload.get("sub")
        if not user_id:
            return None
        
        async with db_pool.acquire() as conn:
            user = await conn.fetchrow("""
                SELECT id, email, first_name, last_name, subscription_tier, 
                       is_verified, is_active
                FROM users 
                WHERE id = $1 AND is_active = true
            """, user_id)
            
            return dict(user) if user else None
            
    except (jwt.JWTError, Exception) as e:
        logger.debug(f"Optional auth failed: {e}")
        return None

async def verify_api_key(
    request: Request,
    db_pool: asyncpg.Pool = Depends(get_db_pool)
) -> Dict[str, Any]:
    """Verify API key authentication."""
    api_key = request.headers.get("X-API-Key")
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required"
        )
    
    if not api_key.startswith("ctx_"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key format"
        )
    
    async with db_pool.acquire() as conn:
        # Find API key and associated user
        result = await conn.fetchrow("""
            SELECT ak.id, ak.user_id, ak.permissions, ak.expires_at,
                   u.email, u.subscription_tier, u.is_active, u.is_verified
            FROM user_api_keys ak
            JOIN users u ON ak.user_id = u.id
            WHERE ak.api_key = $1 AND ak.is_active = true AND u.is_active = true
        """, api_key)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid API key"
            )
        
        # Check expiration
        if result['expires_at'] and result['expires_at'] < datetime.utcnow():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key expired"
            )
        
        # Update usage
        await conn.execute("""
            UPDATE user_api_keys 
            SET last_used_at = now(), usage_count = usage_count + 1
            WHERE id = $1
        """, result['id'])
        
        return {
            "user_id": str(result['user_id']),
            "email": result['email'],
            "subscription_tier": result['subscription_tier'],
            "permissions": result['permissions'],
            "auth_type": "api_key"
        }

def require_permission(permission: str):
    """Decorator to require specific permission."""
    def permission_checker(current_user: Dict[str, Any] = Depends(get_current_user)):
        # For JWT auth, assume all permissions for now
        # You can implement more granular permissions later
        return current_user
    return permission_checker

def require_api_permission(permission: str):
    """Decorator to require specific API key permission."""
    def permission_checker(api_user: Dict[str, Any] = Depends(verify_api_key)):
        if permission not in api_user.get('permissions', []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return api_user
    return permission_checker

# Rate limiting helper
def get_client_ip(request: Request) -> str:
    """Get client IP address from request."""
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    return request.client.host if request.client else "unknown"

def get_user_agent(request: Request) -> str:
    """Get user agent from request."""
    return request.headers.get("User-Agent", "unknown")


--- app/core/auth.py.txt ---
# app/core/auth.py
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import OAuth2PasswordBearer

class AuthConfig:
    # THIS IS A DEMO KEY. IN PRODUCTION, PULL FROM ENVIRONMENT VARIABLES.
    SECRET_KEY = "your-super-secret-key-change-in-production"
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    REFRESH_TOKEN_EXPIRE_DAYS = 7

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/login")

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=AuthConfig.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, AuthConfig.SECRET_KEY, algorithm=AuthConfig.ALGORITHM)
    return encoded_jwt

def create_refresh_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=AuthConfig.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, AuthConfig.SECRET_KEY, algorithm=AuthConfig.ALGORITHM)
    return encoded_jwt

def verify_token(token: str, credentials_exception):
    try:
        payload = jwt.decode(token, AuthConfig.SECRET_KEY, algorithms=[AuthConfig.ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        return user_id
    except JWTError:
        raise credentials_exception

def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    return verify_token(token, credentials_exception)


--- app/core/auth_service.py.txt ---
"""
Authentication service for ConTXT API.
Handles user registration, login, email verification, password reset, and JWT token management.
"""
import asyncio
import secrets
import logging
import bcrypt

logger = logging.getLogger(__name__)
import jwt
from datetime import datetime, timedelta, timezone
import json
from typing import Dict, Any, Optional, List
from fastapi import HTTPException, status
import asyncpg
import logging

from app.config.settings import settings
from app.schemas.auth import (
    UserRegistration, UserLogin, TokenResponse, PasswordReset, 
    PasswordChange, UserProfile, UserProfileUpdate, ApiKeyCreate, ApiKeyResponse,
    OTPVerification, OTPRequest, PasswordResetOTP, RefreshTokenRequest
)
from app.core.otp_service import OTPService
from app.core.email_service import EmailService

logger = logging.getLogger(__name__)

class AuthService:
    """Authentication service with comprehensive user management."""
    
    def __init__(self, db_pool: asyncpg.Pool, otp_service: OTPService, email_service: EmailService):
        self.db_pool = db_pool
        self.otp_service = otp_service
        self.email_service = email_service
        self.jwt_secret = settings.JWT_SECRET_KEY
        self.jwt_algorithm = settings.JWT_ALGORITHM
        self.access_token_expire = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    async def register_user(self, user_data: UserRegistration) -> Dict[str, Any]:
        """Register new user with email verification."""
        async with self.db_pool.acquire() as conn:
            # Check if user exists
            existing_user = await conn.fetchrow(
                "SELECT id FROM users WHERE email = $1", user_data.email
            )
            if existing_user:
                await self._log_auth_event(conn, None, "registration", "failure", additional_data={"reason": "email_already_registered"})
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
            
            # Hash password
            password_hash = bcrypt.hashpw(
                user_data.password.encode('utf-8'), 
                bcrypt.gensalt()
            ).decode('utf-8')
            
            # Create user as inactive until verified
            user_id = await conn.fetchval("""
                INSERT INTO users (email, password_hash, first_name, last_name, is_active, is_verified)
                VALUES ($1, $2, $3, $4, false, false)
                RETURNING id
            """, user_data.email, password_hash, user_data.first_name, user_data.last_name)
            
            # Create default user preferences
            await conn.execute("""
                INSERT INTO user_preferences (user_id)
                VALUES ($1)
            """, user_id)

            try:
                # Request OTP via OTPService
                await self.otp_service.send_verification_otp(
                    email=user_data.email, user_id=user_id
                )
                
                # Log registration event
                await self._log_auth_event(
                    conn, user_id, "registration", "success", 
                    additional_data={"email": user_data.email}
                )
                
                return {
                    "user_id": str(user_id),
                    "message": "Registration successful. Please check your email for verification.",
                    "verification_required": True
                }
            except Exception as e:
                logger.error(f"Error during OTP request for {user_data.email}: {e}")
                # Rollback user creation by deleting the user
                await conn.execute("DELETE FROM users WHERE id = $1", user_id)
                # Log the detailed error
                logger.error(f"Registration failed for {user_data.email}: {e}", exc_info=True)
                # Re-raise with a more informative message for debugging
                await self._log_auth_event(conn, None, "registration", "failure", additional_data={"reason": "otp_request_error", "error": str(e)})
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to send verification email: {str(e)}",
                )
    
    async def login_user(self, login_data: UserLogin, ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> TokenResponse:
        """Authenticate user and return JWT tokens."""
        async with self.db_pool.acquire() as conn:
            user = await conn.fetchrow("""
                SELECT id, email, password_hash, is_active, is_verified, 
                       first_name, subscription_tier, failed_login_attempts, locked_until
                FROM users WHERE email = $1
            """, login_data.email)
            
            if not user:
                await self._log_auth_event(
                    conn, None, "login", "failure", 
                    additional_data={"email": login_data.email, "reason": "user_not_found"}
                )
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )
            
            # Check if account is locked
            if user['locked_until'] and user['locked_until'] > datetime.now(timezone.utc):
                await self._log_auth_event(
                    conn, user['id'], "login", "blocked", 
                    additional_data={"reason": "account_locked"}
                )
                raise HTTPException(
                    status_code=status.HTTP_423_LOCKED,
                    detail="Account is temporarily locked due to failed login attempts"
                )
            
            # Verify password
            if not bcrypt.checkpw(
                login_data.password.encode('utf-8'), 
                user['password_hash'].encode('utf-8')
            ):
                # Increment failed attempts
                failed_attempts = user['failed_login_attempts'] + 1
                locked_until = None
                
                if failed_attempts >= 5:
                    locked_until = datetime.now(timezone.utc) + timedelta(minutes=30)
                    # Send account locked email
                    try:
                        await self.email_service.send_account_locked_email(user['email'], user['first_name'])
                    except Exception as e:
                        logger.error(f"Failed to send account locked email to {user['email']}: {e}")

                await conn.execute("""
                    UPDATE users 
                    SET failed_login_attempts = $1, locked_until = $2
                    WHERE id = $3
                """, failed_attempts, locked_until, user['id'])
                
                await self._log_auth_event(
                    conn, user['id'], "login", "failure", 
                    additional_data={"reason": "invalid_password", "failed_attempts": failed_attempts}
                )
                
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )
            
            if not user['is_active']:
                await self._log_auth_event(
                    conn, user['id'], "login", "blocked", 
                    additional_data={"reason": "account_inactive"}
                )
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Account is deactivated"
                )
            
            # Reset failed attempts on successful login
            await conn.execute("""
                UPDATE users 
                SET failed_login_attempts = 0, locked_until = NULL, 
                    last_login_at = now(), login_count = login_count + 1
                WHERE id = $1
            """, user['id'])
            
            # Generate tokens
            access_token = self._create_access_token(
                data={"sub": str(user['id']), "email": user['email']}
            )
            refresh_token = self._create_refresh_token(
                data={"sub": str(user['id'])}
            )
            
            # Store refresh token session
            refresh_token_hash = bcrypt.hashpw(
                refresh_token.encode('utf-8'), bcrypt.gensalt()
            ).decode('utf-8')
            
            await conn.execute("""
                INSERT INTO user_sessions (user_id, refresh_token_hash, ip_address, user_agent, expires_at)
                VALUES ($1, $2, $3, $4, $5)
            """, user['id'], refresh_token_hash, ip_address, user_agent, 
                datetime.now(timezone.utc) + timedelta(days=self.refresh_token_expire))
            
            # Log successful login
            await self._log_auth_event(
                conn, user['id'], "login", "success", 
                additional_data={"ip_address": ip_address}
            )
            
            return TokenResponse(
                access_token=access_token,
                refresh_token=refresh_token,
                expires_in=self.access_token_expire * 60,
                user_id=str(user['id'])
            )
    
    async def verify_email(self, verification_data: OTPVerification) -> Dict[str, str]:
        """Verify user's email using an OTP and activate the account."""
        async with self.db_pool.acquire() as conn:
            user = await conn.fetchrow("SELECT id FROM users WHERE email = $1", verification_data.email)
            if not user:
                await self._log_auth_event(conn, None, "email_verification", "failure", additional_data={"reason": "user_not_found"})
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

            try:
                verification_result = await self.otp_service.verify_email_otp(
                    email=verification_data.email,
                    otp_code=verification_data.otp_code
                )
                if not verification_result.get("success"):
                    raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=verification_result.get("message"))
            except HTTPException as e:
                await self._log_auth_event(conn, user['id'], "email_verification", "failure", additional_data={"reason": e.detail})
                raise e

            # If verification is successful, proceed
                await self._log_auth_event(conn, user['id'], "email_verification", "failure", additional_data={"reason": "invalid_otp"})
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired OTP"
                )

            await conn.execute(
                "UPDATE users SET is_verified = true, is_active = true WHERE email = $1",
                verification_data.email
            )
            await self._log_auth_event(conn, user['id'], "email_verification", "success")

        return {"message": "Email verified successfully. Your account is now active."}
    
    async def request_password_reset(self, otp_request: OTPRequest) -> Dict[str, str]:
        """Request a password reset OTP."""
        async with self.db_pool.acquire() as conn:
            # We don't check for user existence to prevent email enumeration attacks
            await self.otp_service.send_password_reset_otp(email=otp_request.email)
            await self._log_auth_event(conn, None, "password_reset_request", "success", additional_data={"email": otp_request.email})
        return {"message": "If an account with that email exists, an OTP has been sent."}
    
    async def reset_password_with_otp(self, reset_data: PasswordResetOTP) -> Dict[str, str]:
        """Reset password using an OTP."""
        async with self.db_pool.acquire() as conn:
            user = await conn.fetchrow("SELECT id FROM users WHERE email = $1", reset_data.email)
            # First, verify the OTP is valid
            try:
                verification_result = await self.otp_service.verify_password_reset_otp(
                    email=reset_data.email,
                    otp_code=reset_data.otp_code
                )
                if not verification_result.get("success"):
                    raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=verification_result.get("message"))
            except HTTPException as e:
                if user:
                    await self._log_auth_event(conn, user['id'], "password_reset", "failure", additional_data={"reason": e.detail})
                raise e

            # If OTP is valid, proceed
                if user:
                    await self._log_auth_event(conn, user['id'], "password_reset", "failure", additional_data={"reason": "invalid_otp"})
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired OTP."
                )

            # If OTP is valid, proceed with password reset
            if not user:
                # This case should ideally not be hit if OTP verification passed
                await self._log_auth_event(conn, None, "password_reset", "failure", additional_data={"reason": "user_not_found_post_otp"})
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

            # Hash new password
            password_hash = bcrypt.hashpw(
                reset_data.new_password.encode('utf-8'), 
                bcrypt.gensalt()
            ).decode('utf-8')
            
            # Update password and reset failed attempts
            await conn.execute("""
                UPDATE users 
                SET password_hash = $1, failed_login_attempts = 0, locked_until = NULL
                WHERE id = $2
            """, password_hash, user['id'])
            
            # Invalidate all user sessions for security
            await conn.execute("""
                UPDATE user_sessions SET is_active = false WHERE user_id = $1
            """, user['id'])
            
            await self._log_auth_event(conn, user['id'], "password_reset", "success")
            
            return {"message": "Password has been reset successfully."}
    
    async def change_password(self, user_id: str, password_data: PasswordChange) -> Dict[str, str]:
        """Change user password."""
        async with self.db_pool.acquire() as conn:
            # Get current password hash
            user = await conn.fetchrow(
                "SELECT password_hash FROM users WHERE id = $1", user_id
            )
            
            if not user:
                await self._log_auth_event(conn, None, "password_change", "failure", additional_data={"reason": "user_not_found"})
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            # Verify current password
            if not bcrypt.checkpw(
                password_data.current_password.encode('utf-8'),
                user['password_hash'].encode('utf-8')
            ):
                await self._log_auth_event(conn, user_id, "password_change", "failure", additional_data={"reason": "incorrect_password"})
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Current password is incorrect"
                )
            
            # Hash new password
            new_password_hash = bcrypt.hashpw(
                password_data.new_password.encode('utf-8'),
                bcrypt.gensalt()
            ).decode('utf-8')
            
            # Update password
            await conn.execute("""
                UPDATE users SET password_hash = $1 WHERE id = $2
            """, new_password_hash, user_id)
            
            # Log password change
            await self._log_auth_event(
                conn, user_id, "password_change", "success"
            )
            
            return {"message": "Password changed successfully"}
    
    async def refresh_access_token(self, refresh_token: str) -> TokenResponse:
        """Refresh JWT access token."""
        try:
            payload = jwt.decode(
                refresh_token, self.jwt_secret, algorithms=[self.jwt_algorithm]
            )
            
            if payload.get("type") != "refresh":
                # No user_id available here, but we can log the attempt
                async with self.db_pool.acquire() as conn:
                    await self._log_auth_event(conn, None, "refresh_token", "failure", additional_data={"reason": "invalid_token_type"})
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token type"
                )
            
            user_id = payload.get("sub")
            if not user_id:
                async with self.db_pool.acquire() as conn:
                    await self._log_auth_event(conn, None, "refresh_token", "failure", additional_data={"reason": "missing_sub_in_payload"})
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
            
            async with self.db_pool.acquire() as conn:
                # Verify refresh token exists and is active
                session = await conn.fetchrow("""
                    SELECT s.id, u.email, u.subscription_tier
                    FROM user_sessions s
                    JOIN users u ON s.user_id = u.id
                    WHERE s.user_id = $1 AND s.is_active = true AND s.expires_at > now()
                """, user_id)
                
                if not session:
                    await self._log_auth_event(conn, user_id, "refresh_token", "failure", additional_data={"reason": "session_not_found_or_inactive"})
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Invalid or expired refresh token"
                    )
                
                # Generate new access token
                access_token = self._create_access_token(
                    data={"sub": user_id, "email": session['email']}
                )
                
                # Update session last accessed
                await conn.execute("""
                    UPDATE user_sessions 
                    SET last_accessed_at = now()
                    WHERE id = $1
                """, session['id'])
                
                await self._log_auth_event(conn, user_id, "refresh_token", "success")
                return TokenResponse(
                    access_token=access_token,
                    refresh_token=refresh_token,  # Keep same refresh token
                    expires_in=self.access_token_expire * 60,
                    user_id=user_id
                )
                
        except jwt.ExpiredSignatureError as e:
            async with self.db_pool.acquire() as conn:
                await self._log_auth_event(conn, None, "refresh_token", "failure", additional_data={"reason": "expired_signature", "error": str(e)})
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Refresh token expired"
            )
        except jwt.JWTError as e:
            async with self.db_pool.acquire() as conn:
                await self._log_auth_event(conn, None, "refresh_token", "failure", additional_data={"reason": "jwt_error", "error": str(e)})
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )

    async def logout_user(self, user_id: str, refresh_token: Optional[str] = None) -> Dict[str, str]:
        """Logout user and invalidate sessions."""
        async with self.db_pool.acquire() as conn:
            if refresh_token:
                # Invalidate specific session by finding the matching hashed refresh token
                # This is inefficient and assumes we can find the salt. A better design would be a session ID.
                # For now, we will invalidate all sessions for simplicity when a token is provided.
                pass # Fall through to invalidate all for now

            # Invalidate all active sessions for the user
            await conn.execute("""
                UPDATE user_sessions 
                SET is_active = false
                WHERE user_id = $1 AND is_active = true
            """, user_id)
            
            await self._log_auth_event(conn, user_id, "logout", "success")
            
        return {"message": "Logged out successfully"}

    async def create_api_key(self, user_id: str, api_key_data: ApiKeyCreate) -> ApiKeyResponse:
        """Create a new API key for a user."""
        # Placeholder for API key creation logic
        raise NotImplementedError("API key creation is not yet implemented.")
    async def _log_auth_event(
        self,
        conn: asyncpg.Connection,
        user_id: Optional[str],
        event_type: str,
        status: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None,
    ):
        """Log an authentication event to the database."""
        try:
            await conn.execute(
                """
                INSERT INTO auth_events (user_id, event_type, status, ip_address, user_agent, additional_data)
                VALUES ($1, $2, $3, $4, $5, $6::jsonb)
                """,
                user_id,
                event_type,
                status,
                ip_address,
                user_agent,
                json.dumps(additional_data) if additional_data else None
            )
        except Exception as e:
            logger.error(f"Failed to log auth event for user {user_id}: {e}", exc_info=True)

    def _create_access_token(self, data: dict) -> str:
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(minutes=self.access_token_expire)
        to_encode.update({"exp": expire, "type": "access"})
        return jwt.encode(to_encode, self.jwt_secret, algorithm=self.jwt_algorithm)

    def _create_refresh_token(self, data: dict) -> str:
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(days=self.refresh_token_expire)
        to_encode.update({"exp": expire, "type": "refresh"})
        return jwt.encode(to_encode, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    async def _send_password_reset_email(self, email: str, token: str):
        """Send password reset email."""
        reset_url = f"https://contxt.ai/reset-password?token={token}"
        logger.info(f"Send reset email to {email}: {reset_url}")
        # TODO: Implement with SendGrid, AWS SES, or other email service


--- app/core/context_engine.py.txt ---
"""
Context Engineering Engine.

This module implements the core context engineering functionality, including:
- Content selection from various sources
- Context compression and optimization
- Context ordering and structuring
- System prompt generation

The context engineering process follows a deliberate, modular approach to
ensure optimal context window usage and prevent overload.
"""
import uuid
from typing import Dict, List, Optional, Any, Union

from langgraph.graph import StateGraph
import litellm

from app.schemas.context import Source, ContextBlock, ContextResponse
from app.db.neo4j_client import Neo4jClient
from app.db.qdrant_client import QdrantClient

class ContextEngine:
    """
    Core engine for context engineering operations.
    
    This class implements the context engineering workflow using LangGraph
    for orchestrating the multi-step process of context curation.
    """
    
    def __init__(self):
        """Initialize the context engine."""
        self.neo4j_client = Neo4jClient()
        self.qdrant_client = QdrantClient()
        self.context_graph = self._build_context_graph()
    
    def _build_context_graph(self) -> StateGraph:
        """
        Build the LangGraph workflow for context engineering.
        
        This creates a graph with nodes for each step of the context
        engineering process, allowing for a deliberate, modular approach.
        """
        # Define the state schema
        from typing import TypedDict, List
        
        class ContextState(TypedDict):
            sources: List[Dict[str, Any]]
            selected_content: List[Dict[str, Any]]
            compressed_content: List[Dict[str, Any]]
            ordered_content: List[Dict[str, Any]]
            final_context: List[ContextBlock]
            metadata: Dict[str, Any]
        
        # Create the graph
        builder = StateGraph(ContextState)
        
        # Add nodes for each step
        builder.add_node("select_content", self._select_relevant_content)
        builder.add_node("compress_content", self._compress_content)
        builder.add_node("order_content", self._order_content)
        builder.add_node("structure_output", self._structure_output)
        
        # Define the edges
        builder.add_edge("select_content", "compress_content")
        builder.add_edge("compress_content", "order_content")
        builder.add_edge("order_content", "structure_output")
        
        # Build and return the graph
        return builder.compile()
    
    async def _select_relevant_content(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Select relevant content from sources based on context needs.
        
        This step implements the first context engineering principle:
        deliberate selection of knowledge to avoid overload.
        """
        sources = state["sources"]
        selected_content = []
        
        for source in sources:
            # Implement selection logic based on source type
            if source["source_type"] == "url":
                # Select content from URL
                pass
            elif source["source_type"] == "file":
                # Select content from file
                pass
            elif source["source_type"] == "text":
                # Select content from raw text
                selected_content.append({
                    "content": source["content"],
                    "source_id": source.get("source_id"),
                    "metadata": source.get("metadata", {})
                })
        
        # Update state with selected content
        state["selected_content"] = selected_content
        return state
    
    async def _compress_content(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compress content to fit within context window limits.
        
        This step implements the second context engineering principle:
        compression for efficiency without losing critical information.
        """
        selected_content = state["selected_content"]
        compressed_content = []
        
        for content in selected_content:
            # Implement compression logic (e.g., summarization)
            compressed_content.append({
                "content": content["content"],  # Replace with actual compression
                "source_id": content.get("source_id"),
                "metadata": content.get("metadata", {})
            })
        
        # Update state with compressed content
        state["compressed_content"] = compressed_content
        return state
    
    async def _order_content(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Order content by relevance and importance.
        
        This step implements the third context engineering principle:
        strategic ordering for optimal consumption by AI models.
        """
        compressed_content = state["compressed_content"]
        
        # Implement ordering logic (e.g., by relevance score)
        ordered_content = sorted(
            compressed_content,
            key=lambda x: x.get("metadata", {}).get("relevance_score", 0),
            reverse=True
        )
        
        # Update state with ordered content
        state["ordered_content"] = ordered_content
        return state
    
    async def _structure_output(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Structure the final context output.
        
        This step implements the fourth context engineering principle:
        structured outputs for optimal consumption.
        """
        ordered_content = state["ordered_content"]
        final_context = []
        
        for idx, content in enumerate(ordered_content):
            block = ContextBlock(
                block_id=f"block_{idx}",
                block_type="text",  # Determine type based on content
                content=content["content"],
                source_id=content.get("source_id"),
                metadata=content.get("metadata"),
                relevance_score=content.get("metadata", {}).get("relevance_score")
            )
            final_context.append(block)
        
        # Update state with final context
        state["final_context"] = final_context
        return state
    
    async def build_context(
        self,
        sources: List[Source],
        max_tokens: int = 128000,
        compression_ratio: float = 0.5
    ) -> ContextResponse:
        """
        Build engineered context from provided sources.
        
        This method orchestrates the full context engineering workflow,
        from source selection to final structured output.
        
        Args:
            sources: List of content sources
            max_tokens: Maximum tokens for context window
            compression_ratio: Target compression ratio
            
        Returns:
            ContextResponse with engineered context blocks
        """
        # Initialize state
        state = {
            "sources": [source.model_dump() for source in sources],
            "selected_content": [],
            "compressed_content": [],
            "ordered_content": [],
            "final_context": [],
            "metadata": {
                "max_tokens": max_tokens,
                "compression_ratio": compression_ratio
            }
        }
        
        # Execute the graph
        final_state = await self.context_graph.ainvoke(state)
        
        # Create response
        context_id = str(uuid.uuid4())
        token_count = sum(len(block.content.split()) for block in final_state["final_context"])
        
        return ContextResponse(
            context_id=context_id,
            blocks=final_state["final_context"],
            token_count=token_count,
            compression_ratio=compression_ratio,
            metadata=final_state["metadata"]
        )
    
    async def generate_system_prompt(
        self,
        context_id: str,
        tool_type: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Generate a system prompt based on engineered context.
        
        Args:
            context_id: ID of the built context
            tool_type: Target tool type (cursor, windsurf, etc.)
            parameters: Additional parameters
            
        Returns:
            Generated system prompt as string
        """
        # Placeholder implementation
        # In a real implementation, this would:
        # 1. Retrieve the context blocks from storage
        # 2. Format them according to the tool type
        # 3. Generate a tailored system prompt
        
        return f"System prompt for {tool_type} based on context {context_id}"
    
    async def get_context_status(self, context_id: str) -> Dict[str, Any]:
        """
        Get the status and metadata of a context building operation.
        
        Args:
            context_id: ID of the context
            
        Returns:
            Status information as dictionary
        """
        # Placeholder implementation
        return {
            "context_id": context_id,
            "status": "completed",
            "created_at": "2023-01-01T00:00:00Z"
        } 

--- app/core/email_service.py.txt ---
import resend
import asyncio
import logging
from typing import Optional, Dict
from datetime import datetime
from enum import Enum

from app.config.settings import settings

logger = logging.getLogger(__name__)

class EmailType(Enum):
    OTP_VERIFICATION = "otp_verification"
    PASSWORD_RESET = "password_reset"
    WELCOME = "welcome"
    ACCOUNT_LOCKED = "account_locked"

class EmailService:
    def __init__(self):
        if not settings.RESEND_API_KEY:
            raise ValueError("RESEND_API_KEY is not set in the environment.")
        resend.api_key = settings.RESEND_API_KEY
        
        self.from_address = "ConTXT <<EMAIL>>"
        self.support_email = "<EMAIL>"
        self.max_retries = 3
        self.base_delay = 1
        
    async def send_otp_email(self, email: str, otp_code: str, user_name: Optional[str] = None) -> Dict:
        """Send OTP verification email with security best practices"""
        
        html_template = self._generate_otp_email_html(otp_code, user_name)
        
        params: resend.Emails.SendParams = {
            "from": self.from_address,
            "to": [email],
            "subject": "ConTXT - Verify Your Account (Code Inside)",
            "html": html_template,
            "reply_to": self.support_email,
            "tags": [
                {"name": "type", "value": "otp_verification"},
                {"name": "user_email", "value": email},
                {"name": "timestamp", "value": datetime.now().isoformat()}
            ]
        }
        
        return await self._send_with_retry(params)
    
    async def send_password_reset_otp(self, email: str, otp_code: str, user_name: Optional[str] = None) -> Dict:
        """Send password reset OTP email with security best practices"""
        
        html_template = self._generate_password_reset_email_html(otp_code, user_name)
        
        params: resend.Emails.SendParams = {
            "from": self.from_address,
            "to": [email],
            "subject": "ConTXT - Password Reset Code",
            "html": html_template,
            "reply_to": self.support_email,
            "tags": [
                {"name": "type", "value": "password_reset"},
                {"name": "user_email", "value": email},
                {"name": "timestamp", "value": datetime.now().isoformat()}
            ]
        }
        
        return await self._send_with_retry(params)
    
    async def send_welcome_email(self, email: str, user_name: str) -> Dict:
        """Send welcome email to new users"""
        
        html_template = self._generate_welcome_email_html(user_name)
        
        params: resend.Emails.SendParams = {
            "from": self.from_address,
            "to": [email],
            "subject": "Welcome to ConTXT - Your AI Context Engineering Platform",
            "html": html_template,
            "reply_to": self.support_email,
            "tags": [
                {"name": "type", "value": "welcome"},
                {"name": "user_email", "value": email}
            ]
        }
        
        return await self._send_with_retry(params)
    
    async def send_account_locked_email(self, email: str, user_name: Optional[str] = None) -> Dict:
        """Send account locked notification email"""
        
        html_template = self._generate_account_locked_email_html(user_name)
        
        params: resend.Emails.SendParams = {
            "from": self.from_address,
            "to": [email],
            "subject": "ConTXT - Account Security Alert",
            "html": html_template,
            "reply_to": self.support_email,
            "tags": [
                {"name": "type", "value": "account_locked"},
                {"name": "user_email", "value": email}
            ]
        }
        
        return await self._send_with_retry(params)
    
    async def _send_with_retry(self, params: dict) -> Dict:
        """Send email with exponential backoff retry logic"""
        
        for attempt in range(self.max_retries):
            try:
                result = resend.Emails.send(params)
                logger.info(f"Email sent successfully: {result.id}")
                return {
                    "success": True, 
                    "message_id": result.id,
                    "timestamp": datetime.now().isoformat()
                }
                
            except Exception as e:
                if attempt == self.max_retries - 1:
                    logger.error(f"Final email send failure: {str(e)}")
                    return {
                        "success": False, 
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                    
                delay = self.base_delay * (2 ** attempt)
                logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {str(e)}")
                await asyncio.sleep(delay)
        
        return {"success": False, "error": "Max retries exceeded"}
    
    def _generate_otp_email_html(self, otp_code: str, user_name: Optional[str] = None) -> str:
        """Generate professional OTP email template with modern styling"""
        
        return f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>ConTXT - Verify Your Account</title>
            <style>
                * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }}
                .container {{ max-width: 600px; margin: 0 auto; background: #ffffff; }}
                .header {{ background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; padding: 40px 20px; text-align: center; }}
                .logo {{ font-size: 28px; font-weight: bold; margin-bottom: 10px; }}
                .content {{ padding: 40px 30px; }}
                .otp-container {{ text-align: center; margin: 30px 0; }}
                .otp-code {{ 
                    display: inline-block;
                    font-size: 36px; 
                    font-weight: bold; 
                    color: #2563eb; 
                    background: #f3f4f6; 
                    padding: 20px 30px; 
                    border-radius: 12px; 
                    letter-spacing: 8px;
                    border: 2px solid #e5e7eb;
                }}
                .security-notice {{ 
                    background: #fef3c7; 
                    border-left: 4px solid #f59e0b; 
                    padding: 15px; 
                    margin: 20px 0; 
                }}
                .footer {{ background: #f9fafb; padding: 30px; text-align: center; color: #6b7280; }}
                .button {{ 
                    display: inline-block; 
                    background: #2563eb; 
                    color: white; 
                    padding: 12px 24px; 
                    text-decoration: none; 
                    border-radius: 6px; 
                    margin: 20px 0; 
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">ConTXT</div>
                    <div>Secure Account Verification</div>
                </div>
                <div class="content">
                    <h2>Hello {user_name or 'there'}!</h2>
                    <p>You're almost ready to start using ConTXT. Please verify your email address with the code below:</p>
                    
                    <div class="otp-container">
                        <div class="otp-code">{otp_code}</div>
                        <p>Enter this code in the app to continue</p>
                    </div>
                    
                    <div class="security-notice">
                        <strong>Security Notice:</strong> This code expires in 10 minutes and can only be used once. 
                        Never share this code with anyone.
                    </div>
                    
                    <p>If you didn't create a ConTXT account, you can safely ignore this email.</p>
                    
                    <p>Need help? <a href="mailto:{self.support_email}">Contact our support team</a></p>
                </div>
                <div class="footer">
                    <p>© 2025 ConTXT AI - Intelligent Context Management</p>
                    <p>This is an automated message, please do not reply.</p>
                </div>
            </div>
        </body>
        </html>
        """

    def _generate_password_reset_email_html(self, otp_code: str, user_name: Optional[str] = None) -> str:
        """Generate professional password reset email template"""
        
        return f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>ConTXT - Password Reset</title>
            <style>
                * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }}
                .container {{ max-width: 600px; margin: 0 auto; background: #ffffff; }}
                .header {{ background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; padding: 40px 20px; text-align: center; }}
                .content {{ padding: 40px 30px; }}
                .otp-container {{ text-align: center; margin: 30px 0; }}
                .otp-code {{ 
                    display: inline-block;
                    font-size: 36px; 
                    font-weight: bold; 
                    color: #dc2626; 
                    background: #f3f4f6; 
                    padding: 20px 30px; 
                    border-radius: 12px; 
                    letter-spacing: 8px;
                    border: 2px solid #e5e7eb;
                }}
                .security-notice {{ background: #fef2f2; border-left: 4px solid #dc2626; padding: 15px; margin: 20px 0; }}
                .footer {{ background: #f9fafb; padding: 30px; text-align: center; color: #6b7280; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div style="font-size: 28px; font-weight: bold; margin-bottom: 10px;">ConTXT</div>
                    <div>Password Reset Request</div>
                </div>
                <div class="content">
                    <h2>Hello {user_name or 'there'}!</h2>
                    <p>We received a request to reset your ConTXT password. Use the code below to create a new password:</p>
                    
                    <div class="otp-container">
                        <div class="otp-code">{otp_code}</div>
                        <p>Enter this code in the app to reset your password</p>
                    </div>
                    
                    <div class="security-notice">
                        <strong>Security Notice:</strong> This code expires in 10 minutes. If you didn't request this reset, 
                        please ignore this email and contact support if you have concerns.
                    </div>
                    
                    <p>For security, this code can only be used once.</p>
                    
                    <p>Need help? <a href="mailto:{self.support_email}">Contact Support</a></p>
                </div>
                <div class="footer">
                    <p>© 2025 ConTXT AI</p>
                    <p>Need help? <a href="mailto:{self.support_email}">Contact Support</a></p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _generate_welcome_email_html(self, user_name: str) -> str:
        """Generate welcome email template"""
        
        return f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to ConTXT</title>
            <style>
                * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }}
                .container {{ max-width: 600px; margin: 0 auto; background: #ffffff; }}
                .header {{ background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 40px 20px; text-align: center; }}
                .content {{ padding: 40px 30px; }}
                .feature-list {{ margin: 20px 0; }}
                .feature-item {{ display: flex; align-items: center; margin: 10px 0; }}
                .feature-icon {{ width: 20px; height: 20px; background: #10b981; border-radius: 50%; margin-right: 15px; }}
                .cta-button {{ 
                    display: inline-block; 
                    background: #10b981; 
                    color: white; 
                    padding: 15px 30px; 
                    text-decoration: none; 
                    border-radius: 8px; 
                    font-weight: bold;
                    margin: 20px 0; 
                }}
                .footer {{ background: #f9fafb; padding: 30px; text-align: center; color: #6b7280; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div style="font-size: 28px; font-weight: bold; margin-bottom: 10px;">ConTXT</div>
                    <div>Welcome to AI Context Engineering</div>
                </div>
                <div class="content">
                    <h2>Welcome, {user_name}!</h2>
                    <p>Thank you for joining ConTXT, the intelligent context management platform that revolutionizes how you work with AI.</p>
                    
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="feature-icon"></div>
                            <span>Context Engineering workflows</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"></div>
                            <span>Knowledge Graph visualization</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"></div>
                            <span>Multi-provider AI integration</span>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"></div>
                            <span>Advanced content ingestion</span>
                        </div>
                    </div>
                    
                    <p>Your account is ready to use. Start by uploading your first content or exploring our knowledge graph features.</p>
                    
                    <div style="text-align: center;">
                        <a href="https://app.contxt.ai/dashboard" class="cta-button">Get Started</a>
                    </div>
                    
                    <p>If you have any questions, our support team is here to help.</p>
                </div>
                <div class="footer">
                    <p>© 2025 ConTXT AI - Intelligent Context Management</p>
                    <p>Questions? <a href="mailto:{self.support_email}">Contact Support</a></p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _generate_account_locked_email_html(self, user_name: Optional[str] = None) -> str:
        """Generate account locked notification email template"""
        
        return f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>ConTXT - Account Security Alert</title>
            <style>
                * {{ margin: 0; padding: 0; box-sizing: border-box; }}
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }}
                .container {{ max-width: 600px; margin: 0 auto; background: #ffffff; }}
                .header {{ background: linear-gradient(135deg, #f59e0b, #d97706); color: white; padding: 40px 20px; text-align: center; }}
                .content {{ padding: 40px 30px; }}
                .alert-box {{ 
                    background: #fef3c7; 
                    border-left: 4px solid #f59e0b; 
                    padding: 20px; 
                    margin: 20px 0; 
                    border-radius: 4px;
                }}
                .unlock-button {{ 
                    display: inline-block; 
                    background: #f59e0b; 
                    color: white; 
                    padding: 15px 30px; 
                    text-decoration: none; 
                    border-radius: 8px; 
                    font-weight: bold;
                    margin: 20px 0; 
                }}
                .footer {{ background: #f9fafb; padding: 30px; text-align: center; color: #6b7280; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div style="font-size: 28px; font-weight: bold; margin-bottom: 10px;">ConTXT</div>
                    <div>Account Security Alert</div>
                </div>
                <div class="content">
                    <h2>Hello {user_name or 'there'}!</h2>
                    <p>Your ConTXT account has been temporarily locked for security reasons due to multiple failed login attempts.</p>
                    
                    <div class="alert-box">
                        <strong>Security Alert:</strong> If this wasn't you, please contact our support team immediately. 
                        Your account security is our priority.
                    </div>
                    
                    <p>To unlock your account, please:</p>
                    <ol>
                        <li>Wait 15 minutes for the automatic unlock</li>
                        <li>Or contact our support team for immediate assistance</li>
                        <li>Consider updating your password for enhanced security</li>
                    </ol>
                    
                    <div style="text-align: center;">
                        <a href="mailto:{self.support_email}" class="unlock-button">Contact Support</a>
                    </div>
                    
                    <p>Thank you for helping us keep your account secure.</p>
                </div>
                <div class="footer">
                    <p>© 2025 ConTXT AI</p>
                    <p>Security Team: <a href="mailto:{self.support_email}">Contact Support</a></p>
                </div>
            </div>
        </body>
        </html>
        """


--- app/core/enhanced_ai_layer.py.txt ---
"""
Enhanced AI layer for document processing with multi-provider support.

This module provides an enhanced AI layer that integrates with the multi-provider
LLM system, providing advanced capabilities for document processing and analysis.
"""
import os
import logging
from typing import Dict, List, Any, Optional, Union

from app.core.llm_providers import (
    MultiProviderOrchestrator, 
    default_orchestrator,
    SecureProviderManager
)

logger = logging.getLogger(__name__)

class EnhancedAILayer:
    """
    Enhanced AI layer with multi-provider support for document processing.
    
    This class extends the original AIEnhancementLayer with support for multiple
    LLM providers, intelligent provider selection, fallback mechanisms, and
    cost optimization strategies.
    """
    
    def __init__(
        self,
        orchestrator: Optional[MultiProviderOrchestrator] = None,
        default_provider: str = "openai",
        default_priority: str = "balanced",
        user_tier: str = "pro_tier"
    ):
        """
        Initialize the enhanced AI layer.
        
        Args:
            orchestrator: Multi-provider orchestrator (uses default if None)
            default_provider: Default provider to use
            default_priority: Default priority for provider selection
            user_tier: User subscription tier
        """
        # Use provided orchestrator or default
        self.orchestrator = orchestrator or default_orchestrator
        
        # Default settings
        self.default_provider = default_provider
        self.default_priority = default_priority
        self.user_tier = user_tier
        
        # Initialize embedding provider
        self.embeddings = self.orchestrator.initialize_embedding_provider(
            provider="openai",
            model="text-embedding-3-large"
        )
        
        # Track initialization status
        self.initialized = self._check_initialization()
        
        logger.info(f"Enhanced AI layer initialized: {self.initialized}")
    
    def _check_initialization(self) -> bool:
        """
        Check if the AI layer is properly initialized.
        
        Returns:
            True if initialized, False otherwise
        """
        # Check if orchestrator has available providers
        has_providers = bool(self.orchestrator.provider_clients)
        
        # Check if default provider is available
        default_available = (
            self.default_provider in self.orchestrator.provider_clients or 
            len(self.orchestrator.provider_clients) > 0
        )
        
        return has_providers and default_available
    
    async def enhance_content(
        self, 
        content: Any, 
        content_type: str, 
        enhancement_type: str = "analysis",
        provider: Optional[str] = None,
        priority: Optional[str] = None,
        task_type: Optional[str] = None
    ) -> Optional[str]:
        """
        Enhance content with AI analysis using intelligent provider selection.
        
        Args:
            content: Content to enhance
            content_type: Type of content
            enhancement_type: Type of enhancement to perform
            provider: Specific provider to use (if None, select optimal)
            priority: Priority for provider selection
            task_type: Type of task
            
        Returns:
            Enhanced content or None if enhancement fails
        """
        if not self.initialized:
            logger.warning("AI enhancement requested but AI layer is not properly initialized")
            return None
        
        # Create prompt based on content type and enhancement type
        prompt = self._create_enhancement_prompt(content, content_type, enhancement_type)
        
        # Set task type based on enhancement type if not provided
        if not task_type:
            task_type = self._map_enhancement_to_task(enhancement_type)
        
        # Use provided priority or default
        priority = priority or self.default_priority
        
        try:
            # Define provider chain if specific provider requested
            provider_chain = [provider] if provider else None
            
            # Execute with automatic fallback
            result = await self.orchestrator.execute_with_fallback(
                prompt=prompt,
                provider_chain=provider_chain,
                task_type=task_type,
                user_tier=self.user_tier,
                priority=priority
            )
            
            # Extract content from result
            if hasattr(result, "content"):
                return result.content
            else:
                return str(result)
        except Exception as e:
            logger.error(f"AI enhancement failed: {e}")
            return None
    
    def _map_enhancement_to_task(self, enhancement_type: str) -> str:
        """
        Map enhancement type to task type for provider selection.
        
        Args:
            enhancement_type: Type of enhancement
            
        Returns:
            Corresponding task type
        """
        # Map enhancement types to task types
        enhancement_to_task = {
            "analysis": "analysis",
            "summary": "summarization",
            "extract": "extraction",
            "classify": "classification",
            "generate": "generation",
            "translate": "translation",
            "research": "research"
        }
        
        return enhancement_to_task.get(enhancement_type, "chat")
    
    def _create_enhancement_prompt(self, content: Any, content_type: str, enhancement_type: str) -> str:
        """
        Create a prompt for AI enhancement.
        
        Args:
            content: Content to enhance
            content_type: Type of content
            enhancement_type: Type of enhancement
            
        Returns:
            Prompt for the AI model
        """
        # Base prompt template
        if enhancement_type == "analysis":
            return f"""
            Analyze the following {content_type} content and provide insights:
            
            {content}
            
            Extract:
            1. Key topics and themes
            2. Important entities and relationships
            3. Main insights and findings
            4. Structure and organization
            """
        elif enhancement_type == "summary":
            return f"""
            Summarize the following {content_type} content:
            
            {content}
            
            Provide a concise summary highlighting the most important information.
            """
        elif enhancement_type == "extract":
            return f"""
            Extract structured information from the following {content_type} content:
            
            {content}
            
            Provide the extracted information in a clear, structured format.
            """
        elif enhancement_type == "classify":
            return f"""
            Classify the following {content_type} content:
            
            {content}
            
            Provide the primary category, subcategories, and confidence levels.
            """
        elif enhancement_type == "research":
            return f"""
            Research the following topic from {content_type} content:
            
            {content}
            
            Provide detailed, factual information including recent developments.
            """
        else:
            return f"""
            Process the following {content_type} content for {enhancement_type}:
            
            {content}
            """
    
    async def generate_embeddings(self, text: str, model: str = "text-embedding-3-large") -> Optional[List[float]]:
        """
        Generate embeddings for text using optimal provider.
        
        Args:
            text: Text to generate embeddings for
            model: Embedding model to use
            
        Returns:
            Vector embedding or None if generation fails
        """
        if not self.embeddings:
            logger.warning("Embedding generation requested but no embedding provider is available")
            return None
        
        try:
            # Generate embeddings
            result = await self.embeddings.aembed_query(text)
            return result
        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            return None
    
    async def analyze_document(
        self, 
        content: str, 
        content_type: str, 
        analysis_type: str = "comprehensive"
    ) -> Dict[str, Any]:
        """
        Perform advanced document analysis.
        
        Args:
            content: Document content
            content_type: Type of content
            analysis_type: Type of analysis to perform
            
        Returns:
            Analysis results
        """
        if not self.initialized:
            logger.warning("Document analysis requested but AI layer is not properly initialized")
            return {"error": "AI layer not initialized"}
        
        # Create prompt based on analysis type
        if analysis_type == "comprehensive":
            prompt = f"""
            Perform comprehensive analysis on the following {content_type} document:
            
            {content}
            
            Provide:
            1. Executive summary (2-3 sentences)
            2. Key topics and themes (bullet points)
            3. Important entities mentioned (names, organizations, dates, etc.)
            4. Main insights and claims
            5. Document structure analysis
            6. Sentiment and tone assessment
            7. Knowledge domains covered
            
            Format the response as JSON with these sections as keys.
            """
        elif analysis_type == "technical":
            prompt = f"""
            Perform technical analysis on the following {content_type} document:
            
            {content}
            
            Provide:
            1. Technical summary
            2. Technologies mentioned
            3. Technical concepts explained
            4. Code elements or algorithms
            5. Technical accuracy assessment
            6. Implementation considerations
            
            Format the response as JSON with these sections as keys.
            """
        else:
            prompt = f"""
            Analyze the following {content_type} document for {analysis_type}:
            
            {content}
            
            Provide a comprehensive analysis focused on {analysis_type}.
            Format the response as JSON with relevant sections.
            """
        
        try:
            # Select optimal provider for this task (analysis tasks benefit from higher quality)
            result = await self.orchestrator.execute_with_fallback(
                prompt=prompt,
                task_type="analysis",
                priority="quality"
            )
            
            # Extract content from result
            content = result.content if hasattr(result, "content") else str(result)
            
            # Try to parse JSON (if model didn't return proper JSON, wrap in dict)
            try:
                import json
                return json.loads(content)
            except json.JSONDecodeError:
                return {"raw_analysis": content}
        except Exception as e:
            logger.error(f"Document analysis failed: {e}")
            return {"error": str(e)}
    
    def get_provider_health(self) -> Dict[str, Dict[str, Any]]:
        """
        Get health status of all providers.
        
        Returns:
            Dictionary of provider health status
        """
        return self.orchestrator.provider_manager.get_provider_health()
    
    def get_available_providers(self) -> List[str]:
        """
        Get list of available providers.
        
        Returns:
            List of available provider names
        """
        return list(self.orchestrator.provider_clients.keys())


# Create a default instance for convenience
default_ai_layer = EnhancedAILayer() 

--- app/core/ingestion.py.txt ---
"""
Ingestion Manager.

This module handles the ingestion of various data sources,
including URLs, files, and raw text. It processes the data
and stores it in the knowledge graph and vector database.
Enhanced with optional AI capabilities and Cognee integration.
"""
import uuid
import json
import os
import tempfile
import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, BinaryIO
from fastapi import UploadFile
import httpx
from pathlib import Path

from app.db.neo4j_client import Neo4jClient
from app.db.qdrant_client import QdrantClient
from app.schemas.ingestion import IngestionStatus
from app.processors.factory import ProcessorFactory

logger = logging.getLogger(__name__)

class IngestionManager:
    """
    Manager for data ingestion operations.
    
    This class provides methods for ingesting various types of data
    sources and processing them for storage in the knowledge graph
    and vector database. Supports enhanced processing capabilities.
    """
    
    def __init__(self, use_cognee: bool = False, enable_ai: bool = False):
        """
        Initialize the ingestion manager.
        
        Args:
            use_cognee: Whether to use Cognee for database operations
            enable_ai: Whether to enable AI enhancements
        """
        # Check environment variables for feature flags
        self.use_cognee = use_cognee or os.getenv("USE_COGNEE", "false").lower() == "true"
        self.enable_ai = enable_ai or os.getenv("ENABLE_AI", "false").lower() == "true"
        
        # Initialize database clients
        self.neo4j_client = Neo4jClient()
        self.qdrant_client = QdrantClient()
        
        # In-memory store for job status (replace with Redis in production)
        self.job_store = {}
        
        logger.info(f"Initialized IngestionManager with use_cognee={self.use_cognee}, enable_ai={self.enable_ai}")
    
    async def ingest_url(
        self,
        url: str,
        metadata: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Ingest content from a URL.
        
        Args:
            url: URL to ingest
            metadata: Additional metadata
            options: Ingestion options
            
        Returns:
            Job ID for tracking the ingestion process
        """
        # Generate a job ID
        job_id = str(uuid.uuid4())
        
        # Initialize job status
        self.job_store[job_id] = {
            "status": "processing",
            "progress": 0.0,
            "source_type": "url",
            "source": url,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Process asynchronously
        asyncio.create_task(self._process_url(job_id, url, metadata or {}, options or {}))
        
        return job_id
    
    async def _process_url(
        self,
        job_id: str,
        url: str,
        metadata: Dict[str, Any],
        options: Dict[str, Any]
    ) -> None:
        """
        Process a URL asynchronously.
        
        Args:
            job_id: Job ID
            url: URL to process
            metadata: Additional metadata
            options: Processing options
        """
        try:
            # Extract processing options
            use_cognee = options.get("use_cognee", self.use_cognee)
            enable_ai = options.get("enable_ai", self.enable_ai)
            dataset_name = options.get("dataset_name", f"url_{job_id}")
            
            # Update job status
            self._update_job_status(job_id, progress=10.0, message="Fetching URL content")
            
            # Fetch content from URL
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url)
                response.raise_for_status()
                
                # Get content type
                content_type = response.headers.get('content-type', '').split(';')[0]
                
                # Update metadata with URL info
                metadata.update({
                    "url": url,
                    "content_type": content_type,
                    "fetch_date": datetime.now().isoformat(),
                    "status_code": response.status_code
                })
                
                # Update job status
                self._update_job_status(job_id, progress=30.0, message="Processing content")
                
                # Get appropriate processor with enhancement options
                try:
                    processor = ProcessorFactory.get_processor_for_content_type(
                        content_type,
                        use_cognee=use_cognee,
                        enable_ai=enable_ai,
                        dataset_name=dataset_name
                    )
                except ValueError:
                    # If no specific processor is available, use optimal processor
                    processor = ProcessorFactory.get_optimal_processor(
                        response.content, 
                        content_type=content_type,
                        use_cognee=use_cognee,
                        enable_ai=enable_ai,
                        dataset_name=dataset_name
                    )
                
                # Process content with or without enhancements
                if enable_ai:
                    result = await processor.process_with_enhancements(
                        response.content, 
                        metadata=metadata
                    )
                else:
                    result = await processor.process(
                        response.content, 
                        metadata=metadata
                    )
                
                # Update job status
                self._update_job_status(job_id, progress=70.0, message="Storing processed content")
                
                # Store in Neo4j and Qdrant (handled by processor)
                document_id = f"url_{job_id}"
                
                # Update job status
                self._update_job_status(
                    job_id, 
                    status="completed", 
                    progress=100.0, 
                    message="URL processing completed",
                    result={
                        "document_id": document_id,
                        "chunks_count": len(result.get("chunks", [])),
                        "has_enhancements": result.get("has_enhancements", False),
                        "metadata": result.get("metadata", {})
                    }
                )
                
        except Exception as e:
            logger.error(f"Error processing URL {url}: {str(e)}", exc_info=True)
            self._update_job_status(
                job_id,
                status="failed",
                progress=0.0,
                message=f"URL processing failed: {str(e)}"
            )
    
    async def ingest_file(
        self,
        file: UploadFile,
        metadata: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Ingest content from a file.
        
        Args:
            file: Uploaded file
            metadata: Additional metadata as JSON string
            options: Processing options
            
        Returns:
            Job ID for tracking the ingestion process
        """
        # Generate a job ID
        job_id = str(uuid.uuid4())
        
        # Parse metadata if provided
        meta_dict = json.loads(metadata) if metadata else {}
        
        # Add file info to metadata
        meta_dict.update({
            "filename": file.filename,
            "content_type": file.content_type,
            "upload_date": datetime.now().isoformat()
        })
        
        # Initialize job status
        self.job_store[job_id] = {
            "status": "processing",
            "progress": 0.0,
            "source_type": "file",
            "source": file.filename,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Process asynchronously
        asyncio.create_task(self._process_file(job_id, file, meta_dict, options or {}))
        
        return job_id
    
    async def _process_file(
        self,
        job_id: str,
        file: UploadFile,
        metadata: Dict[str, Any],
        options: Dict[str, Any]
    ) -> None:
        """
        Process a file asynchronously.
        
        Args:
            job_id: Job ID
            file: Uploaded file
            metadata: Additional metadata
            options: Processing options
        """
        temp_file_path = None
        
        try:
            # Extract processing options
            use_cognee = options.get("use_cognee", self.use_cognee)
            enable_ai = options.get("enable_ai", self.enable_ai)
            dataset_name = options.get("dataset_name", f"file_{job_id}")
            
            # Update job status
            self._update_job_status(job_id, progress=10.0, message="Saving file")
            
            # Save file to temporary location
            content = await file.read()
            
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
                temp_file.write(content)
                temp_file_path = temp_file.name
            
            # Update job status
            self._update_job_status(job_id, progress=30.0, message="Processing file")
            
            # Get appropriate processor based on file extension with enhancement options
            try:
                processor = ProcessorFactory.get_processor_for_file(
                    temp_file_path,
                    use_cognee=use_cognee,
                    enable_ai=enable_ai,
                    dataset_name=dataset_name
                )
            except ValueError:
                # If no specific processor is available, use optimal processor
                processor = ProcessorFactory.get_optimal_processor(
                    content,
                    content_type=file.content_type,
                    use_cognee=use_cognee,
                    enable_ai=enable_ai,
                    dataset_name=dataset_name
                )
            
            # Process file with or without enhancements
            with open(temp_file_path, 'rb') as f:
                if enable_ai:
                    result = await processor.process_with_enhancements(f, metadata=metadata)
                else:
                    result = await processor.process(f, metadata=metadata)
            
            # Update job status
            self._update_job_status(job_id, progress=70.0, message="Storing processed content")
            
            # Store in Neo4j and Qdrant (handled by processor)
            document_id = f"file_{job_id}"
            
            # Update job status
            self._update_job_status(
                job_id, 
                status="completed", 
                progress=100.0, 
                message="File processing completed",
                result={
                    "document_id": document_id,
                    "chunks_count": len(result.get("chunks", [])),
                    "has_enhancements": result.get("has_enhancements", False),
                    "metadata": result.get("metadata", {})
                }
            )
        
        except Exception as e:
            logger.error(f"Error processing file {file.filename}: {str(e)}", exc_info=True)
            self._update_job_status(
                job_id,
                status="failed",
                progress=0.0,
                message=f"File processing failed: {str(e)}"
            )
        
        finally:
            # Clean up temporary file
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    async def ingest_text(
        self,
        text: str,
        metadata: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Ingest raw text content.
        
        Args:
            text: Text content to ingest
            metadata: Additional metadata as JSON string
            options: Processing options
            
        Returns:
            Job ID for tracking the ingestion process
        """
        # Generate a job ID
        job_id = str(uuid.uuid4())
        
        # Parse metadata if provided
        meta_dict = json.loads(metadata) if metadata else {}
        
        # Add text info to metadata
        meta_dict.update({
            "content_type": "text/plain",
            "ingestion_date": datetime.now().isoformat(),
            "length": len(text)
        })
        
        # Initialize job status
        self.job_store[job_id] = {
            "status": "processing",
            "progress": 0.0,
            "source_type": "text",
            "source": text[:50] + "..." if len(text) > 50 else text,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Process asynchronously
        asyncio.create_task(self._process_text(job_id, text, meta_dict, options or {}))
        
        return job_id
    
    async def _process_text(
        self,
        job_id: str,
        text: str,
        metadata: Dict[str, Any],
        options: Dict[str, Any]
    ) -> None:
        """
        Process text content asynchronously.
        
        Args:
            job_id: Job ID
            text: Text content
            metadata: Additional metadata
            options: Processing options
        """
        try:
            # Extract processing options
            use_cognee = options.get("use_cognee", self.use_cognee)
            enable_ai = options.get("enable_ai", self.enable_ai)
            dataset_name = options.get("dataset_name", f"text_{job_id}")
            
            # Update job status
            self._update_job_status(job_id, progress=30.0, message="Processing text")
            
            # Get text processor with enhancement options
            processor = ProcessorFactory.get_processor_for_content_type(
                "text/plain",
                use_cognee=use_cognee,
                enable_ai=enable_ai,
                dataset_name=dataset_name
            )
            
            # Process text with or without enhancements
            if enable_ai:
                result = await processor.process_with_enhancements(text, metadata=metadata)
            else:
                result = await processor.process(text, metadata=metadata)
            
            # Update job status
            self._update_job_status(job_id, progress=70.0, message="Storing processed content")
            
            # Store in Neo4j and Qdrant (handled by processor)
            document_id = f"text_{job_id}"
            
            # Update job status
            self._update_job_status(
                job_id, 
                status="completed", 
                progress=100.0, 
                message="Text processing completed",
                result={
                    "document_id": document_id,
                    "chunks_count": len(result.get("chunks", [])),
                    "has_enhancements": result.get("has_enhancements", False),
                    "metadata": result.get("metadata", {})
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing text: {str(e)}", exc_info=True)
            self._update_job_status(
                job_id,
                status="failed",
                progress=0.0,
                message=f"Text processing failed: {str(e)}"
            )
    
    async def ingest_with_privacy(
        self,
        content: Any,
        content_type: str,
        redact_pii: bool = True,
        pii_types: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Ingest content with privacy compliance.
        
        Args:
            content: Content to ingest
            content_type: Type of content (MIME type)
            redact_pii: Whether to redact personally identifiable information
            pii_types: Types of PII to redact (email, phone, etc.)
            metadata: Additional metadata
            options: Processing options
            
        Returns:
            Job ID for tracking the ingestion process
        """
        # Generate a job ID
        job_id = str(uuid.uuid4())
        
        # Initialize job status
        self.job_store[job_id] = {
            "status": "processing",
            "progress": 0.0,
            "source_type": "privacy",
            "source": content_type,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Process asynchronously
        asyncio.create_task(
            self._process_with_privacy(
                job_id, 
                content, 
                content_type, 
                redact_pii, 
                pii_types, 
                metadata or {}, 
                options or {}
            )
        )
        
        return job_id
    
    async def _process_with_privacy(
        self,
        job_id: str,
        content: Any,
        content_type: str,
        redact_pii: bool,
        pii_types: Optional[List[str]],
        metadata: Dict[str, Any],
        options: Dict[str, Any]
    ) -> None:
        """
        Process content with privacy compliance asynchronously.
        
        Args:
            job_id: Job ID
            content: Content to process
            content_type: Type of content
            redact_pii: Whether to redact PII
            pii_types: Types of PII to redact
            metadata: Additional metadata
            options: Processing options
        """
        try:
            # Extract processing options
            use_cognee = options.get("use_cognee", self.use_cognee)
            enable_ai = options.get("enable_ai", self.enable_ai)
            dataset_name = options.get("dataset_name", f"privacy_{job_id}")
            
            # Update job status
            self._update_job_status(job_id, progress=10.0, message="Applying privacy protection")
            
            # Get privacy processor
            privacy_processor = ProcessorFactory.get_special_processor(
                "privacy",
                use_cognee=use_cognee,
                enable_ai=enable_ai,
                dataset_name=dataset_name,
                redact_pii=redact_pii,
                pii_types=pii_types
            )
            
            # Add privacy info to metadata
            metadata.update({
                "privacy_protected": True,
                "redacted_pii": redact_pii,
                "pii_types": pii_types or []
            })
            
            # Process with privacy protection
            result = await privacy_processor.process(content, metadata=metadata, content_type=content_type)
            
            # Update job status
            self._update_job_status(job_id, progress=70.0, message="Storing privacy-protected content")
            
            # Store in Neo4j and Qdrant (handled by processor)
            document_id = f"privacy_{job_id}"
            
            # Update job status
            self._update_job_status(
                job_id, 
                status="completed", 
                progress=100.0, 
                message="Privacy-protected processing completed",
                result={
                    "document_id": document_id,
                    "chunks_count": len(result.get("chunks", [])),
                    "privacy_protection": {
                        "applied": True,
                        "pii_types": pii_types or []
                    },
                    "metadata": result.get("metadata", {})
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing with privacy: {str(e)}", exc_info=True)
            self._update_job_status(
                job_id,
                status="failed",
                progress=0.0,
                message=f"Privacy-protected processing failed: {str(e)}"
            )
    
    def _update_job_status(
        self,
        job_id: str,
        status: Optional[str] = None,
        progress: Optional[float] = None,
        message: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Update the status of an ingestion job.
        
        Args:
            job_id: Job ID
            status: Job status
            progress: Progress percentage
            message: Status message
            result: Job result
        """
        if job_id not in self.job_store:
            return
        
        # Update job status
        if status:
            self.job_store[job_id]["status"] = status
        
        if progress is not None:
            self.job_store[job_id]["progress"] = progress
        
        if message:
            self.job_store[job_id]["message"] = message
        
        if result:
            self.job_store[job_id]["result"] = result
        
        # Update timestamp
        self.job_store[job_id]["updated_at"] = datetime.now().isoformat()
    
    async def get_status(self, job_id: str) -> IngestionStatus:
        """
        Get the status of an ingestion job.
        
        Args:
            job_id: Job ID
            
        Returns:
            Job status
            
        Raises:
            ValueError: If the job is not found
        """
        if job_id not in self.job_store:
            raise ValueError(f"Job not found: {job_id}")
        
        job_data = self.job_store[job_id]
        
        return IngestionStatus(
            job_id=job_id,
            status=job_data["status"],
            progress=job_data.get("progress"),
            message=job_data.get("message"),
            result=job_data.get("result"),
            created_at=job_data["created_at"],
            updated_at=job_data["updated_at"]
        ) 

--- app/core/__init__.py.txt ---
"""
Core functionality for the application.

This module provides core functionality for the application,
including AI enhancements, LLM providers, context engine, and more.
"""

# Import core modules for easy access
from .context_engine import ContextEngine
from .knowledge_graph import KnowledgeGraph
from .auth import get_password_hash, verify_password

# Import multi-provider system if available
try:
    from .llm_providers import (
        MultiProviderOrchestrator,
        SecureProviderManager,
        default_orchestrator
    )
    from .enhanced_ai_layer import EnhancedAILayer, default_ai_layer
    MULTI_PROVIDER_AVAILABLE = True
except ImportError:
    MULTI_PROVIDER_AVAILABLE = False

# Export main classes
__all__ = [
    "ContextEngine",
    "KnowledgeGraph",
    "get_password_hash",
    "verify_password",
]

# Add multi-provider exports if available
if MULTI_PROVIDER_AVAILABLE:
    __all__.extend([
        "MultiProviderOrchestrator",
        "SecureProviderManager",
        "default_orchestrator",
        "EnhancedAILayer", 
        "default_ai_layer"
    ])


--- app/core/knowledge_graph.py.txt ---
"""
Knowledge Graph Manager.

This module provides an interface to the Neo4j knowledge graph,
implementing operations for storing, retrieving, and querying
knowledge representations.
"""
import uuid
from typing import Dict, List, Optional, Any, Union

from app.db.neo4j_client import Neo4jClient

class KnowledgeGraph:
    """
    Manager for knowledge graph operations.
    """
    
    def __init__(self):
        """Initialize the knowledge graph manager.""" 
        self.neo4j_client = Neo4jClient()

    async def get_all_nodes(
        self,
        limit: int = 100,
        node_type: Optional[str] = None,
        search: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get all nodes with metadata for visualization"""
        query = "MATCH (n) "
        params: Dict[str, Any] = {}
        conditions = []

        if node_type:
            query += f"WHERE labels(n)[0] = $node_type "
            params["node_type"] = node_type

        if search:
            search_condition = "(n.name CONTAINS $search OR n.title CONTAINS $search OR n.content CONTAINS $search)"
            if conditions:
                query += "AND " + search_condition
            else:
                query += "WHERE " + search_condition
            params["search"] = search

        query += "RETURN elementId(n) as id, n as properties, labels(n) as labels LIMIT $limit"
        params["limit"] = limit

        result = await self.neo4j_client.run_query(query, params)
        
        nodes = []
        for record in result:
            properties = record["properties"]
            node = {
                "id": record["id"],
                "label": properties.get("title", properties.get("name", "Unknown")),
                "type": record["labels"][0] if record["labels"] else "Unknown",
                "content": properties.get("content", "")[:200] + "...",
                "metadata": dict(properties)
            }
            nodes.append(node)
        return nodes

    async def get_all_relationships(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get all relationships for visualization"""
        query = """
        MATCH (a)-[r]->(b)
        RETURN elementId(a) as source,
               elementId(b) as target,
               type(r) as type,
               properties(r) as properties
        LIMIT $limit
        """
        result = await self.neo4j_client.run_query(query, {"limit": limit})
        
        relationships = []
        for record in result:
            rel = {
                "source": record["source"],
                "target": record["target"],
                "type": record["type"],
                "weight": record["properties"].get("weight", 1.0),
                "metadata": record["properties"]
            }
            relationships.append(rel)
        return relationships

    async def get_neighbors(self, node_id: str, depth: int = 1) -> Dict[str, List]:
        """Get neighbors of a specific node up to a certain depth."""
        query = """
        MATCH (startNode)
        WHERE elementId(startNode) = $node_id
        CALL apoc.path.subgraphAll(startNode, {maxLevel: $depth})
        YIELD nodes, relationships
        RETURN nodes, relationships
        """
        params = {"node_id": node_id, "depth": depth}
        result = await self.neo4j_client.run_query(query, params)

        if not result:
            return {"nodes": [], "links": []}

        raw_nodes = result[0].get('nodes', [])
        raw_rels = result[0].get('relationships', [])

        nodes = [
            {
                "id": n.element_id,
                "label": n.get("title", n.get("name", "Unknown")),
                "type": list(n.labels)[0] if n.labels else "Unknown",
                "metadata": dict(n)
            }
            for n in raw_nodes
        ]

        links = [
            {
                "source": r.start_node.element_id,
                "target": r.end_node.element_id,
                "type": r.type,
                "metadata": dict(r)
            }
            for r in raw_rels
        ]
        
        return {"nodes": nodes, "links": links}

    async def get_graph_analytics(self) -> Dict[str, Any]:
        """Get graph analytics and statistics"""
        stats_query = """
        MATCH (n)
        OPTIONAL MATCH ()-[r]->()
        RETURN 
            count(DISTINCT n) as total_nodes,
            count(DISTINCT r) as total_relationships
        """
        stats_result = await self.neo4j_client.run_query(stats_query)
        stats: Dict[str, Any] = dict(stats_result[0]) if stats_result else {'total_nodes': 0, 'total_relationships': 0}

        node_dist_query = "MATCH (n) RETURN labels(n)[0] as node_type, count(n) as count ORDER BY count DESC"
        node_dist_result = await self.neo4j_client.run_query(node_dist_query)
        stats['node_distribution'] = [{'type': r['node_type'], 'count': r['count']} for r in node_dist_result]
        stats['node_types'] = [r['type'] for r in stats['node_distribution']]

        rel_dist_query = "MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count ORDER BY count DESC"
        rel_dist_result = await self.neo4j_client.run_query(rel_dist_query)
        stats['relationship_distribution'] = [{'type': r['rel_type'], 'count': r['count']} for r in rel_dist_result]
        stats['relationship_types'] = [r['type'] for r in stats['relationship_distribution']]

        return stats 

--- app/core/llm_providers.py.txt ---
"""
Multi-model LLM provider integration using LangChain.

This module provides a unified interface for multiple LLM providers,
including OpenRouter, XAI (Grok), OpenAI, Anthropic, Google, and others,
with intelligent provider selection and fallback mechanisms.
"""
import os
import logging
import time
from typing import Dict, List, Any, Optional, Union, Callable
from functools import wraps
from collections import defaultdict

logger = logging.getLogger(__name__)

# Import LangChain components with fallbacks
try:
    from langchain.schema import AIMessage, HumanMessage
    from langchain.schema.runnable import RunnableConfig
    LANGCHAIN_CORE_AVAILABLE = True
except ImportError:
    logger.warning("LangChain core not available. Install with 'pip install langchain-core'")
    LANGCHAIN_CORE_AVAILABLE = False

# Import LangChain provider integrations with fallbacks
providers = {
    "openrouter": False,
    "xai": False,
    "openai": False,
    "anthropic": False,
    "google": False,
    "groq": False,
    "mistral": False,
    "together": False,
    "perplexity": False,
    "azure": False,
    "ollama": False,
}

try:
    from langchain_openai import ChatOpenAI, OpenAIEmbeddings
    providers["openai"] = True
except ImportError:
    logger.warning("LangChain OpenAI integration not available. Install with 'pip install langchain-openai'")

try:
    from langchain_xai import ChatXAI
    providers["xai"] = True
except ImportError:
    logger.warning("LangChain XAI integration not available. Install with 'pip install langchain-xai'")

try:
    from langchain_anthropic import ChatAnthropic
    providers["anthropic"] = True
except ImportError:
    logger.warning("LangChain Anthropic integration not available. Install with 'pip install langchain-anthropic'")

try:
    from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings
    providers["google"] = True
except ImportError:
    logger.warning("LangChain Google AI integration not available. Install with 'pip install langchain-google-genai'")

try:
    from langchain_groq import ChatGroq
    providers["groq"] = True
except ImportError:
    logger.warning("LangChain Groq integration not available. Install with 'pip install langchain-groq'")

try:
    from langchain_mistral import ChatMistral
    providers["mistral"] = True
except ImportError:
    logger.warning("LangChain Mistral integration not available. Install with 'pip install langchain-mistral'")

try:
    from langchain_together import ChatTogether
    providers["together"] = True
except ImportError:
    logger.warning("LangChain Together integration not available. Install with 'pip install langchain-together'")

try:
    from langchain_perplexity import ChatPerplexity
    providers["perplexity"] = True
except ImportError:
    logger.warning("LangChain Perplexity integration not available. Install with 'pip install langchain-perplexity'")

try:
    from langchain_openai import AzureChatOpenAI
    providers["azure"] = True
except ImportError:
    logger.warning("LangChain Azure OpenAI integration not available. Install with 'pip install langchain-openai'")

try:
    from langchain_community.llms import Ollama
    providers["ollama"] = True
except ImportError:
    logger.warning("LangChain Ollama integration not available. Install with 'pip install langchain-community'")

# Try to import OpenRouter integration
try:
    from langchain_openai import ChatOpenAI as OpenRouterChat
    providers["openrouter"] = True
except ImportError:
    logger.warning("OpenRouter integration not available (uses langchain-openai with custom base_url)")


class SecureProviderManager:
    """
    Securely manages API keys and provider configuration.
    """
    
    def __init__(self):
        """Initialize the provider manager."""
        self.providers = {}
        self.provider_health = {}
        self._load_secure_credentials()
    
    def _load_secure_credentials(self) -> Dict[str, str]:
        """
        Load API keys from secure environment variables.
        
        Returns:
            Dictionary of provider credentials
        """
        credentials = {
            "openrouter": os.getenv("OPENROUTER_API_KEY", ""),
            "openai": os.getenv("OPENAI_API_KEY", ""),
            "anthropic": os.getenv("ANTHROPIC_API_KEY", ""),
            "google": os.getenv("GOOGLE_API_KEY", ""),
            "xai": os.getenv("XAI_API_KEY", ""),
            "groq": os.getenv("GROQ_API_KEY", ""),
            "mistral": os.getenv("MISTRAL_API_KEY", ""),
            "perplexity": os.getenv("PERPLEXITY_API_KEY", ""),
            "azure": os.getenv("AZURE_OPENAI_API_KEY", ""),
            "ollama": os.getenv("OLLAMA_API_KEY", "")
        }
        
        # Initialize provider health status
        for provider, api_key in credentials.items():
            self.provider_health[provider] = {
                "available": bool(api_key) and providers.get(provider, False),
                "healthy": bool(api_key) and providers.get(provider, False),
                "last_checked": time.time() if api_key else 0,
                "failure_count": 0
            }
        
        return credentials
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """
        Get API key for a provider.
        
        Args:
            provider: Provider name
            
        Returns:
            API key if available, None otherwise
        """
        return os.getenv(f"{provider.upper()}_API_KEY", "")
    
    def is_provider_available(self, provider: str) -> bool:
        """
        Check if a provider is available (has API key and integration).
        
        Args:
            provider: Provider name
            
        Returns:
            True if provider is available, False otherwise
        """
        return self.provider_health.get(provider, {}).get("available", False)
    
    def mark_provider_unhealthy(self, provider: str) -> None:
        """
        Mark a provider as unhealthy.
        
        Args:
            provider: Provider name
        """
        if provider in self.provider_health:
            self.provider_health[provider]["healthy"] = False
            self.provider_health[provider]["failure_count"] += 1
            self.provider_health[provider]["last_checked"] = time.time()
    
    def mark_provider_healthy(self, provider: str) -> None:
        """
        Mark a provider as healthy.
        
        Args:
            provider: Provider name
        """
        if provider in self.provider_health:
            self.provider_health[provider]["healthy"] = True
            self.provider_health[provider]["failure_count"] = 0
            self.provider_health[provider]["last_checked"] = time.time()
    
    def get_provider_health(self) -> Dict[str, Dict[str, Any]]:
        """
        Get health status of all providers.
        
        Returns:
            Dictionary of provider health status
        """
        return self.provider_health.copy()


class ProviderRateLimiter:
    """
    Rate limiting for LLM provider calls.
    """
    
    def __init__(self):
        """Initialize the rate limiter."""
        self.request_counts = defaultdict(list)
        self.limits = {
            "free_tier": {"requests_per_minute": 10, "monthly_cost_limit": 0},
            "pro_tier": {"requests_per_minute": 100, "monthly_cost_limit": 50},
            "enterprise": {"requests_per_minute": 1000, "monthly_cost_limit": 500}
        }
    
    def rate_limit(self, user_tier: str, provider: str):
        """
        Rate limiting decorator for provider calls.
        
        Args:
            user_tier: User subscription tier
            provider: Provider name
            
        Returns:
            Decorated function
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Check rate limits
                current_time = time.time()
                user_requests = self.request_counts[f"{user_tier}_{provider}"]
                
                # Remove old requests (older than 1 minute)
                user_requests[:] = [req_time for req_time in user_requests 
                                  if current_time - req_time < 60]
                
                # Check if rate limit is exceeded
                limit = self.limits.get(user_tier, {}).get("requests_per_minute", 10)
                if len(user_requests) >= limit:
                    raise ValueError(f"Rate limit exceeded for {provider} with {user_tier}")
                
                # Add current request
                user_requests.append(current_time)
                
                # Call the function
                return await func(*args, **kwargs)
            
            return wrapper
        
        return decorator


class MultiProviderOrchestrator:
    """
    Orchestrates multiple LLM providers with intelligent selection and fallback.
    """
    
    def __init__(self):
        """Initialize the orchestrator."""
        self.provider_manager = SecureProviderManager()
        self.rate_limiter = ProviderRateLimiter()
        
        # Provider tiers based on features and cost
        self.provider_tiers = {
            # Ultra-fast, affordable providers
            "speed": ["groq", "ollama"],
            
            # Premium quality providers
            "quality": ["openai", "anthropic", "xai"],
            
            # Mid-tier cost-effective providers
            "balanced": ["mistral", "together", "google"],
            
            # Specialized for research and fact-checking
            "research": ["perplexity"]
        }
        
        # Provider costs per 1M tokens (input + output combined)
        self.provider_costs = {
            "openai": 10.0,  # $10/1M tokens for GPT-4o
            "anthropic": 15.0,  # $15/1M tokens for Claude 3 Sonnet
            "xai": 8.0,  # $8/1M tokens for Grok
            "google": 7.0,  # $7/1M tokens for Gemini Pro
            "groq": 1.5,  # $1.5/1M tokens for LLama 3
            "mistral": 5.0,  # $5/1M tokens
            "together": 2.0,  # $2/1M tokens
            "perplexity": 10.0,  # $10/1M tokens
            "azure": 10.0,  # $10/1M tokens (varies by deployment)
            "ollama": 0.0,  # Free (local)
            "openrouter": 10.0  # Varies by selected model
        }
        
        # Provider latency characteristics (lower is faster)
        self.provider_latency = {
            "groq": 1,  # Ultra low latency
            "ollama": 2,  # Low latency (local)
            "openai": 3,  # Medium latency
            "anthropic": 3,  # Medium latency
            "google": 3,  # Medium latency
            "xai": 3,  # Medium latency
            "mistral": 3,  # Medium latency
            "together": 3,  # Medium latency
            "perplexity": 4,  # High latency (research optimized)
            "azure": 3,  # Medium latency
            "openrouter": 3  # Medium latency (varies by model)
        }
        
        # Initialize provider clients
        self.provider_clients = {}
        self._initialize_available_providers()
    
    def _initialize_available_providers(self) -> None:
        """Initialize all available provider clients."""
        # Attempt to initialize OpenRouter (preferred gateway)
        if providers["openrouter"] and self.provider_manager.is_provider_available("openrouter"):
            try:
                self.provider_clients["openrouter"] = OpenRouterChat(
                    base_url="https://openrouter.ai/api/v1",
                    api_key=self.provider_manager.get_api_key("openrouter"),
                    model="anthropic/claude-3-sonnet",
                    temperature=0.1,
                    default_headers={
                        "HTTP-Referer": "https://contxt.ai",
                        "X-Title": "ConTXT AI"
                    }
                )
                logger.info("Initialized OpenRouter client")
            except Exception as e:
                logger.error(f"Failed to initialize OpenRouter client: {e}")
        
        # Initialize direct provider clients for fallback
        self._initialize_direct_providers()
    
    def _initialize_direct_providers(self) -> None:
        """Initialize direct provider clients."""
        # Initialize OpenAI
        if providers["openai"] and self.provider_manager.is_provider_available("openai"):
            try:
                self.provider_clients["openai"] = ChatOpenAI(
                    api_key=self.provider_manager.get_api_key("openai"),
                    model="gpt-4o",
                    temperature=0.1
                )
                logger.info("Initialized OpenAI client")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI client: {e}")
        
        # Initialize XAI (Grok)
        if providers["xai"] and self.provider_manager.is_provider_available("xai"):
            try:
                self.provider_clients["xai"] = ChatXAI(
                    xai_api_key=self.provider_manager.get_api_key("xai"),
                    model="grok-beta",
                    temperature=0.1,
                    base_url="https://api.x.ai/v1"
                )
                logger.info("Initialized XAI client")
            except Exception as e:
                logger.error(f"Failed to initialize XAI client: {e}")
        
        # Initialize Anthropic
        if providers["anthropic"] and self.provider_manager.is_provider_available("anthropic"):
            try:
                self.provider_clients["anthropic"] = ChatAnthropic(
                    anthropic_api_key=self.provider_manager.get_api_key("anthropic"),
                    model="claude-3-sonnet-20240229",
                    temperature=0.1,
                    max_tokens=4096
                )
                logger.info("Initialized Anthropic client")
            except Exception as e:
                logger.error(f"Failed to initialize Anthropic client: {e}")
        
        # Initialize Google
        if providers["google"] and self.provider_manager.is_provider_available("google"):
            try:
                self.provider_clients["google"] = ChatGoogleGenerativeAI(
                    google_api_key=self.provider_manager.get_api_key("google"),
                    model="gemini-2.0-flash-exp",
                    temperature=0.1
                )
                logger.info("Initialized Google client")
            except Exception as e:
                logger.error(f"Failed to initialize Google client: {e}")
        
        # Initialize Groq
        if providers["groq"] and self.provider_manager.is_provider_available("groq"):
            try:
                self.provider_clients["groq"] = ChatGroq(
                    groq_api_key=self.provider_manager.get_api_key("groq"),
                    model="llama-3.1-8b-instant",
                    temperature=0.1
                )
                logger.info("Initialized Groq client")
            except Exception as e:
                logger.error(f"Failed to initialize Groq client: {e}")
        
        # Initialize Mistral
        if providers["mistral"] and self.provider_manager.is_provider_available("mistral"):
            try:
                self.provider_clients["mistral"] = ChatMistral(
                    mistral_api_key=self.provider_manager.get_api_key("mistral"),
                    model="mistral-large-latest",
                    temperature=0.1
                )
                logger.info("Initialized Mistral client")
            except Exception as e:
                logger.error(f"Failed to initialize Mistral client: {e}")
        
        # Initialize Together
        if providers["together"] and self.provider_manager.is_provider_available("together"):
            try:
                self.provider_clients["together"] = ChatTogether(
                    together_api_key=self.provider_manager.get_api_key("together"),
                    model="meta-llama/Llama-3-70b-chat-hf",
                    temperature=0.1
                )
                logger.info("Initialized Together client")
            except Exception as e:
                logger.error(f"Failed to initialize Together client: {e}")
        
        # Initialize Perplexity
        if providers["perplexity"] and self.provider_manager.is_provider_available("perplexity"):
            try:
                self.provider_clients["perplexity"] = ChatPerplexity(
                    api_key=self.provider_manager.get_api_key("perplexity"),
                    model="pplx-70b-online",
                    temperature=0.1
                )
                logger.info("Initialized Perplexity client")
            except Exception as e:
                logger.error(f"Failed to initialize Perplexity client: {e}")
        
        # Initialize Azure OpenAI
        if providers["azure"] and self.provider_manager.is_provider_available("azure"):
            try:
                azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT", "")
                if azure_endpoint:
                    self.provider_clients["azure"] = AzureChatOpenAI(
                        azure_endpoint=azure_endpoint,
                        api_key=self.provider_manager.get_api_key("azure"),
                        deployment_name=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4"),
                        temperature=0.1
                    )
                    logger.info("Initialized Azure OpenAI client")
            except Exception as e:
                logger.error(f"Failed to initialize Azure OpenAI client: {e}")
        
        # Initialize Ollama
        if providers["ollama"]:
            try:
                ollama_base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
                self.provider_clients["ollama"] = Ollama(
                    base_url=ollama_base_url,
                    model="llama3",
                    temperature=0.1
                )
                logger.info("Initialized Ollama client")
            except Exception as e:
                logger.error(f"Failed to initialize Ollama client: {e}")
    
    def select_optimal_provider(
        self, 
        task_type: str, 
        user_tier: str = "pro_tier",
        priority: str = "balanced"
    ) -> str:
        """
        Select the optimal provider based on task type, user tier, and priority.
        
        Args:
            task_type: Type of task (e.g., 'chat', 'analysis', 'research')
            user_tier: User subscription tier
            priority: Priority for selection ('speed', 'quality', 'cost')
            
        Returns:
            Selected provider name
        """
        # Get available healthy providers
        available_providers = [
            provider for provider, client in self.provider_clients.items()
            if self.provider_manager.is_provider_available(provider)
        ]
        
        if not available_providers:
            raise ValueError("No available providers")
        
        # Select based on priority
        if priority == "speed" and any(p in available_providers for p in self.provider_tiers["speed"]):
            for provider in self.provider_tiers["speed"]:
                if provider in available_providers:
                    return provider
        
        if priority == "quality" and any(p in available_providers for p in self.provider_tiers["quality"]):
            for provider in self.provider_tiers["quality"]:
                if provider in available_providers:
                    return provider
        
        if task_type == "research" and "perplexity" in available_providers:
            return "perplexity"
        
        # Default to a balanced option based on user tier
        if user_tier == "free_tier" and "ollama" in available_providers:
            return "ollama"
        elif user_tier == "enterprise" and "openai" in available_providers:
            return "openai"
        elif "openrouter" in available_providers:
            # OpenRouter provides good balanced access to multiple models
            return "openrouter"
        
        # Fallback to first available provider
        return available_providers[0]
    
    def get_provider_client(self, provider: str):
        """
        Get the client for a provider.
        
        Args:
            provider: Provider name
            
        Returns:
            Provider client
        """
        if provider not in self.provider_clients:
            raise ValueError(f"Provider {provider} not initialized")
        
        return self.provider_clients[provider]
    
    async def execute_with_fallback(
        self, 
        prompt: str, 
        provider_chain: Optional[List[str]] = None,
        task_type: str = "chat",
        user_tier: str = "pro_tier",
        priority: str = "balanced"
    ):
        """
        Execute a prompt with automatic fallback on failure.
        
        Args:
            prompt: Prompt to execute
            provider_chain: List of providers to try in order (if None, select optimal)
            task_type: Type of task
            user_tier: User subscription tier
            priority: Priority for selection
            
        Returns:
            Result from the provider
        """
        # Determine provider chain if not provided
        if not provider_chain:
            selected_provider = self.select_optimal_provider(
                task_type=task_type,
                user_tier=user_tier,
                priority=priority
            )
            # Create fallback chain based on provider tiers
            provider_chain = [selected_provider]
            
            # Add fallbacks from different tiers
            for tier in ["quality", "balanced", "speed"]:
                for provider in self.provider_tiers[tier]:
                    if (provider in self.provider_clients and 
                        provider not in provider_chain and
                        self.provider_manager.is_provider_available(provider)):
                        provider_chain.append(provider)
            
            # Always add Ollama as last resort if available
            if "ollama" in self.provider_clients and "ollama" not in provider_chain:
                provider_chain.append("ollama")
        
        # Try each provider in the chain
        last_error = None
        for provider in provider_chain:
            try:
                client = self.get_provider_client(provider)
                
                # Apply rate limiting
                @self.rate_limiter.rate_limit(user_tier, provider)
                async def execute_with_rate_limit():
                    # Create messages for the provider
                    messages = [HumanMessage(content=prompt)]
                    
                    # Invoke the provider
                    result = await client.ainvoke(messages)
                    return result
                
                result = await execute_with_rate_limit()
                
                # Mark provider as healthy
                self.provider_manager.mark_provider_healthy(provider)
                
                return result
            except Exception as e:
                logger.error(f"Provider {provider} failed: {e}")
                self.provider_manager.mark_provider_unhealthy(provider)
                last_error = e
        
        # If we get here, all providers failed
        raise ValueError(f"All providers failed: {last_error}")
    
    def initialize_embedding_provider(
        self, 
        provider: str = "openai", 
        model: str = "text-embedding-3-large"
    ):
        """
        Initialize an embedding provider.
        
        Args:
            provider: Provider name
            model: Model name
            
        Returns:
            Embedding provider
        """
        if provider == "openai" and providers["openai"]:
            try:
                return OpenAIEmbeddings(
                    api_key=self.provider_manager.get_api_key("openai"),
                    model=model
                )
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI embeddings: {e}")
        
        if provider == "google" and providers["google"]:
            try:
                return GoogleGenerativeAIEmbeddings(
                    google_api_key=self.provider_manager.get_api_key("google"),
                    model="models/embedding-001"
                )
            except Exception as e:
                logger.error(f"Failed to initialize Google embeddings: {e}")
        
        logger.warning(f"Unsupported embedding provider: {provider}")
        return None


# Create a default instance for convenience
default_orchestrator = MultiProviderOrchestrator() 

--- app/core/otp_service.py.txt ---
import secrets
import string
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional

import asyncpg
from fastapi import HTTPException, status
import logging

from app.core.email_service import EmailService
from app.config.settings import settings

logger = logging.getLogger(__name__)


class OTPService:
    """Service for generating, sending, and verifying One-Time Passwords (OTPs)."""

    def __init__(self, db_pool: asyncpg.Pool, email_service: EmailService):
        self.db_pool = db_pool
        self.email_service = email_service
        self.otp_length = 6
        self.expiry_minutes = settings.OTP_EXPIRY_MINUTES
        self.rate_limit_window = settings.OTP_RATE_LIMIT_WINDOW
        self.max_attempts = settings.MAX_OTP_ATTEMPTS

    def _generate_otp(self) -> str:
        """Generate a secure OTP."""
        return ''.join(secrets.choice(string.digits) for _ in range(self.otp_length))

    async def _check_rate_limit(self, email: str, conn: asyncpg.Connection):
        """Check and enforce rate limits for OTP generation."""
        now = datetime.now(timezone.utc)
        window_start = now - timedelta(seconds=self.rate_limit_window)

        # Clean up old rate limit entries
        await conn.execute("DELETE FROM otp_rate_limits WHERE created_at < $1", window_start)

        # Count recent requests
        count = await conn.fetchval(
            "SELECT COUNT(*) FROM otp_rate_limits WHERE email = $1 AND created_at >= $2",
            email, window_start
        )

        if count and count >= self.max_attempts:
            logger.warning(f"OTP rate limit exceeded for {email}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Rate limit exceeded. Try again in {self.rate_limit_window} seconds."
            )

        # Record the current request
        await conn.execute(
            "INSERT INTO otp_rate_limits (email, created_at) VALUES ($1, $2)", email, now
        )

    async def send_verification_otp(self, email: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Send OTP for email verification"""
        async with self.db_pool.acquire() as conn:
            await self._check_rate_limit(email, conn)
            # The unified otps table is cleaned up by a central function, so no need for specific cleanup here.

            otp_code = self._generate_otp()
            # In a real app, you would hash the OTP before storing it.
            # For this example, we'll store it directly but acknowledge this is not best practice.
            otp_hash = otp_code # In a real implementation: await self.hash_otp(otp_code)
            expires_at = datetime.now(timezone.utc) + timedelta(minutes=self.expiry_minutes)

            await conn.execute(
                """INSERT INTO otps (user_id, otp_type, otp_hash, expires_at)
                   VALUES ($1, 'email_verification', $2, $3)""",
                user_id, otp_hash, expires_at
            )

            await self.email_service.send_otp_email(email, otp_code)

            return {
                "success": True,
                "message": "Verification code sent to your email",
                "expires_in": self.expiry_minutes * 60
            }

    async def verify_email_otp(self, email: str, otp_code: str) -> Dict[str, Any]:
        """Verify OTP for email verification"""
        async with self.db_pool.acquire() as conn:
            # In a real app, you would hash the provided otp_code to compare it with the stored hash
            otp_hash = otp_code

            otp_record = await conn.fetchrow(
                """SELECT id, user_id, is_used FROM otps
                   WHERE user_id = (SELECT id FROM users WHERE email = $1) 
                   AND otp_type = 'email_verification'
                   AND otp_hash = $2 
                   AND expires_at > now()
                   ORDER BY created_at DESC LIMIT 1""",
                email, otp_hash
            )

            if not otp_record:
                await self.increment_otp_attempt(email, otp_code)
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired verification code"
                )

            if otp_record['is_used']:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Verification code already used")

            # The new schema doesn't track attempts per OTP, but this could be added if needed.
            # For now, we'll remove this check to align with the current schema.


            await conn.execute("UPDATE otps SET is_used = true WHERE id = $1", otp_record['id'])

            if otp_record['user_id']:
                await conn.execute("UPDATE users SET is_verified = true, is_active = true WHERE id = $1", otp_record['user_id'])
                
                # Fetch user details to send welcome email
                user = await conn.fetchrow("SELECT email, first_name FROM users WHERE id = $1", otp_record['user_id'])
                if user:
                    try:
                        await self.email_service.send_welcome_email(user['email'], user['first_name'])
                    except Exception as e:
                        logger.error(f"Failed to send welcome email to {user['email']}: {e}")
                        # Do not block registration flow if email fails

            return {
                "success": True,
                "message": "Email verified successfully",
                "user_id": str(otp_record['user_id']) if otp_record['user_id'] else None
            }

    async def increment_otp_attempt(self, email: str, otp_code: str):
        """Increment failed attempt counter for a specific OTP, if it exists and is not expired."""
        async with self.db_pool.acquire() as conn:
            await conn.execute(
                """UPDATE email_verification_otps SET attempts = attempts + 1
                   WHERE email = $1 AND otp_code = $2 AND expires_at > now() AND is_used = false""",
                email, otp_code
            )

    async def send_password_reset_otp(self, email: str) -> Dict[str, Any]:
        """Send OTP for password reset"""
        async with self.db_pool.acquire() as conn:
            await self._check_rate_limit(email, conn)
            
            user = await conn.fetchrow("SELECT id, first_name FROM users WHERE email = $1 AND is_active = true", email)

            if not user:
                logger.warning(f"Password reset requested for non-existent or inactive user: {email}")
                return {"success": True, "message": "If an account with that email exists, a password reset code has been sent."}

            otp_code = self._generate_otp()
            expires_at = datetime.now(timezone.utc) + timedelta(minutes=self.expiry_minutes)

            await conn.execute(
                "INSERT INTO password_reset_otps (user_id, email, otp_code, expires_at, max_attempts) VALUES ($1, $2, $3, $4, $5)",
                user['id'], email, otp_code, expires_at, self.max_attempts
            )

            await self.email_service.send_password_reset_otp(email, otp_code, user['first_name'])

            return {
                "success": True,
                "message": "Reset code sent to your email",
                "expires_in": self.expiry_minutes * 60
            }

    async def verify_password_reset_otp(self, email: str, otp_code: str) -> Dict[str, Any]:
        """Verify OTP for password reset"""
        async with self.db_pool.acquire() as conn:
            otp_record = await conn.fetchrow(
                """SELECT id, is_used FROM password_reset_otps
                   WHERE email = $1 AND otp_code = $2 AND expires_at > now()
                   ORDER BY created_at DESC LIMIT 1""",
                email, otp_code
            )

            if not otp_record:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired password reset code"
                )

            if otp_record['is_used']:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Password reset code has already been used")

            await conn.execute("UPDATE password_reset_otps SET is_used = true WHERE id = $1", otp_record['id'])

            return {"success": True, "message": "Password reset OTP verified successfully."}


--- app/core/worker.py.txt ---
"""
Celery worker configuration.

This module sets up the Celery worker for background task processing,
configuring the broker, task routes, and other settings.
"""
import os
from celery import Celery

# Create Celery instance
celery_app = Celery(
    'contxt',
    broker=os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0'),
    backend=os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0'),
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
)

# Define task routes
celery_app.conf.task_routes = {
    'app.core.ingestion.*': {'queue': 'ingestion'},
    'app.processors.*': {'queue': 'processing'},
}

# Optional configuration based on environment
if os.getenv('DEBUG', 'false').lower() == 'true':
    celery_app.conf.update(
        task_always_eager=True,  # Execute tasks locally in debug mode
        task_eager_propagates=True,  # Propagate exceptions in eager mode
    )

# Auto-discover tasks from these modules
celery_app.autodiscover_tasks(['app.core', 'app.processors']) 

--- app/crud/crud_user.py.txt ---
from sqlalchemy.orm import Session
from app.models.user import User
from app.schemas.user import UserCreate
from app.core.auth import get_password_hash

def get_user_by_email(db: Session, *, email: str) -> User:
    return db.query(User).filter(User.email == email).first()

def create_user(db: Session, *, obj_in: UserCreate) -> User:
    db_obj = User(
        email=obj_in.email,
        hashed_password=get_password_hash(obj_in.password),
        full_name=obj_in.full_name,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


--- app/db/base_class.py.txt ---
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


--- app/db/neo4j_client.py.txt ---
"""
Neo4j database client.
"""
import logging
from typing import Dict, List, Any, Optional

from neo4j import AsyncGraphDatabase
from neo4j.exceptions import ServiceUnavailable

from app.config.settings import settings

logger = logging.getLogger(__name__)

class Neo4jClient:
    """Client for interacting with Neo4j database."""
    
    def __init__(self):
        """Initialize the Neo4j client."""
        self.uri = settings.NEO4J_URI
        self.user = settings.NEO4J_USER
        self.password = settings.NEO4J_PASSWORD
        self._driver = None
    
    async def get_driver(self):
        """Get or create the Neo4j driver."""
        if self._driver is None:
            try:
                self._driver = AsyncGraphDatabase.driver(
                    self.uri, 
                    auth=(self.user, self.password)
                )
                # Test connection
                await self._driver.verify_connectivity()
                logger.info("Connected to Neo4j at %s", self.uri)
            except ServiceUnavailable as e:
                logger.error("Failed to connect to Neo4j: %s", str(e))
                raise
        return self._driver
    
    async def close(self):
        """Close the Neo4j driver."""
        if self._driver is not None:
            await self._driver.close()
            self._driver = None
            logger.info("Neo4j connection closed")
    
    async def run_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Run a Cypher query against Neo4j.
        
        Args:
            query: Cypher query string
            params: Query parameters
            
        Returns:
            List of results as dictionaries
        """
        driver = await self.get_driver()
        params = params or {}
        
        try:
            async with driver.session() as session:
                result = await session.run(query, params)
                records = await result.values()
                return [dict(zip(result.keys(), record)) for record in records]
        except Exception as e:
            logger.error("Neo4j query failed: %s", str(e))
            raise 

--- app/db/postgres_client.py.txt ---
"""
PostgreSQL database client for ConTXT authentication system.
Provides connection pool management and database operations.
"""
import asyncio
import asyncpg
import logging
from typing import Optional
from contextlib import asynccontextmanager

from app.config.settings import settings

logger = logging.getLogger(__name__)

class PostgreSQLClient:
    """PostgreSQL client with connection pooling."""
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self._connection_string = self._build_connection_string()
    
    def _build_connection_string(self) -> str:
        """Build PostgreSQL connection string from settings."""
        return (
            f"postgresql://{settings.DB_USERNAME}:{settings.DB_PASSWORD}"
            f"@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
            f"?sslmode={settings.DB_SSL_MODE}"
        )
    
    async def connect(self) -> None:
        """Initialize database connection pool."""
        try:
            self.pool = await asyncpg.create_pool(
                self._connection_string,
                min_size=5,
                max_size=20,
                command_timeout=60,
                server_settings={
                    'jit': 'off'  # Disable JIT for better performance on small queries
                }
            )
            logger.info("PostgreSQL connection pool created successfully")
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
                logger.info("PostgreSQL connection test successful")
                
        except Exception as e:
            logger.error(f"Failed to create PostgreSQL connection pool: {e}")
            raise
    
    async def disconnect(self) -> None:
        """Close database connection pool."""
        if self.pool:
            await self.pool.close()
            logger.info("PostgreSQL connection pool closed")
    
    async def get_pool(self) -> asyncpg.Pool:
        """Get database connection pool."""
        if not self.pool:
            await self.connect()
        return self.pool
    
    @asynccontextmanager
    async def get_connection(self):
        """Get database connection from pool."""
        if not self.pool:
            await self.connect()
        
        async with self.pool.acquire() as connection:
            yield connection
    
    async def execute_script(self, script_path: str) -> None:
        """Execute SQL script file."""
        try:
            with open(script_path, 'r') as file:
                script = file.read()
            
            async with self.get_connection() as conn:
                await conn.execute(script)
                logger.info(f"Successfully executed script: {script_path}")
                
        except Exception as e:
            logger.error(f"Failed to execute script {script_path}: {e}")
            raise
    
    async def health_check(self) -> bool:
        """Check database health."""
        try:
            async with self.get_connection() as conn:
                await conn.fetchval("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False

# Global client instance
_postgres_client: Optional[PostgreSQLClient] = None

async def get_postgres_client() -> PostgreSQLClient:
    """Get global PostgreSQL client instance."""
    global _postgres_client
    if _postgres_client is None:
        _postgres_client = PostgreSQLClient()
        await _postgres_client.connect()
    return _postgres_client

async def get_db_pool() -> asyncpg.Pool:
    """Get database connection pool (dependency injection)."""
    client = await get_postgres_client()
    return await client.get_pool()

async def close_postgres_client() -> None:
    """Close global PostgreSQL client."""
    global _postgres_client
    if _postgres_client:
        await _postgres_client.disconnect()
        _postgres_client = None


--- app/db/qdrant_client.py.txt ---
"""
Qdrant vector database client.
"""
import logging
from typing import Dict, List, Any, Optional, Union

from qdrant_client import QdrantClient as QClient
from qdrant_client.http import models as qdrant_models

from app.config.settings import settings

logger = logging.getLogger(__name__)

class QdrantClient:
    """Client for interacting with Qdrant vector database."""
    
    def __init__(self):
        """Initialize the Qdrant client."""
        self.host = settings.QDRANT_HOST
        self.port = settings.QDRANT_PORT
        self.collection = settings.QDRANT_COLLECTION
        self._client = None
    
    def get_client(self):
        """Get or create the Qdrant client."""
        if self._client is None:
            try:
                self._client = QClient(host=self.host, port=self.port)
                logger.info("Connected to Qdrant at %s:%s", self.host, self.port)
            except Exception as e:
                logger.error("Failed to connect to Qdrant: %s", str(e))
                raise
        return self._client
    
    def close(self):
        """Close the Qdrant client."""
        if self._client is not None:
            self._client = None
            logger.info("Qdrant connection closed")
    
    async def ensure_collection(self, vector_size: int = 1536):
        """
        Ensure the collection exists, creating it if necessary.
        
        Args:
            vector_size: Size of vectors to store (default: 1536 for OpenAI embeddings)
        """
        client = self.get_client()
        collections = client.get_collections().collections
        collection_names = [c.name for c in collections]
        
        if self.collection not in collection_names:
            logger.info("Creating Qdrant collection: %s", self.collection)
            client.create_collection(
                collection_name=self.collection,
                vectors_config=qdrant_models.VectorParams(
                    size=vector_size,
                    distance=qdrant_models.Distance.COSINE
                )
            )
    
    async def store_vectors(
        self,
        vectors: List[List[float]],
        metadata: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Store vectors in the collection.
        
        Args:
            vectors: List of vector embeddings
            metadata: List of metadata dictionaries
            ids: Optional list of IDs
            
        Returns:
            List of stored vector IDs
        """
        client = self.get_client()
        
        # Ensure collection exists
        await self.ensure_collection(len(vectors[0]))
        
        # Prepare points
        points = []
        result_ids = []
        
        for i, (vector, meta) in enumerate(zip(vectors, metadata)):
            point_id = ids[i] if ids and i < len(ids) else str(i)
            result_ids.append(point_id)
            
            points.append(
                qdrant_models.PointStruct(
                    id=point_id,
                    vector=vector,
                    payload=meta
                )
            )
        
        # Upsert points
        client.upsert(
            collection_name=self.collection,
            points=points
        )
        
        return result_ids
    
    async def search_vectors(
        self,
        query_vector: List[float],
        limit: int = 10,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar vectors.
        
        Args:
            query_vector: Query vector embedding
            limit: Maximum number of results
            filter_dict: Optional filter dictionary
            
        Returns:
            List of search results with metadata
        """
        client = self.get_client()
        
        # Convert filter dict to Qdrant filter if provided
        filter_obj = None
        if filter_dict:
            filter_obj = qdrant_models.Filter(
                must=[
                    qdrant_models.FieldCondition(
                        key=key,
                        match=qdrant_models.MatchValue(value=value)
                    )
                    for key, value in filter_dict.items()
                ]
            )
        
        # Search
        search_result = client.search(
            collection_name=self.collection,
            query_vector=query_vector,
            limit=limit,
            query_filter=filter_obj
        )
        
        # Format results
        results = []
        for hit in search_result:
            result = {
                "id": hit.id,
                "score": hit.score,
                "payload": hit.payload
            }
            results.append(result)
        
        return results 

--- app/db/session.py.txt ---
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os

DATABASE_URL = os.getenv("DATABASE_URL", "*************************************************")

engine = create_engine(DATABASE_URL, pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


--- app/examples/__init__.py.txt ---
"""
Example modules for the application.

This package contains example usage for various application components.
"""

# List available examples
__all__ = ["multi_provider_example"] 

--- app/examples/multi_provider_example.py.txt ---
"""
Example usage of the multi-provider LLM system.

This module demonstrates how to use the multi-provider LLM system
for different document processing tasks.
"""
import os
import asyncio
import logging
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the components we'll use
from processors.factory import ProcessorFactory
from processors.processors_updated import MultiProviderProcessor
from core.enhanced_ai_layer import EnhancedAILayer, default_ai_layer
from core.llm_providers import MultiProviderOrchestrator, default_orchestrator
from app.config.provider_config import get_recommended_providers

async def process_document_example():
    """Example of processing a document with multi-provider support."""
    # Create a processor with multi-provider support
    processor = ProcessorFactory.get_enhanced_processor(
        content_type="text/plain",
        use_multi_provider=True,
        dataset_name="example_dataset",
        user_tier="pro_tier",
        priority="balanced"
    )
    
    # Sample content to process
    content = """
    # Machine Learning Project Proposal
    
    ## Overview
    This project aims to develop a predictive model for customer churn in the telecommunications industry.
    
    ## Objectives
    1. Identify key factors leading to customer churn
    2. Develop a model with at least 85% accuracy
    3. Implement the model in a production-ready API
    
    ## Timeline
    - Data collection and preparation: 2 weeks
    - Model development and testing: 4 weeks
    - API development and deployment: 2 weeks
    
    ## Budget
    Total estimated cost: $50,000
    """
    
    # Process the content with enhancements
    result = await processor.process_with_enhancements(
        content=content,
        enhancement_type="analysis",
        priority="quality"  # Use high-quality models for this analysis
    )
    
    # Print the results
    logger.info("Document processing results:")
    logger.info(f"Processed content: {result.get('processed_content', '')[:100]}...")
    logger.info(f"Has enhancements: {result.get('has_enhancements', False)}")
    
    if result.get("has_enhancements"):
        logger.info("Enhanced content:")
        logger.info(result.get("enhanced_content", ""))
    
    # Print provider information if available
    if "provider_info" in result:
        logger.info("Provider information:")
        logger.info(f"Available providers: {result['provider_info'].get('available_providers', [])}")
        logger.info(f"Recommended providers: {result['provider_info'].get('recommended_providers', [])}")
    
    return result

async def try_different_providers():
    """Example of trying different providers for the same task."""
    # Initialize the AI layer directly
    ai_layer = EnhancedAILayer()
    
    # Content to analyze
    content = "Climate change is one of the most pressing issues of our time. It requires coordinated global action."
    
    # Try different providers and priorities
    providers_to_try = {
        "openai": "quality",
        "anthropic": "quality",
        "google": "balanced",
        "groq": "speed",
        "ollama": "speed"
    }
    
    results = {}
    for provider, priority in providers_to_try.items():
        try:
            logger.info(f"Trying provider: {provider} with priority: {priority}")
            
            # Only try if provider is available
            if provider in ai_layer.get_available_providers():
                result = await ai_layer.enhance_content(
                    content=content,
                    content_type="text/plain",
                    enhancement_type="summary",
                    provider=provider,
                    priority=priority
                )
                
                results[provider] = result
                logger.info(f"Result from {provider}: {result}")
            else:
                logger.info(f"Provider {provider} not available")
        except Exception as e:
            logger.error(f"Error with provider {provider}: {e}")
    
    return results

async def run_document_analysis():
    """Example of running advanced document analysis."""
    # Create a multi-provider processor directly
    processor = MultiProviderProcessor(
        enable_ai=True,
        user_tier="enterprise",
        priority="quality"
    )
    
    # Sample document to analyze
    document = """
    QUARTERLY FINANCIAL REPORT - Q2 2023
    
    Revenue: $12.4 million
    Growth: 15% year-over-year
    New customers: 2,500
    Churn rate: 3.2%
    
    Key Highlights:
    1. Launched premium subscription tier, contributing $1.2M in new revenue
    2. Expanded European operations with new office in Berlin
    3. Reduced customer acquisition cost by 12%
    
    Challenges:
    - Supply chain disruptions affected hardware product delivery
    - Increased competition in North American market
    
    Outlook:
    We expect continued growth in Q3, with projected revenue of $13.5-14.5 million.
    """
    
    # Run advanced analysis
    result = await processor.analyze_document(
        content=document,
        content_type="text/plain",
        analysis_type="comprehensive"
    )
    
    # Print the results
    logger.info("Document analysis results:")
    logger.info(result)
    
    return result

async def test_provider_fallback():
    """Example of testing the provider fallback mechanism."""
    # Get orchestrator directly
    orchestrator = default_orchestrator
    
    # Test prompt
    prompt = "Explain quantum computing in simple terms."
    
    # Try with specific provider chain (intentionally including some that might not be available)
    try:
        logger.info("Testing fallback mechanism with specific provider chain")
        result = await orchestrator.execute_with_fallback(
            prompt=prompt,
            provider_chain=["unavailable_provider", "openai", "anthropic", "groq", "ollama"],
            task_type="chat"
        )
        
        if hasattr(result, "content"):
            logger.info(f"Result content: {result.content[:100]}...")
        else:
            logger.info(f"Result: {str(result)[:100]}...")
        
        # Show which provider succeeded
        provider_health = orchestrator.provider_manager.get_provider_health()
        for provider, status in provider_health.items():
            if status.get("healthy"):
                logger.info(f"Provider {provider} is healthy")
            else:
                logger.info(f"Provider {provider} has failures: {status.get('failure_count', 0)}")
    
    except Exception as e:
        logger.error(f"Fallback test failed: {e}")
    
    return orchestrator.provider_manager.get_provider_health()

async def main():
    """Run all examples."""
    logger.info("Starting multi-provider examples")
    
    # Process a document
    logger.info("\n\n=== DOCUMENT PROCESSING EXAMPLE ===\n")
    await process_document_example()
    
    # Try different providers
    logger.info("\n\n=== DIFFERENT PROVIDERS EXAMPLE ===\n")
    await try_different_providers()
    
    # Run document analysis
    logger.info("\n\n=== DOCUMENT ANALYSIS EXAMPLE ===\n")
    await run_document_analysis()
    
    # Test provider fallback
    logger.info("\n\n=== PROVIDER FALLBACK EXAMPLE ===\n")
    await test_provider_fallback()
    
    logger.info("\n\nAll examples completed")

if __name__ == "__main__":
    # Ensure required environment variables are set
    required_vars = [
        "OPENAI_API_KEY",  # At least one provider key should be present
        "OPENROUTER_API_KEY"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        logger.warning(f"Missing environment variables: {missing_vars}")
        logger.warning("Some examples may fail without required API keys")
    
    # Run the examples
    asyncio.run(main()) 

--- app/__init__.py.txt ---
"""
AI Context Engineering Agent application package.
""" 

--- app/main.py.txt ---
"""
Main application module for the AI Context Engineering Agent.
Comprehensive backend with authentication, context engineering, and knowledge management.
"""
import logging
from typing import Optional
from contextlib import asynccontextmanager
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import Counter, Histogram, generate_latest
import time
import sys

from app.config.settings import settings

# Configure logging
logging.basicConfig(
    level=settings.LOG_LEVEL.upper(),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stdout,
)
from app.api.router import api_router
from app.middleware.security import add_security_headers
# from app.api.endpoints.auth import router as auth_router
from app.db.postgres_client import get_postgres_client, close_postgres_client
from app.core.auth_dependencies import get_optional_user
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

# Metrics setup
request_count = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
request_duration = Histogram('http_request_duration_seconds', 'HTTP request duration')
auth_requests = Counter('auth_requests_total', 'Total auth requests', ['endpoint', 'status'])

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Application lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application startup and shutdown."""
    # Startup
    logging.info("Starting ConTXT Backend with Authentication...")
    
    try:
        # Initialize database connections
        postgres_client = await get_postgres_client()
        logging.info("✓ PostgreSQL connection established")
        
        # Test database connectivity
        if await postgres_client.health_check():
            logging.info("✓ Database health check passed")
        else:
            logging.error("✗ Database health check failed")
        
        logging.info("🚀 ConTXT Backend startup complete")
        
    except Exception as e:
        logging.error(f"❌ Startup failed: {e}")
        raise
    
    yield
    
    # Shutdown
    logging.info("Shutting down ConTXT Backend...")
    try:
        await close_postgres_client()
        logging.info("✓ Database connections closed")
    except Exception as e:
        logging.error(f"Error during shutdown: {e}")
    
    logging.info("👋 ConTXT Backend shutdown complete")

app = FastAPI(
    title="ConTXT - AI Context Engineering Agent",
    description="Enterprise-grade backend API for AI context engineering, knowledge management, and user authentication",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    openapi_components={
        "securitySchemes": {
            "JWTBearer": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
                "description": "Enter JWT token in the format: Bearer &lt;token&gt;"
            }
        }
    }
)

# Apply the security scheme globally to all endpoints
app.openapi_schema = app.openapi()
app.openapi_schema["security"] = [{"JWTBearer": []}]

# Add security headers middleware
app.middleware("http")(add_security_headers)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limiting middleware
app.state.limiter = limiter
app.add_middleware(SlowAPIMiddleware)
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Metrics middleware
@app.middleware("http")
async def metrics_middleware(request, call_next):
    """Collect metrics for all HTTP requests."""
    start_time = time.time()
    
    response = await call_next(request)
    
    # Record metrics
    duration = time.time() - start_time
    method = request.method
    endpoint = request.url.path
    status = str(response.status_code)
    
    request_count.labels(method=method, endpoint=endpoint, status=status).inc()
    request_duration.observe(duration)
    
    # Track auth-specific metrics
    if endpoint.startswith("/auth/"):
        auth_requests.labels(endpoint=endpoint, status=status).inc()
    
    return response

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Handle unexpected exceptions gracefully."""
    logging.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "success": False}
    )

# Include API routes
app.include_router(api_router, prefix="/api")
# app.include_router(auth_router, tags=["Authentication"]) 

class ConnectionManager:
    def __init__(self):
        self.active_connections: list[WebSocket] = []
        self.user_connections: dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, user_id: Optional[str] = None):
        await websocket.accept()
        self.active_connections.append(websocket)
        if user_id:
            self.user_connections[user_id] = websocket

    def disconnect(self, websocket: WebSocket, user_id: Optional[str] = None):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if user_id and user_id in self.user_connections:
            del self.user_connections[user_id]

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                disconnected.append(connection)
        
        # Clean up disconnected connections
        for conn in disconnected:
            if conn in self.active_connections:
                self.active_connections.remove(conn)
    
    async def send_to_user(self, user_id: str, message: dict):
        if user_id in self.user_connections:
            try:
                import json
                await self.user_connections[user_id].send_text(json.dumps(message))
            except:
                del self.user_connections[user_id]
    
    async def broadcast_graph_update(self, update_data: dict):
        import json
        message = json.dumps({
            "type": "graph_update",
            "data": update_data
        })
        await self.broadcast(message)

manager = ConnectionManager()

@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str, token: Optional[str] = None):
    """
    WebSocket endpoint for real-time communication.
    Requires a token for authentication.
    """
    if not token:
        await websocket.close(code=1008)
        return

    # Here you would typically validate the token.
    # For now, we'll just accept the connection if a token is present.

    await manager.connect(websocket, user_id)
    print(f"Client #{user_id} connected with token.")
    try:
        while True:
            # Keep the connection alive.
            # A more robust implementation would handle incoming messages.
            await websocket.receive_text() 
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
        print(f"Client #{user_id} disconnected: {websocket.client.host}")
    except Exception as e:
        print(f"WebSocket Error: {e}")
        manager.disconnect(websocket)

@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "AI Context Engineering Agent is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True) 

--- app/middleware/security.py.txt ---
# app/middleware/security.py
from fastapi import Request
from starlette.responses import Response
from app.config.settings import settings

async def add_security_headers(request: Request, call_next):
    response: Response = await call_next(request)
    
    # Default security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    
    # Content Security Policy (CSP)
    csp_policy = {
        "default-src": "'self'",
        "script-src": "'self' 'unsafe-inline' https://cdn.jsdelivr.net", # Allow inline scripts and scripts from jsdelivr
        "style-src": "'self' 'unsafe-inline' https://fonts.googleapis.com", # Allow inline styles and fonts from Google
        "font-src": "'self' https://fonts.gstatic.com",
        "img-src": "'self' data:",
        "connect-src": "'self'",
        "form-action": "'self'",
        "frame-ancestors": "'none'", # Disallow embedding in iframes
        "base-uri": "'self'",
        "object-src": "'none'", # Disallow plugins like Flash
        "upgrade-insecure-requests": "", # This will be handled by HSTS
    }
    
    # Allow overriding CSP from settings for different environments
    if settings.CSP_POLICY_OVERRIDES:
        csp_policy.update(settings.CSP_POLICY_OVERRIDES)
        
    csp_header_value = "; ".join([f"{key} {value}" for key, value in csp_policy.items() if value is not None])
    response.headers["Content-Security-Policy"] = csp_header_value
    
    # HTTP Strict Transport Security (HSTS)
    if settings.ENABLE_HSTS:
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains; preload"
        
    # Referrer-Policy
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    
    # Permissions-Policy
    response.headers["Permissions-Policy"] = "camera=(), microphone=(), geolocation=()"

    return response



--- app/models/user.py.txt ---
# app/models/user.py
import uuid
from datetime import datetime
from sqlalchemy import (
    Column, String, DateTime, Boolean, Text, ForeignKey, Integer, JSON, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID, INET
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, unique=True, index=True, nullable=False)
    password_hash = Column(String, nullable=False)
    first_name = Column(String(100))
    last_name = Column(String(100))
    full_name = Column(String(201), index=True)
    is_active = Column(Boolean(), default=False, nullable=False)
    is_verified = Column(Boolean(), default=False, nullable=False)
    subscription_tier = Column(String, default="free", nullable=False)
    
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login_at = Column(DateTime(timezone=True))
    login_count = Column(Integer, default=0, nullable=False)

    api_keys = relationship("UserAPIKey", back_populates="user", cascade="all, delete-orphan")
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    auth_events = relationship("AuthEvent", back_populates="user", cascade="all, delete-orphan")
    preferences = relationship("UserPreferences", back_populates="user", uselist=False, cascade="all, delete-orphan")

class UserAPIKey(Base):
    __tablename__ = "user_api_keys"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    key_name = Column(String(100), nullable=False)
    api_key = Column(String, unique=True, index=True, nullable=False)
    permissions = Column(JSON, nullable=False, default=lambda: ["read"])
    is_active = Column(Boolean, default=True, nullable=False)
    expires_at = Column(DateTime(timezone=True))
    last_used_at = Column(DateTime(timezone=True))
    usage_count = Column(Integer, default=0, nullable=False)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    
    user = relationship("User", back_populates="api_keys")

class UserSession(Base):
    __tablename__ = "user_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    refresh_token_hash = Column(String, nullable=False, unique=True)
    is_active = Column(Boolean, default=True, nullable=False)
    ip_address = Column(INET)
    user_agent = Column(String)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    last_accessed_at = Column(DateTime(timezone=True))
    
    user = relationship("User", back_populates="sessions")

class AuthEvent(Base):
    __tablename__ = "auth_events"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), index=True)
    event_type = Column(String(50), nullable=False)
    status = Column(String(20), nullable=False)
    ip_address = Column(INET)
    user_agent = Column(String)
    additional_data = Column(JSON)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    
    user = relationship("User", back_populates="auth_events")

class OTP(Base):
    __tablename__ = "otps"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, nullable=False, index=True)
    otp_code = Column(String, nullable=False)
    otp_type = Column(String(50), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    
    __table_args__ = (UniqueConstraint('email', 'otp_type', name='_email_otp_type_uc'),)

class OTPRateLimit(Base):
    __tablename__ = "otp_rate_limits"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False, index=True)

class UserPreferences(Base):
    __tablename__ = "user_preferences"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, unique=True)
    preferences = Column(JSON, default=lambda: {})
    
    user = relationship("User", back_populates="preferences")


--- app/processors/base.py.txt ---
"""
Base document processor module.

This module defines the base class for document processors and common utilities.
It combines functionality from the original processors system and the advanced
doc_process system, providing enhanced capabilities with AI integration.
"""
import logging
import os
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

# Import database clients for direct database operations
from app.db.neo4j_client import Neo4jClient
from app.db.qdrant_client import QdrantClient

logger = logging.getLogger(__name__)

# Try to import optional dependencies with fallbacks
try:
    import cognee
    from cognee.api.v1.search import SearchType
    COGNEE_AVAILABLE = True
except ImportError:
    COGNEE_AVAILABLE = False
    logger.warning("Cognee not available. Some advanced features will be disabled.")

try:
    from langchain_xai import ChatXAI
    XAI_AVAILABLE = True
except ImportError:
    XAI_AVAILABLE = False
    logger.warning("LangChain XAI not available. AI enhancements will be disabled.")


class DatabaseAdapter:
    """
    Adapter for database operations that can use either direct database
    clients or the Cognee abstraction layer.
    """
    
    def __init__(self, use_cognee: bool = False):
        """
        Initialize the database adapter.
        
        Args:
            use_cognee: Whether to use Cognee for database operations
        """
        self.use_cognee = use_cognee and COGNEE_AVAILABLE
        
        if self.use_cognee:
            # Initialize Cognee
            try:
                cognee.config.set_vector_db_provider("qdrant")
                cognee.config.set_graph_db_provider("neo4j")
                cognee.config.vector_db_url = os.getenv("VECTOR_DB_URL", "")
                cognee.config.vector_db_key = os.getenv("VECTOR_DB_KEY", "")
                cognee.config.graph_db_url = os.getenv("NEO4J_URI", "")
                cognee.config.graph_db_username = os.getenv("NEO4J_USERNAME", "")
                cognee.config.graph_db_password = os.getenv("NEO4J_PASSWORD", "")
                cognee.config.chunk_size = 1024
                cognee.config.chunk_overlap = 128
                self.cognee = cognee
            except Exception as e:
                logger.error(f"Failed to initialize Cognee: {e}")
                self.use_cognee = False
        
        # Always initialize direct clients as fallback
        self.neo4j_client = Neo4jClient()
        self.qdrant_client = QdrantClient()
    
    async def store_in_knowledge_graph(self, data: Dict[str, Any]) -> Optional[str]:
        """
        Store data in the knowledge graph.
        
        Args:
            data: Data to store
            
        Returns:
            ID of the stored node
        """
        if self.use_cognee:
            try:
                # Use Cognee's graph functionality
                await self.cognee.add_to_graph(data)
                return data.get("id")
            except Exception as e:
                logger.error(f"Cognee graph storage failed: {e}, falling back to direct Neo4j")
        
        # Create a node in Neo4j
        query = """
        CREATE (d:Document {
            id: $id,
            title: $title,
            content_type: $content_type,
            created_at: $created_at,
            updated_at: $updated_at
        })
        RETURN d.id as id
        """
        
        params = {
            "id": data.get("id", str(datetime.now().timestamp())),
            "title": data.get("title", "Untitled Document"),
            "content_type": data.get("content_type", "text"),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        result = await self.neo4j_client.run_query(query, params)
        return result[0]["id"] if result else None
    
    async def store_in_vector_db(self, embedding: List[float], metadata: Dict[str, Any]) -> Optional[str]:
        """
        Store embedding in vector database.
        
        Args:
            embedding: Vector embedding
            metadata: Metadata to store with the embedding
            
        Returns:
            ID of the stored vector
        """
        if self.use_cognee:
            try:
                # Use Cognee's vector database functionality
                await self.cognee.add_to_vector_db(embedding, metadata)
                return metadata.get("id")
            except Exception as e:
                logger.error(f"Cognee vector storage failed: {e}, falling back to direct Qdrant")
        
        # Store in Qdrant
        ids = await self.qdrant_client.store_vectors(
            vectors=[embedding],
            metadata=[metadata],
            ids=[metadata.get("id")]
        )
        return ids[0] if ids else None


class AIEnhancementLayer:
    """
    Optional AI enhancement layer for document processing.
    Provides advanced analysis capabilities when available.
    """
    
    def __init__(self):
        """Initialize the AI enhancement layer."""
        self.ai_model = None
        self.initialized = False
        
        # Try to initialize the AI model
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the AI model based on available providers."""
        if not self.initialized:
            # Try XAI (Grok) first
            if XAI_AVAILABLE and os.getenv("XAI_API_KEY"):
                try:
                    self.ai_model = ChatXAI(
                        xai_api_key=os.getenv("XAI_API_KEY"),
                        model="grok-4",
                        temperature=0.1
                    )
                    self.initialized = True
                    logger.info("Initialized XAI enhancement layer with Grok-4")
                except Exception as e:
                    logger.error(f"Failed to initialize XAI: {e}")
            
            # Try OpenAI if XAI is not available
            if not self.initialized:
                try:
                    from langchain_openai import ChatOpenAI
                    if os.getenv("OPENAI_API_KEY"):
                        self.ai_model = ChatOpenAI(
                            api_key=os.getenv("OPENAI_API_KEY"),
                            model="gpt-4o",
                            temperature=0.1
                        )
                        self.initialized = True
                        logger.info("Initialized OpenAI enhancement layer with GPT-4o")
                except ImportError:
                    logger.warning("LangChain OpenAI not available")
    
    async def enhance_content(self, content: Any, content_type: str, enhancement_type: str = "analysis") -> Optional[str]:
        """
        Enhance content with AI analysis.
        
        Args:
            content: Content to enhance
            content_type: Type of content
            enhancement_type: Type of enhancement to perform
            
        Returns:
            Enhanced content or None if enhancement fails
        """
        if not self.initialized or not self.ai_model:
            logger.warning("AI enhancement requested but no AI model is available")
            return None
        
        # Create prompt based on content type and enhancement type
        prompt = self._create_enhancement_prompt(content, content_type, enhancement_type)
        
        try:
            # Invoke the AI model
            result = await self.ai_model.ainvoke(prompt)
            return result.content if hasattr(result, "content") else str(result)
        except Exception as e:
            logger.error(f"AI enhancement failed: {e}")
            return None
    
    def _create_enhancement_prompt(self, content: Any, content_type: str, enhancement_type: str) -> str:
        """
        Create a prompt for AI enhancement.
        
        Args:
            content: Content to enhance
            content_type: Type of content
            enhancement_type: Type of enhancement
            
        Returns:
            Prompt for the AI model
        """
        # Base prompt template
        if enhancement_type == "analysis":
            return f"""
            Analyze the following {content_type} content and provide insights:
            
            {content}
            
            Extract:
            1. Key topics and themes
            2. Important entities and relationships
            3. Main insights and findings
            4. Structure and organization
            """
        elif enhancement_type == "summary":
            return f"""
            Summarize the following {content_type} content:
            
            {content}
            
            Provide a concise summary highlighting the most important information.
            """
        else:
            return f"""
            Process the following {content_type} content for {enhancement_type}:
            
            {content}
            """


class BaseProcessor(ABC):
    """
    Enhanced base class for document processors.
    
    This abstract class defines the interface and common functionality
    for all document processors in the system, combining features from
    the original processors and the advanced doc_process system.
    """
    
    def __init__(
        self,
        dataset_name: str = None,
        use_cognee: bool = False,
        enable_ai: bool = False
    ):
        """
        Initialize the base processor.
        
        Args:
            dataset_name: Name of the dataset (for Cognee integration)
            use_cognee: Whether to use Cognee for database operations
            enable_ai: Whether to enable AI enhancements
        """
        # Set up database adapter
        self.db_adapter = DatabaseAdapter(use_cognee=use_cognee)
        
        # Set up AI enhancement layer if enabled
        self.enable_ai = enable_ai
        if enable_ai:
            self.ai_layer = AIEnhancementLayer()
        
        # Set dataset name for Cognee
        self.dataset_name = dataset_name or self.__class__.__name__.lower()
        
        # Initialize metadata
        self.metadata = {}
    
    @abstractmethod
    async def process(self, content: Any, **kwargs) -> Dict[str, Any]:
        """
        Process the document content.
        
        Args:
            content: Document content to process
            **kwargs: Additional processing options
            
        Returns:
            Processing result
        """
        pass
    
    async def process_with_enhancements(self, content: Any, **kwargs) -> Dict[str, Any]:
        """
        Process the document content with AI enhancements if enabled.
        
        Args:
            content: Document content to process
            **kwargs: Additional processing options
            
        Returns:
            Enhanced processing result
        """
        # Standard processing first
        result = await self.process(content, **kwargs)
        
        # Apply AI enhancements if enabled
        if self.enable_ai and hasattr(self, "ai_layer"):
            content_type = kwargs.get("content_type") or self._detect_content_type(content)
            enhancement_type = kwargs.get("enhancement_type", "analysis")
            
            enhanced_content = await self.ai_layer.enhance_content(
                content=result.get("processed_content", content),
                content_type=content_type,
                enhancement_type=enhancement_type
            )
            
            if enhanced_content:
                result["enhanced_content"] = enhanced_content
                result["has_enhancements"] = True
        
        return result
    
    def _detect_content_type(self, content: Any) -> str:
        """
        Detect the content type based on the content itself.
        
        Args:
            content: Content to detect type for
            
        Returns:
            Detected content type
        """
        # Simple detection based on content
        if isinstance(content, str):
            if content.strip().startswith(("{", "[")):
                return "application/json"
            elif content.strip().startswith("#"):
                return "text/markdown"
            else:
                return "text/plain"
        elif isinstance(content, bytes):
            if content.startswith(b"%PDF"):
                return "application/pdf"
            elif content.startswith((b"\x89PNG", b"\xFF\xD8\xFF")):
                return "image"
            else:
                return "application/octet-stream"
        else:
            return "unknown"
    
    async def extract_text(self, content: Any) -> str:
        """
        Extract text from content.
        
        Args:
            content: Content to extract text from
            
        Returns:
            Extracted text
        """
        # Default implementation returns content as string if possible
        if isinstance(content, str):
            return content
        elif hasattr(content, "read"):
            return await content.read()
        else:
            return str(content)
    
    async def generate_embeddings(self, text: str, model: str = "text-embedding-3-large") -> List[float]:
        """
        Generate embeddings for text.
        
        Args:
            text: Text to generate embeddings for
            model: Embedding model to use
            
        Returns:
            Vector embedding
        """
        # If Cognee is available, try to use it for embeddings
        if self.db_adapter.use_cognee:
            try:
                embedding = await self.db_adapter.cognee.generate_embedding(text)
                return embedding
            except Exception as e:
                logger.error(f"Cognee embedding generation failed: {e}, falling back to mock")
        
        # Placeholder for actual embedding generation
        # In a real implementation, this would call an embedding API
        logger.info(f"Using mock embeddings with model: {model}")
        
        # Return a mock embedding (would be replaced with actual API call)
        return [0.0] * 1536  # OpenAI embeddings are 1536 dimensions
    
    async def store_in_knowledge_graph(self, data: Dict[str, Any]) -> str:
        """
        Store data in the knowledge graph.
        
        Args:
            data: Data to store
            
        Returns:
            ID of the stored node
        """
        return await self.db_adapter.store_in_knowledge_graph(data)
    
    async def store_in_vector_db(self, embedding: List[float], metadata: Dict[str, Any]) -> str:
        """
        Store embedding in vector database.
        
        Args:
            embedding: Vector embedding
            metadata: Metadata to store with the embedding
            
        Returns:
            ID of the stored vector
        """
        return await self.db_adapter.store_in_vector_db(embedding, metadata)
    
    async def search_content(self, query: str) -> List[Dict[str, Any]]:
        """
        Search processed content.
        
        Args:
            query: Search query
            
        Returns:
            Search results
        """
        if self.db_adapter.use_cognee:
            try:
                results = await self.db_adapter.cognee.search(
                    query_text=query,
                    query_type=SearchType.INSIGHTS if self.enable_ai else SearchType.RELEVANT
                )
                return results
            except Exception as e:
                logger.error(f"Cognee search failed: {e}, falling back to direct search")
        
        # Fallback to direct search
        # This would be implemented with direct vector search in Qdrant
        logger.warning("Direct vector search not fully implemented")
        return []
    
    def add_metadata(self, key: str, value: Any) -> None:
        """
        Add metadata to the processor.
        
        Args:
            key: Metadata key
            value: Metadata value
        """
        self.metadata[key] = value
    
    def get_metadata(self) -> Dict[str, Any]:
        """
        Get all metadata.
        
        Returns:
            Metadata dictionary
        """
        return self.metadata.copy() 

--- app/processors/code_processor.py.txt ---
"""
Code file processor.

This module provides a processor for source code files that extracts structure,
functions, classes, and other code elements with language-specific handling.
"""
import logging
import re
from typing import Dict, Any, List, Optional, Tuple, Set
from pathlib import Path

# Code parsing libraries
try:
    import pygments
    from pygments.lexers import get_lexer_for_filename, get_lexer_by_name
    from pygments.token import Token
    PYGMENTS_AVAILABLE = True
except ImportError:
    PYGMENTS_AVAILABLE = False

from .base import BaseProcessor

logger = logging.getLogger(__name__)

class CodeProcessor(BaseProcessor):
    """
    Processor for source code files.
    
    This processor extracts structure, functions, classes, and other code elements
    from source code files with language-specific handling.
    """
    
    # Language-specific patterns for extracting structure
    LANGUAGE_PATTERNS = {
        'python': {
            'function': r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(',
            'class': r'class\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*(\(|:)',
            'import': r'(?:from\s+([a-zA-Z0-9_.]+)\s+)?import\s+([a-zA-Z0-9_.*]+)',
            'comment': r'#.*$',
            'docstring': r'"""[\s\S]*?"""|\'\'\'[\s\S]*?\'\'\'',
        },
        'javascript': {
            'function': r'(?:function\s+([a-zA-Z_][a-zA-Z0-9_]*)|(?:const|let|var)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*(?:async\s*)?(?:function|\([^)]*\)\s*=>))',
            'class': r'class\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            'import': r'import\s+(?:\*\s+as\s+([a-zA-Z0-9_]+)|{([^}]+)}|([a-zA-Z0-9_]+))\s+from\s+[\'"]([^\'"]+)[\'"]',
            'comment': r'//.*$|/\*[\s\S]*?\*/',
        },
        'typescript': {
            'function': r'(?:function\s+([a-zA-Z_][a-zA-Z0-9_]*)|(?:const|let|var)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*(?:async\s*)?(?:function|\([^)]*\)\s*=>))',
            'class': r'class\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            'interface': r'interface\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            'type': r'type\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=',
            'import': r'import\s+(?:\*\s+as\s+([a-zA-Z0-9_]+)|{([^}]+)}|([a-zA-Z0-9_]+))\s+from\s+[\'"]([^\'"]+)[\'"]',
            'comment': r'//.*$|/\*[\s\S]*?\*/',
        },
        'java': {
            'function': r'(?:public|private|protected|static|\s) +[\w\<\>\[\]]+\s+(\w+) *\([^\)]*\)',
            'class': r'(?:public|private|protected|static|\s) +class +(\w+)',
            'interface': r'(?:public|private|protected|static|\s) +interface +(\w+)',
            'import': r'import\s+([a-zA-Z0-9_.]+(?:\.[*])?);',
            'comment': r'//.*$|/\*[\s\S]*?\*/',
        },
        'c': {
            'function': r'(?:[a-zA-Z_][a-zA-Z0-9_]*\s+)+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^;]*\)\s*{',
            'struct': r'struct\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*{',
            'include': r'#include\s+[<"]([^>"]+)[>"]',
            'comment': r'//.*$|/\*[\s\S]*?\*/',
        },
        'cpp': {
            'function': r'(?:[a-zA-Z_][a-zA-Z0-9_:]*\s+)+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^;]*\)\s*{',
            'class': r'class\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            'struct': r'struct\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*{',
            'include': r'#include\s+[<"]([^>"]+)[>"]',
            'namespace': r'namespace\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            'comment': r'//.*$|/\*[\s\S]*?\*/',
        },
        'go': {
            'function': r'func\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(',
            'struct': r'type\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+struct',
            'interface': r'type\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+interface',
            'import': r'import\s+(?:"([^"]+)"|(?:\(\s*(?:"[^"]+"\s*)+\)))',
            'comment': r'//.*$|/\*[\s\S]*?\*/',
        },
        'rust': {
            'function': r'fn\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(',
            'struct': r'struct\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            'enum': r'enum\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            'trait': r'trait\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            'impl': r'impl(?:\s+<[^>]+>)?\s+(?:[^{]+)\s+for\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            'use': r'use\s+([^;]+);',
            'comment': r'//.*$|/\*[\s\S]*?\*/',
        },
        # Add more languages as needed
    }
    
    # File extension to language mapping
    EXTENSION_TO_LANGUAGE = {
        '.py': 'python',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.jsx': 'javascript',
        '.tsx': 'typescript',
        '.java': 'java',
        '.c': 'c',
        '.h': 'c',
        '.cpp': 'cpp',
        '.hpp': 'cpp',
        '.cc': 'cpp',
        '.go': 'go',
        '.rs': 'rust',
        # Add more extensions as needed
    }
    
    def __init__(self, 
                 extract_structure: bool = True,
                 extract_dependencies: bool = True,
                 extract_comments: bool = True,
                 highlight_syntax: bool = True,
                 **kwargs):
        """
        Initialize the code processor.
        
        Args:
            extract_structure: Whether to extract code structure (functions, classes, etc.)
            extract_dependencies: Whether to extract dependencies (imports, includes, etc.)
            extract_comments: Whether to extract comments and docstrings
            highlight_syntax: Whether to perform syntax highlighting
            **kwargs: Additional options for the base processor
        """
        super().__init__(**kwargs)
        
        if highlight_syntax and not PYGMENTS_AVAILABLE:
            logger.warning(
                "Pygments is required for syntax highlighting. "
                "Install it with: pip install pygments"
            )
            highlight_syntax = False
        
        self.extract_structure = extract_structure
        self.extract_dependencies = extract_dependencies
        self.extract_comments = extract_comments
        self.highlight_syntax = highlight_syntax and PYGMENTS_AVAILABLE
    
    def process(self, content: Any, metadata: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """
        Process a code file.
        
        Args:
            content: Code content (string, bytes, or file path)
            metadata: Document metadata
            **kwargs: Additional processing options
            
        Returns:
            Processing results including extracted structure and text
        """
        if metadata is None:
            metadata = {}
        
        # Prepare code content for processing
        code_content, language = self._prepare_content(content, metadata.get('file_path'), metadata.get('language'))
        
        # Update metadata with detected language
        metadata['language'] = language
        metadata['content_type'] = f'text/{language}'
        
        # Extract structure if requested
        structure = {}
        if self.extract_structure:
            structure = self._extract_structure(code_content, language)
            
            # Add structure summary to metadata
            if structure:
                for key, items in structure.items():
                    if items:
                        metadata[f'{key}_count'] = len(items)
        
        # Extract dependencies if requested
        dependencies = []
        if self.extract_dependencies:
            dependencies = self._extract_dependencies(code_content, language)
            
            # Add dependencies to metadata
            if dependencies:
                metadata['dependencies_count'] = len(dependencies)
        
        # Extract comments if requested
        comments = []
        if self.extract_comments:
            comments = self._extract_comments(code_content, language)
            
            # Add comments to metadata
            if comments:
                metadata['comments_count'] = len(comments)
        
        # Perform syntax highlighting if requested
        highlighted_code = None
        if self.highlight_syntax:
            highlighted_code = self._highlight_syntax(code_content, language)
        
        # Create chunks based on the code structure
        chunks = self.create_chunks(code_content, structure=structure, language=language, **kwargs)
        
        # Prepare result
        result = {
            'chunks': chunks,
            'extracted_text': code_content,
            'metadata': metadata
        }
        
        # Add structure if extracted
        if structure:
            result['structure'] = structure
            
        # Add dependencies if extracted
        if dependencies:
            result['dependencies'] = dependencies
            
        # Add comments if extracted
        if comments:
            result['comments'] = comments
            
        # Add highlighted code if generated
        if highlighted_code:
            result['highlighted_code'] = highlighted_code
        
        return result
    
    def _prepare_content(self, content: Any, file_path: Optional[str] = None, language: Optional[str] = None) -> Tuple[str, str]:
        """
        Prepare code content for processing and detect language.
        
        Args:
            content: Code content (string, bytes, or file path)
            file_path: Path to the file (optional, used for language detection)
            language: Language identifier (optional, overrides detection)
            
        Returns:
            Tuple of (code content as string, detected language)
        """
        # Get content as string
        if isinstance(content, bytes):
            code_content = content.decode('utf-8', errors='replace')
        elif isinstance(content, str):
            if Path(content).exists() and file_path is None:
                # Content is a file path
                file_path = content
                with open(content, 'r', encoding='utf-8', errors='replace') as f:
                    code_content = f.read()
            else:
                # Content is the actual code
                code_content = content
        elif hasattr(content, 'read'):
            # File-like object
            code_content = content.read()
            if isinstance(code_content, bytes):
                code_content = code_content.decode('utf-8', errors='replace')
        else:
            raise ValueError(f"Unsupported content type: {type(content)}")
        
        # Detect language
        detected_language = language
        
        if not detected_language and file_path:
            # Try to detect from file extension
            ext = Path(file_path).suffix.lower()
            detected_language = self.EXTENSION_TO_LANGUAGE.get(ext)
        
        if not detected_language and PYGMENTS_AVAILABLE:
            # Try to detect using Pygments
            try:
                if file_path:
                    lexer = get_lexer_for_filename(file_path, code=code_content)
                else:
                    lexer = pygments.lexers.guess_lexer(code_content)
                
                detected_language = lexer.aliases[0]
            except Exception as e:
                logger.warning(f"Failed to detect language using Pygments: {e}")
        
        # Default to plaintext if detection failed
        if not detected_language:
            detected_language = 'plaintext'
        
        return code_content, detected_language
    
    def _extract_structure(self, code_content: str, language: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        Extract code structure (functions, classes, etc.).
        
        Args:
            code_content: Code content as string
            language: Programming language
            
        Returns:
            Dictionary of extracted structure elements
        """
        structure = {}
        
        # Get language-specific patterns
        patterns = self.LANGUAGE_PATTERNS.get(language, {})
        
        # Extract each type of structure element
        for element_type, pattern in patterns.items():
            if element_type == 'comment':  # Skip comment pattern here
                continue
                
            matches = []
            for match in re.finditer(pattern, code_content, re.MULTILINE):
                # Get the line number
                line_number = code_content[:match.start()].count('\n') + 1
                
                # Get the matched name (first capturing group)
                name = next((group for group in match.groups() if group), '')
                
                # Get the context (surrounding lines)
                lines = code_content.split('\n')
                start_line = max(0, line_number - 2)
                end_line = min(len(lines), line_number + 2)
                context = '\n'.join(lines[start_line:end_line])
                
                matches.append({
                    'name': name,
                    'line': line_number,
                    'start': match.start(),
                    'end': match.end(),
                    'context': context
                })
            
            if matches:
                structure[element_type] = matches
        
        return structure
    
    def _extract_dependencies(self, code_content: str, language: str) -> List[Dict[str, Any]]:
        """
        Extract dependencies (imports, includes, etc.).
        
        Args:
            code_content: Code content as string
            language: Programming language
            
        Returns:
            List of extracted dependencies
        """
        dependencies = []
        
        # Get language-specific patterns
        patterns = self.LANGUAGE_PATTERNS.get(language, {})
        
        # Extract dependencies based on language
        if language in ['python', 'javascript', 'typescript', 'java', 'go', 'rust']:
            pattern_key = 'import' if language != 'rust' else 'use'
            if pattern_key in patterns:
                for match in re.finditer(patterns[pattern_key], code_content, re.MULTILINE):
                    # Get the line number
                    line_number = code_content[:match.start()].count('\n') + 1
                    
                    # Process the match based on language
                    if language == 'python':
                        module = match.group(1) or ''
                        imports = match.group(2) or ''
                        dependencies.append({
                            'type': 'import',
                            'module': module,
                            'imports': imports,
                            'line': line_number
                        })
                    elif language in ['javascript', 'typescript']:
                        source = match.group(4) or ''
                        dependencies.append({
                            'type': 'import',
                            'source': source,
                            'line': line_number
                        })
                    elif language == 'java':
                        package = match.group(1) or ''
                        dependencies.append({
                            'type': 'import',
                            'package': package,
                            'line': line_number
                        })
                    elif language == 'go':
                        package = match.group(1) or ''
                        dependencies.append({
                            'type': 'import',
                            'package': package,
                            'line': line_number
                        })
                    elif language == 'rust':
                        path = match.group(1) or ''
                        dependencies.append({
                            'type': 'use',
                            'path': path,
                            'line': line_number
                        })
        elif language in ['c', 'cpp']:
            for match in re.finditer(patterns['include'], code_content, re.MULTILINE):
                # Get the line number
                line_number = code_content[:match.start()].count('\n') + 1
                
                header = match.group(1) or ''
                dependencies.append({
                    'type': 'include',
                    'header': header,
                    'line': line_number
                })
        
        return dependencies
    
    def _extract_comments(self, code_content: str, language: str) -> List[Dict[str, Any]]:
        """
        Extract comments and docstrings.
        
        Args:
            code_content: Code content as string
            language: Programming language
            
        Returns:
            List of extracted comments
        """
        comments = []
        
        # Get language-specific patterns
        patterns = self.LANGUAGE_PATTERNS.get(language, {})
        
        # Extract comments
        if 'comment' in patterns:
            for match in re.finditer(patterns['comment'], code_content, re.MULTILINE):
                # Get the line number
                line_number = code_content[:match.start()].count('\n') + 1
                
                # Get the comment text
                text = match.group(0).strip()
                
                # Skip empty comments
                if not text or (language in ['python'] and text == '#'):
                    continue
                
                # Clean up the comment text
                if language in ['python']:
                    text = text.lstrip('#').strip()
                elif language in ['javascript', 'typescript', 'java', 'c', 'cpp', 'go', 'rust']:
                    if text.startswith('//'):
                        text = text[2:].strip()
                    elif text.startswith('/*') and text.endswith('*/'):
                        text = text[2:-2].strip()
                
                comments.append({
                    'type': 'comment',
                    'text': text,
                    'line': line_number
                })
        
        # Extract Python docstrings
        if language == 'python' and 'docstring' in patterns:
            for match in re.finditer(patterns['docstring'], code_content, re.MULTILINE):
                # Get the line number
                line_number = code_content[:match.start()].count('\n') + 1
                
                # Get the docstring text
                text = match.group(0).strip()
                
                # Clean up the docstring text
                if text.startswith('"""') and text.endswith('"""'):
                    text = text[3:-3].strip()
                elif text.startswith("'''") and text.endswith("'''"):
                    text = text[3:-3].strip()
                
                comments.append({
                    'type': 'docstring',
                    'text': text,
                    'line': line_number
                })
        
        return comments
    
    def _highlight_syntax(self, code_content: str, language: str) -> Optional[str]:
        """
        Perform syntax highlighting.
        
        Args:
            code_content: Code content as string
            language: Programming language
            
        Returns:
            HTML with syntax highlighting or None if highlighting failed
        """
        if not PYGMENTS_AVAILABLE:
            return None
        
        try:
            # Get lexer for the language
            try:
                lexer = get_lexer_by_name(language)
            except pygments.util.ClassNotFound:
                # Try with common aliases
                language_aliases = {
                    'javascript': 'js',
                    'typescript': 'ts',
                    'python': 'py',
                }
                alias = language_aliases.get(language)
                if alias:
                    try:
                        lexer = get_lexer_by_name(alias)
                    except pygments.util.ClassNotFound:
                        return None
                else:
                    return None
            
            # Highlight code
            from pygments.formatters import HtmlFormatter
            formatter = HtmlFormatter(linenos=True, cssclass="source")
            highlighted = pygments.highlight(code_content, lexer, formatter)
            
            return highlighted
        except Exception as e:
            logger.warning(f"Failed to highlight syntax: {e}")
            return None
    
    def create_chunks(self, text: str, structure: Dict[str, List[Dict[str, Any]]] = None, 
                     language: str = None, **kwargs) -> List[Dict[str, Any]]:
        """
        Create chunks from code, using structure information if available.
        
        Args:
            text: Code content
            structure: Code structure information
            language: Programming language
            **kwargs: Additional chunking options
            
        Returns:
            List of code chunks
        """
        chunks = []
        
        # If we have structure information, use it for structure-aware chunking
        if structure and language:
            # Combine all structure elements for sorting by position
            all_elements = []
            for element_type, elements in structure.items():
                for element in elements:
                    all_elements.append({
                        'type': element_type,
                        'name': element.get('name', ''),
                        'start': element.get('start', 0),
                        'end': element.get('end', 0),
                        'line': element.get('line', 0)
                    })
            
            # Sort elements by start position
            all_elements.sort(key=lambda x: x['start'])
            
            # Create chunks based on structure elements
            if all_elements:
                # Create chunks for each significant code section
                for i, element in enumerate(all_elements):
                    # Determine the end of this chunk
                    if i < len(all_elements) - 1:
                        next_start = all_elements[i + 1]['start']
                    else:
                        next_start = len(text)
                    
                    # Extract the chunk text
                    chunk_text = text[element['start']:next_start].strip()
                    
                    # Skip empty chunks
                    if not chunk_text:
                        continue
                    
                    # Create the chunk
                    chunk = {
                        'text': chunk_text,
                        'type': element['type'],
                        'metadata': {
                            'language': language,
                            'name': element['name'],
                            'line': element['line']
                        }
                    }
                    chunks.append(chunk)
                
                return chunks
        
        # Fall back to standard chunking if we couldn't use structure
        # For code, we'll use smaller chunks than the default
        chunk_size = kwargs.get('chunk_size', 1000)
        chunk_overlap = kwargs.get('chunk_overlap', 100)
        
        # Split by lines first to avoid breaking in the middle of a line
        lines = text.split('\n')
        current_chunk = []
        current_length = 0
        
        for line in lines:
            line_length = len(line) + 1  # +1 for the newline
            
            if current_length + line_length > chunk_size and current_chunk:
                # Create a chunk from the accumulated lines
                chunk_text = '\n'.join(current_chunk)
                chunks.append({
                    'text': chunk_text,
                    'type': 'code',
                    'metadata': {'language': language} if language else {}
                })
                
                # Start a new chunk with overlap
                overlap_lines = []
                overlap_length = 0
                for prev_line in reversed(current_chunk):
                    if overlap_length + len(prev_line) + 1 <= chunk_overlap:
                        overlap_lines.insert(0, prev_line)
                        overlap_length += len(prev_line) + 1
                    else:
                        break
                
                current_chunk = overlap_lines
                current_length = overlap_length
            
            current_chunk.append(line)
            current_length += line_length
        
        # Add the final chunk if there's anything left
        if current_chunk:
            chunk_text = '\n'.join(current_chunk)
            chunks.append({
                'text': chunk_text,
                'type': 'code',
                'metadata': {'language': language} if language else {}
            })
        
        return chunks
    
    def store_in_graph(self, document_id: str, metadata: Dict[str, Any], 
                      chunks: List[Dict[str, Any]], **kwargs) -> None:
        """
        Store code document in graph database.
        
        Args:
            document_id: Document ID
            metadata: Document metadata
            chunks: Document chunks
            **kwargs: Additional storage options
        """
        # Get language from metadata
        language = metadata.get('language', 'unknown')
        
        # Add code-specific metadata
        metadata['content_type'] = f'text/{language}'
        
        # Store document node
        doc_properties = {
            'id': document_id,
            'content_type': f'text/{language}',
            'language': language,
            **metadata
        }
        
        # Create document node
        self.graph_db.create_node('Document', doc_properties)
        
        # Store each chunk
        for i, chunk in enumerate(chunks):
            chunk_id = f"{document_id}_chunk_{i}"
            chunk_type = chunk.get('type', 'code')
            
            # Create chunk node
            chunk_properties = {
                'id': chunk_id,
                'text': chunk['text'],
                'type': chunk_type,
                'index': i,
                **chunk.get('metadata', {})
            }
            self.graph_db.create_node('Chunk', chunk_properties)
            
            # Connect chunk to document
            self.graph_db.create_relationship(
                'Document', {'id': document_id},
                'HAS_CHUNK', {},
                'Chunk', {'id': chunk_id}
            )
            
            # Create special relationships based on chunk type
            if chunk_type in ['function', 'class', 'struct', 'interface']:
                # Create a node for the code element
                element_name = chunk.get('metadata', {}).get('name', '')
                if element_name:
                    element_id = f"{document_id}_{chunk_type}_{element_name}"
                    self.graph_db.create_node(chunk_type.capitalize(), {
                        'id': element_id,
                        'name': element_name,
                        'language': language
                    })
                    
                    # Connect chunk to element
                    self.graph_db.create_relationship(
                        chunk_type.capitalize(), {'id': element_id},
                        'HAS_IMPLEMENTATION', {},
                        'Chunk', {'id': chunk_id}
                    )
                    
                    # Connect document to element
                    self.graph_db.create_relationship(
                        'Document', {'id': document_id},
                        f'HAS_{chunk_type.upper()}', {},
                        chunk_type.capitalize(), {'id': element_id}
                    )
        
        # Store dependencies if available
        if 'dependencies' in kwargs:
            for i, dep in enumerate(kwargs['dependencies']):
                dep_id = f"{document_id}_dependency_{i}"
                
                # Create dependency node
                dep_properties = {
                    'id': dep_id,
                    **dep
                }
                self.graph_db.create_node('Dependency', dep_properties)
                
                # Connect document to dependency
                self.graph_db.create_relationship(
                    'Document', {'id': document_id},
                    'DEPENDS_ON', {},
                    'Dependency', {'id': dep_id}
                )
    
    def store_in_vector_db(self, document_id: str, metadata: Dict[str, Any],
                          chunks: List[Dict[str, Any]], **kwargs) -> None:
        """
        Store code document in vector database.
        
        Args:
            document_id: Document ID
            metadata: Document metadata
            chunks: Document chunks
            **kwargs: Additional storage options
        """
        # Get language from metadata
        language = metadata.get('language', 'unknown')
        
        # Add code-specific metadata
        metadata['content_type'] = f'text/{language}'
        
        # Store each chunk with its embeddings
        for i, chunk in enumerate(chunks):
            chunk_id = f"{document_id}_chunk_{i}"
            chunk_text = chunk['text']
            
            # Get embeddings for the chunk
            embeddings = self.get_embeddings(chunk_text)
            
            # Prepare chunk metadata
            chunk_metadata = {
                'document_id': document_id,
                'chunk_id': chunk_id,
                'chunk_index': i,
                'chunk_type': chunk.get('type', 'code'),
                'language': language,
                **metadata,
                **chunk.get('metadata', {})
            }
            
            # Store in vector database
            self.vector_db.add_vectors(
                collection_name=kwargs.get('collection_name', 'documents'),
                vectors=[(chunk_id, embeddings, chunk_metadata)],
                batch_size=kwargs.get('batch_size', 100)
            ) 

--- app/processors/config.py.txt ---
"""
Configuration module for document processing with Neo4j, Qdrant, and AI integration.

This module provides configuration for the document processing system,
supporting both direct database access and the Cognee abstraction layer.
"""
import os
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

# Import optional dependencies with graceful fallbacks
try:
    import cognee
    COGNEE_AVAILABLE = True
except ImportError:
    COGNEE_AVAILABLE = False
    logger.warning("Cognee is not available. Install with 'pip install cognee[neo4j,qdrant]>=0.1.40'")

try:
    from langchain_xai import ChatXAI
    XAI_AVAILABLE = True
except ImportError:
    XAI_AVAILABLE = False
    logger.warning("LangChain XAI is not available. Install with 'pip install langchain-xai'")

try:
    from langchain_openai import ChatOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("LangChain OpenAI is not available. Install with 'pip install langchain-openai'")

try:
    from pydantic import BaseModel, Field, model_validator
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    logger.warning("Pydantic is not available. Install with 'pip install pydantic>=2.0.0'")


# Define models if Pydantic is available
if PYDANTIC_AVAILABLE:
    class DatabaseConfig(BaseModel):
        """Database configuration model."""
        provider: str = Field(..., description="Database provider name")
        host: str = Field(..., description="Database host address")
        port: int = Field(..., description="Database port")
        username: str = Field(..., description="Database username")
        password: str = Field(..., description="Database password")
        name: Optional[str] = Field(None, description="Database name")
        url: Optional[str] = Field(None, description="Database URL")
        key: Optional[str] = Field(None, description="Database API key")

    class AIModelConfig(BaseModel):
        """AI model configuration."""
        provider: str = Field(..., description="AI model provider")
        model_name: str = Field(..., description="Model name/version")
        api_key: str = Field(..., description="API key for the model")
        temperature: float = Field(0.1, description="Temperature parameter")
        max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
        endpoint: Optional[str] = Field(None, description="Custom endpoint URL")

    class ProcessorConfig(BaseModel):
        """Main processor configuration model."""
        use_cognee: bool = Field(False, description="Whether to use Cognee")
        enable_ai: bool = Field(False, description="Whether to enable AI enhancements")
        databases: Dict[str, DatabaseConfig] = Field({}, description="Database configurations")
        ai_models: Dict[str, AIModelConfig] = Field({}, description="AI model configurations")
        chunk_size: int = Field(1024, description="Document chunk size")
        chunk_overlap: int = Field(128, description="Chunk overlap size")
        default_model: str = Field("openai", description="Default AI model to use")
        
        @model_validator(mode='after')
        def validate_config(self):
            """Validate configuration."""
            if self.enable_ai and not self.ai_models:
                logger.warning("AI is enabled but no AI models are configured")
            
            if self.use_cognee and not COGNEE_AVAILABLE:
                logger.warning("Cognee is not available but use_cognee is True")
            
            return self


class ProcessingConfig:
    """
    Configuration for document processing system.
    
    This class provides configuration for the document processing system,
    supporting both direct database access and the Cognee abstraction layer,
    as well as AI enhancement capabilities.
    """
    
    def __init__(self, use_cognee: bool = False, enable_ai: bool = False):
        """
        Initialize the processing configuration.
        
        Args:
            use_cognee: Whether to use Cognee
            enable_ai: Whether to enable AI enhancements
        """
        # Set configuration from environment or arguments
        self.use_cognee = use_cognee or os.getenv("USE_COGNEE", "false").lower() == "true"
        self.enable_ai = enable_ai or os.getenv("ENABLE_AI", "false").lower() == "true"
        
        # Load configuration
        self._load_environment()
        self._configure_databases()
        self._configure_ai_models()
        
        # Create Pydantic model if available
        self.config_model = None
        if PYDANTIC_AVAILABLE:
            self._create_config_model()
    
    def _load_environment(self):
        """Load configuration from environment variables."""
        # Document processing configuration
        self.chunk_size = int(os.getenv("CHUNK_SIZE", "1024"))
        self.chunk_overlap = int(os.getenv("CHUNK_OVERLAP", "128"))
        
        # Database configuration
        self.neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
        self.neo4j_username = os.getenv("NEO4J_USERNAME", "neo4j")
        self.neo4j_password = os.getenv("NEO4J_PASSWORD", "")
        
        self.vector_db_url = os.getenv("VECTOR_DB_URL", "")
        self.vector_db_key = os.getenv("VECTOR_DB_KEY", "")
        
        # AI configuration
        self.xai_api_key = os.getenv("XAI_API_KEY", "")
        self.openai_api_key = os.getenv("OPENAI_API_KEY", "")
        
        # Feature flags
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        logging.basicConfig(level=self.log_level)
    
    def _configure_databases(self):
        """Configure database connections."""
        self.databases = {}
        
        # Configure Neo4j
        self.databases["neo4j"] = {
            "provider": "neo4j",
            "host": self.neo4j_uri,
            "port": 7687,
            "username": self.neo4j_username,
            "password": self.neo4j_password
        }
        
        # Configure Qdrant
        self.databases["qdrant"] = {
            "provider": "qdrant",
            "url": self.vector_db_url,
            "key": self.vector_db_key
        }
        
        # Configure Cognee if available
        if COGNEE_AVAILABLE and self.use_cognee:
            try:
                cognee.config.set_graph_db_provider("neo4j")
                cognee.config.graph_db_url = self.neo4j_uri
                cognee.config.graph_db_username = self.neo4j_username
                cognee.config.graph_db_password = self.neo4j_password
                
                cognee.config.set_vector_db_provider("qdrant")
                cognee.config.vector_db_url = self.vector_db_url
                cognee.config.vector_db_key = self.vector_db_key
                
                cognee.config.chunk_size = self.chunk_size
                cognee.config.chunk_overlap = self.chunk_overlap
                
                self.cognee = cognee
                logger.info("Configured Cognee successfully")
            except Exception as e:
                logger.error(f"Failed to configure Cognee: {e}")
                self.use_cognee = False
    
    def _configure_ai_models(self):
        """Configure AI model integrations."""
        self.ai_models = {}
        self.available_models = []
        
        # Configure xAI if available
        if XAI_AVAILABLE and self.xai_api_key:
            try:
                self.ai_models["xai"] = {
                    "provider": "xai",
                    "model_name": "grok-4",
                    "api_key": self.xai_api_key,
                    "temperature": 0.1
                }
                self.available_models.append("xai")
                logger.info("Configured xAI model")
            except Exception as e:
                logger.error(f"Failed to configure xAI: {e}")
        
        # Configure OpenAI if available
        if OPENAI_AVAILABLE and self.openai_api_key:
            try:
                self.ai_models["openai"] = {
                    "provider": "openai",
                    "model_name": "gpt-4o",
                    "api_key": self.openai_api_key,
                    "temperature": 0.1
                }
                self.available_models.append("openai")
                logger.info("Configured OpenAI model")
            except Exception as e:
                logger.error(f"Failed to configure OpenAI: {e}")
        
        # Set default model
        self.default_model = os.getenv("DEFAULT_AI_MODEL", "openai")
        if self.default_model not in self.available_models and self.available_models:
            self.default_model = self.available_models[0]
            logger.warning(f"Default AI model not available, using {self.default_model} instead")
    
    def _create_config_model(self):
        """Create a Pydantic model from the configuration."""
        if not PYDANTIC_AVAILABLE:
            return
        
        try:
            # Create database configs
            db_configs = {}
            for db_name, db_info in self.databases.items():
                db_configs[db_name] = DatabaseConfig(**db_info)
            
            # Create AI model configs
            ai_configs = {}
            for model_name, model_info in self.ai_models.items():
                ai_configs[model_name] = AIModelConfig(**model_info)
            
            # Create main config
            self.config_model = ProcessorConfig(
                use_cognee=self.use_cognee,
                enable_ai=self.enable_ai,
                databases=db_configs,
                ai_models=ai_configs,
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap,
                default_model=self.default_model
            )
        except Exception as e:
            logger.error(f"Failed to create Pydantic model: {e}")
    
    def get_ai_model(self, model_name: Optional[str] = None):
        """
        Get an AI model client.
        
        Args:
            model_name: Name of the model to get (default: use default_model)
            
        Returns:
            AI model client or None if not available
        """
        if not self.enable_ai:
            logger.warning("AI is not enabled")
            return None
        
        # Use default model if not specified
        model_name = model_name or self.default_model
        
        # Check if model is available
        if model_name not in self.ai_models:
            logger.warning(f"Model {model_name} not available")
            return None
        
        # Get model configuration
        model_config = self.ai_models[model_name]
        
        # Initialize model client
        if model_name == "xai":
            if not XAI_AVAILABLE:
                logger.warning("XAI is not available")
                return None
            
            try:
                return ChatXAI(
                    xai_api_key=model_config["api_key"],
                    model=model_config["model_name"],
                    temperature=model_config["temperature"]
                )
            except Exception as e:
                logger.error(f"Failed to initialize XAI model: {e}")
                return None
        
        elif model_name == "openai":
            if not OPENAI_AVAILABLE:
                logger.warning("OpenAI is not available")
                return None
            
            try:
                return ChatOpenAI(
                    api_key=model_config["api_key"],
                    model=model_config["model_name"],
                    temperature=model_config["temperature"]
                )
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI model: {e}")
                return None
        
        logger.warning(f"Unknown model provider: {model_name}")
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dictionary representation of the configuration
        """
        # Use Pydantic model if available
        if PYDANTIC_AVAILABLE and self.config_model:
            return self.config_model.model_dump()
        
        # Manual conversion
        return {
            "use_cognee": self.use_cognee,
            "enable_ai": self.enable_ai,
            "chunk_size": self.chunk_size,
            "chunk_overlap": self.chunk_overlap,
            "default_model": self.default_model,
            "available_models": self.available_models,
            "databases": self.databases,
            "ai_models": {k: {**v, "api_key": "****"} for k, v in self.ai_models.items()}  # Redact API keys
        }


# Create a default instance for convenience
default_config = ProcessingConfig() 

--- app/processors/csv_processor.py.txt ---
"""
CSV document processor.

This module provides a processor for CSV documents.
"""
import csv
import io
import logging
from typing import Dict, List, Any, Optional, Union

from app.processors.base import BaseProcessor

logger = logging.getLogger(__name__)

class CsvProcessor(BaseProcessor):
    """
    Processor for CSV documents.
    
    This class provides methods for processing CSV documents,
    extracting structured data, and storing it in the knowledge graph
    and vector database.
    """
    
    async def process(self, content: Union[str, io.TextIOBase], **kwargs) -> Dict[str, Any]:
        """
        Process a CSV document.
        
        Args:
            content: CSV content as string or file-like object
            **kwargs: Additional processing options
                - has_header: Whether the CSV has a header row (default: True)
                - delimiter: CSV delimiter (default: ',')
                - store_as_json: Whether to store as JSON (default: True)
                - max_rows: Maximum number of rows to process (default: None)
                - other options from BaseProcessor
        
        Returns:
            Processing result with document ID and metadata
        """
        # Extract options
        has_header = kwargs.get("has_header", True)
        delimiter = kwargs.get("delimiter", ",")
        store_as_json = kwargs.get("store_as_json", True)
        max_rows = kwargs.get("max_rows")
        document_id = kwargs.get("document_id")
        metadata = kwargs.get("metadata", {})
        
        # Update metadata
        metadata["content_type"] = "text/csv"
        metadata["delimiter"] = delimiter
        metadata["has_header"] = has_header
        
        # Parse CSV
        if isinstance(content, str):
            csv_data = self._parse_csv_string(content, delimiter, has_header, max_rows)
        else:
            csv_data = self._parse_csv_file(content, delimiter, has_header, max_rows)
        
        # Add CSV stats to metadata
        metadata["row_count"] = len(csv_data)
        if csv_data and isinstance(csv_data[0], dict):
            metadata["columns"] = list(csv_data[0].keys())
        elif csv_data:
            metadata["column_count"] = len(csv_data[0])
        
        # Store as JSON if requested
        if store_as_json:
            from app.processors.json_processor import JsonProcessor
            json_processor = JsonProcessor()
            json_processor.metadata = self.metadata
            
            # Process as JSON
            result = await json_processor.process(
                csv_data,
                document_id=document_id,
                metadata=metadata,
                **kwargs
            )
            
            # Add CSV-specific data
            result["csv_processed"] = True
            result["csv_row_count"] = len(csv_data)
            
            return result
        
        # Otherwise, create a text representation and process as text
        text_representation = self._csv_to_text(csv_data)
        
        from app.processors.text_processor import TextProcessor
        text_processor = TextProcessor()
        text_processor.metadata = self.metadata
        
        # Process the text representation
        result = await text_processor.process(
            text_representation,
            document_id=document_id,
            metadata=metadata,
            **kwargs
        )
        
        # Add CSV-specific data
        result["csv_processed"] = True
        result["csv_row_count"] = len(csv_data)
        
        # Store additional relationships in Neo4j
        if result.get("document_id"):
            await self._store_csv_structure(result["document_id"], csv_data, has_header)
        
        return result
    
    def _parse_csv_string(self, content: str, delimiter: str, has_header: bool, 
                         max_rows: Optional[int] = None) -> List[Union[Dict[str, str], List[str]]]:
        """
        Parse CSV from string.
        
        Args:
            content: CSV content as string
            delimiter: CSV delimiter
            has_header: Whether the CSV has a header row
            max_rows: Maximum number of rows to process
            
        Returns:
            Parsed CSV data
        """
        # Use StringIO to create a file-like object
        with io.StringIO(content) as f:
            return self._parse_csv_file(f, delimiter, has_header, max_rows)
    
    def _parse_csv_file(self, file: io.TextIOBase, delimiter: str, has_header: bool,
                       max_rows: Optional[int] = None) -> List[Union[Dict[str, str], List[str]]]:
        """
        Parse CSV from file-like object.
        
        Args:
            file: File-like object containing CSV data
            delimiter: CSV delimiter
            has_header: Whether the CSV has a header row
            max_rows: Maximum number of rows to process
            
        Returns:
            Parsed CSV data
        """
        reader = csv.reader(file, delimiter=delimiter)
        rows = []
        
        # Read header if present
        header = next(reader) if has_header else None
        
        # Process rows
        row_count = 0
        for row in reader:
            if max_rows and row_count >= max_rows:
                break
                
            if header:
                # Create a dictionary using header as keys
                row_dict = {header[i]: value for i, value in enumerate(row) if i < len(header)}
                rows.append(row_dict)
            else:
                # Just store the row as a list
                rows.append(row)
                
            row_count += 1
        
        return rows
    
    def _csv_to_text(self, data: List[Union[Dict[str, str], List[str]]]) -> str:
        """
        Convert CSV data to a text representation for embedding.
        
        Args:
            data: Parsed CSV data
            
        Returns:
            Text representation
        """
        if not data:
            return ""
        
        lines = []
        
        # Handle dictionary rows (with header)
        if isinstance(data[0], dict):
            # Add header
            header = list(data[0].keys())
            lines.append(" | ".join(header))
            lines.append("-" * (sum(len(h) for h in header) + 3 * (len(header) - 1)))
            
            # Add rows
            for row in data:
                lines.append(" | ".join(str(row.get(h, "")) for h in header))
        else:
            # Handle list rows (without header)
            for row in data:
                lines.append(" | ".join(str(val) for val in row))
        
        return "\n".join(lines)
    
    async def _store_csv_structure(self, document_id: str, data: List[Union[Dict[str, str], List[str]]], 
                                  has_header: bool) -> None:
        """
        Store CSV structure in the knowledge graph.
        
        Args:
            document_id: Document ID
            data: Parsed CSV data
            has_header: Whether the CSV has a header row
        """
        if not data:
            return
        
        # Create a document node if it doesn't exist
        doc_query = """
        MERGE (d:Document {id: $id})
        ON CREATE SET d.content_type = 'text/csv'
        RETURN d.id as id
        """
        
        await self.neo4j_client.run_query(doc_query, {"id": document_id})
        
        # Create a CSV node
        csv_query = """
        MATCH (d:Document {id: $document_id})
        MERGE (c:CsvDocument {
            document_id: $document_id,
            row_count: $row_count,
            has_header: $has_header
        })
        MERGE (d)-[:HAS_CSV]->(c)
        RETURN c.document_id as id
        """
        
        await self.neo4j_client.run_query(csv_query, {
            "document_id": document_id,
            "row_count": len(data),
            "has_header": has_header
        })
        
        # Store header if present
        if has_header and isinstance(data[0], dict):
            header = list(data[0].keys())
            
            for i, column in enumerate(header):
                col_query = """
                MATCH (c:CsvDocument {document_id: $document_id})
                MERGE (col:CsvColumn {
                    document_id: $document_id,
                    name: $name,
                    index: $index
                })
                MERGE (c)-[:HAS_COLUMN]->(col)
                """
                
                await self.neo4j_client.run_query(col_query, {
                    "document_id": document_id,
                    "name": column,
                    "index": i
                })
        
        # Store sample rows (up to 5)
        for i, row in enumerate(data[:5]):
            row_query = """
            MATCH (c:CsvDocument {document_id: $document_id})
            CREATE (r:CsvRow {
                document_id: $document_id,
                index: $index,
                content: $content
            })
            CREATE (c)-[:HAS_ROW]->(r)
            """
            
            if isinstance(row, dict):
                content = ", ".join(f"{k}: {v}" for k, v in row.items())
            else:
                content = ", ".join(str(v) for v in row)
                
            await self.neo4j_client.run_query(row_query, {
                "document_id": document_id,
                "index": i,
                "content": content
            }) 

--- app/processors/factory.py.txt ---
"""
Document processor factory.

This module provides a factory for creating document processors
based on content type, file type, and processing requirements.
It integrates standard processors with enhanced AI capabilities.
"""
import logging
import os
from typing import Dict, Any, Optional, Type, Union, List
from pathlib import Path

# Import base processor
from app.processors.base import BaseProcessor

# Import standard processors
from app.processors.text_processor import TextProcessor
from app.processors.markdown_processor import MarkdownProcessor
from app.processors.json_processor import JsonProcessor
from app.processors.csv_processor import CsvProcessor
from app.processors.image_processor import ImageProcessor
from app.processors.pdf_processor import PDFProcessor
from app.processors.html_processor import HTMLProcessor
from app.processors.code_processor import CodeProcessor
from app.processors.privacy_processor import PrivacyCompliantProcessor

# Import multi-provider processor
try:
    from app.processors.processors_updated import MultiProviderProcessor
    MULTI_PROVIDER_AVAILABLE = True
except ImportError:
    MULTI_PROVIDER_AVAILABLE = False
    logging.warning("MultiProviderProcessor not available. Using standard processors.")

logger = logging.getLogger(__name__)

class ProcessorFactory:
    """
    Factory for creating document processors.
    
    This class provides methods for creating the appropriate processor
    based on content type, file extension, or specific processing needs.
    Support standard and AI-enhanced document processing modes.
    """
    
    # Mapping of file extensions to processor classes
    EXTENSION_MAPPING = {
        # Text and documents
        ".txt": TextProcessor,
        ".md": MarkdownProcessor,
        ".json": JsonProcessor,
        ".csv": CsvProcessor,
        ".pdf": PDFProcessor,
        ".html": HTMLProcessor,
        ".htm": HTMLProcessor,
        
        # Images
        ".png": ImageProcessor,
        ".jpg": ImageProcessor,
        ".jpeg": ImageProcessor,
        ".gif": ImageProcessor,
        ".bmp": ImageProcessor,
        ".webp": ImageProcessor,
        
        # Code files
        ".py": CodeProcessor,
        ".js": CodeProcessor,
        ".ts": CodeProcessor,
        ".jsx": CodeProcessor,
        ".tsx": CodeProcessor,
        ".java": CodeProcessor,
        ".c": CodeProcessor,
        ".cpp": CodeProcessor,
        ".h": CodeProcessor,
        ".hpp": CodeProcessor,
        ".go": CodeProcessor,
        ".rs": CodeProcessor,
        ".rb": CodeProcessor,
        ".php": CodeProcessor,
        ".swift": CodeProcessor,
        ".kt": CodeProcessor,
        ".cs": CodeProcessor,
    }
    
    # Mapping of content types to processor classes
    CONTENT_TYPE_MAPPING = {
        # Text and documents
        "text/plain": TextProcessor,
        "text/markdown": MarkdownProcessor,
        "application/json": JsonProcessor,
        "text/csv": CsvProcessor,
        "application/pdf": PDFProcessor,
        "text/html": HTMLProcessor,
        
        # Images
        "image/png": ImageProcessor,
        "image/jpeg": ImageProcessor,
        "image/gif": ImageProcessor,
        "image/bmp": ImageProcessor,
        "image/webp": ImageProcessor,
        
        # Code files
        "text/x-python": CodeProcessor,
        "application/javascript": CodeProcessor,
        "text/javascript": CodeProcessor,
        "application/typescript": CodeProcessor,
        "text/x-java": CodeProcessor,
        "text/x-c": CodeProcessor,
        "text/x-c++": CodeProcessor,
        "text/x-go": CodeProcessor,
        "text/x-rust": CodeProcessor,
        "text/x-ruby": CodeProcessor,
        "application/x-php": CodeProcessor,
        "text/x-swift": CodeProcessor,
        "text/x-kotlin": CodeProcessor,
        "text/x-csharp": CodeProcessor,
    }
    
    # Mapping of special processor types
    SPECIAL_PROCESSORS = {
        "privacy": PrivacyCompliantProcessor,
        "multi_provider": MultiProviderProcessor if MULTI_PROVIDER_AVAILABLE else None,
    }
    
    @classmethod
    def get_processor_for_file(
        cls, 
        file_path: Union[str, Path], 
        use_cognee: bool = False,
        enable_ai: bool = False,
        use_multi_provider: bool = False,
        dataset_name: Optional[str] = None,
        **kwargs
    ) -> BaseProcessor:
        """
        Get processor for a file based on its extension.
        
        Args:
            file_path: Path to the file
            use_cognee: Whether to use Cognee for database operations
            enable_ai: Whether to enable AI enhancements
            use_multi_provider: Whether to use multi-provider AI layer
            dataset_name: Name of the dataset (for Cognee integration)
            **kwargs: Additional options for the processor
            
        Returns:
            Appropriate processor instance
            
        Raises:
            ValueError: If no processor is available for the file type
        """
        file_path = Path(file_path) if isinstance(file_path, str) else file_path
        extension = file_path.suffix.lower()
        
        processor_class = cls.EXTENSION_MAPPING.get(extension)
        if not processor_class:
            raise ValueError(f"No processor available for file type: {extension}")
        
        # Add file path to kwargs for processors that need it
        kwargs['file_path'] = str(file_path)
        
        # Use multi-provider processor if requested and available
        if use_multi_provider and MULTI_PROVIDER_AVAILABLE and enable_ai:
            return MultiProviderProcessor(
                use_cognee=use_cognee,
                enable_ai=enable_ai,
                dataset_name=dataset_name or f"{extension[1:]}_dataset",
                **kwargs
            )
        
        # Create standard processor
        return processor_class(
            use_cognee=use_cognee,
            enable_ai=enable_ai,
            dataset_name=dataset_name or f"{extension[1:]}_dataset",
            **kwargs
        )
    
    @classmethod
    def get_processor_for_content_type(
        cls, 
        content_type: str, 
        use_cognee: bool = False,
        enable_ai: bool = False,
        use_multi_provider: bool = False,
        dataset_name: Optional[str] = None,
        **kwargs
    ) -> BaseProcessor:
        """
        Get processor for a content type.
        
        Args:
            content_type: MIME content type
            use_cognee: Whether to use Cognee for database operations
            enable_ai: Whether to enable AI enhancements
            use_multi_provider: Whether to use multi-provider AI layer
            dataset_name: Name of the dataset (for Cognee integration)
            **kwargs: Additional options for the processor
            
        Returns:
            Appropriate processor instance
            
        Raises:
            ValueError: If no processor is available for the content type
        """
        processor_class = cls.CONTENT_TYPE_MAPPING.get(content_type)
        if not processor_class:
            raise ValueError(f"No processor available for content type: {content_type}")
        
        # Use multi-provider processor if requested and available
        if use_multi_provider and MULTI_PROVIDER_AVAILABLE and enable_ai:
            return MultiProviderProcessor(
                use_cognee=use_cognee,
                enable_ai=enable_ai,
                dataset_name=dataset_name or f"{content_type.split('/')[-1]}_dataset",
                **kwargs
            )
        
        # Create standard processor
        return processor_class(
            use_cognee=use_cognee,
            enable_ai=enable_ai,
            dataset_name=dataset_name or f"{content_type.split('/')[-1]}_dataset",
            **kwargs
        )
    
    @classmethod
    def get_special_processor(
        cls, 
        processor_type: str,
        use_cognee: bool = False,
        enable_ai: bool = False,
        **kwargs
    ) -> BaseProcessor:
        """
        Get a special processor by type.
        
        Args:
            processor_type: Type of special processor
            use_cognee: Whether to use Cognee for database operations
            enable_ai: Whether to enable AI enhancements
            **kwargs: Additional options for the processor
            
        Returns:
            Special processor instance
            
        Raises:
            ValueError: If the requested processor type is not available
        """
        processor_class = cls.SPECIAL_PROCESSORS.get(processor_type)
        if not processor_class:
            raise ValueError(f"Special processor not available: {processor_type}")
        
        # Create processor with enhanced options
        return processor_class(
            use_cognee=use_cognee,
            enable_ai=enable_ai,
            **kwargs
        )
    
    @classmethod
    def get_optimal_processor(
        cls, 
        content: Any, 
        file_size: Optional[int] = None, 
        content_type: Optional[str] = None,
        use_cognee: bool = False,
        enable_ai: bool = False,
        use_multi_provider: bool = False,
        dataset_name: Optional[str] = None,
        **kwargs
    ) -> BaseProcessor:
        """
        Get the optimal processor based on content analysis.
        
        Args:
            content: Content to process
            file_size: Size of the file in bytes
            content_type: MIME content type if known
            use_cognee: Whether to use Cognee for database operations
            enable_ai: Whether to enable AI enhancements
            use_multi_provider: Whether to use multi-provider AI layer
            dataset_name: Name of the dataset (for Cognee integration)
            **kwargs: Additional options for the processor
            
        Returns:
            Optimal processor instance
        """
        # If using multi-provider and AI is enabled, return multi-provider processor
        if use_multi_provider and MULTI_PROVIDER_AVAILABLE and enable_ai:
            inferred_type = "multi_provider"
            return MultiProviderProcessor(
                use_cognee=use_cognee,
                enable_ai=enable_ai,
                dataset_name=dataset_name or f"{inferred_type}_dataset",
                **kwargs
            )
        
        # If content type is provided, use it
        if content_type and content_type in cls.CONTENT_TYPE_MAPPING:
            return cls.get_processor_for_content_type(
                content_type, 
                use_cognee=use_cognee,
                enable_ai=enable_ai,
                use_multi_provider=use_multi_provider,
                dataset_name=dataset_name,
                **kwargs
            )
        
        # Try to infer from content
        if isinstance(content, str):
            # Check if it looks like HTML
            if content.strip().startswith(('<html', '<!DOCTYPE html')):
                processor_class = HTMLProcessor
            # Check if it looks like JSON
            elif content.strip().startswith('{') and content.strip().endswith('}'):
                processor_class = JsonProcessor
            # Check if it looks like CSV
            elif ',' in content and '\n' in content:
                processor_class = CsvProcessor
            # Check if it looks like Markdown
            elif '# ' in content or '## ' in content:
                processor_class = MarkdownProcessor
            # Check if it looks like code
            elif any(keyword in content for keyword in ['def ', 'class ', 'function ', 'import ', '#include']):
                processor_class = CodeProcessor
            # Default to text
            else:
                processor_class = TextProcessor
        elif isinstance(content, bytes):
            # Check for PDF signature
            if content.startswith(b'%PDF'):
                processor_class = PDFProcessor
            # Check for image signatures
            elif content.startswith((b'\x89PNG', b'\xFF\xD8\xFF', b'GIF', b'BM', b'RIFF')):
                processor_class = ImageProcessor
            # Try to decode as text
            else:
                try:
                    text_content = content.decode('utf-8')
                    return cls.get_optimal_processor(
                        text_content, 
                        file_size, 
                        content_type, 
                        use_cognee=use_cognee,
                        enable_ai=enable_ai,
                        use_multi_provider=use_multi_provider,
                        dataset_name=dataset_name,
                        **kwargs
                    )
                except UnicodeDecodeError:
                    # Binary content we don't recognize, default to text
                    processor_class = TextProcessor
        else:
            # Default to text processor
            processor_class = TextProcessor
        
        # Create processor with enhanced options
        inferred_type = processor_class.__name__.replace("Processor", "").lower()
        
        # Use multi-provider processor if requested and available
        if use_multi_provider and MULTI_PROVIDER_AVAILABLE and enable_ai:
            return MultiProviderProcessor(
                use_cognee=use_cognee,
                enable_ai=enable_ai,
                dataset_name=dataset_name or f"{inferred_type}_dataset",
                **kwargs
            )
        
        # Create standard processor
        return processor_class(
            use_cognee=use_cognee,
            enable_ai=enable_ai,
            dataset_name=dataset_name or f"{inferred_type}_dataset",
            **kwargs
        )
    
    @classmethod
    def get_enhanced_processor(
        cls, 
        file_path: Optional[Union[str, Path]] = None, 
        content_type: Optional[str] = None, 
        content: Optional[Any] = None,
        dataset_name: Optional[str] = None,
        **kwargs
    ) -> BaseProcessor:
        """
        Get a processor with enhanced AI capabilities.
        
        This is a convenience method that enables AI enhancements
        and Cognee integration by default.
        
        Args:
            file_path: Path to the file (if processing a file)
            content_type: MIME content type (if known)
            content: Content to process (if not processing a file)
            dataset_name: Name of the dataset (for Cognee integration)
            **kwargs: Additional options for the processor
            
        Returns:
            Enhanced processor instance
        """
        # Set enhanced options
        kwargs.update({
            "use_cognee": kwargs.get("use_cognee", True),
            "enable_ai": kwargs.get("enable_ai", True),
            "use_multi_provider": kwargs.get("use_multi_provider", True),
            "dataset_name": dataset_name
        })
        
        # Determine the appropriate method to call
        if file_path:
            return cls.get_processor_for_file(file_path, **kwargs)
        elif content_type:
            return cls.get_processor_for_content_type(content_type, **kwargs)
        elif content:
            return cls.get_optimal_processor(content, **kwargs)
        else:
            raise ValueError("At least one of file_path, content_type, or content must be provided")
    
    @classmethod
    def available_processor_types(cls) -> Dict[str, List[str]]:
        """
        Get a list of available processor types.
        
        Returns:
            Dictionary of available processor types
        """
        # Collect extensions and content types
        extensions = sorted(cls.EXTENSION_MAPPING.keys())
        content_types = sorted(cls.CONTENT_TYPE_MAPPING.keys())
        special = sorted([k for k, v in cls.SPECIAL_PROCESSORS.items() if v is not None])
        
        # Determine availability of multi-provider processor
        has_multi_provider = MULTI_PROVIDER_AVAILABLE
        
        return {
            "extensions": extensions,
            "content_types": content_types,
            "special": special,
            "has_multi_provider": has_multi_provider
        } 

--- app/processors/html_processor.py.txt ---
"""
HTML document processor.

This module provides a processor for HTML documents that extracts text,
structure, links, and other elements from HTML content.
"""
import logging
import re
from typing import Dict, Any, List, Optional, Tuple, Set
from urllib.parse import urljoin, urlparse

# HTML processing libraries
try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

try:
    import html2text
    HTML2TEXT_AVAILABLE = True
except ImportError:
    HTML2TEXT_AVAILABLE = False

from .base import BaseProcessor

logger = logging.getLogger(__name__)

class HTMLProcessor(BaseProcessor):
    """
    Processor for HTML documents.
    
    This processor extracts text, structure, links, and other elements from HTML content.
    It uses BeautifulSoup for parsing and html2text for conversion to plain text.
    """
    
    # HTML elements that typically represent standalone sections
    SECTION_ELEMENTS = {
        'article', 'section', 'div', 'main', 'aside', 'header', 'footer',
        'nav', 'form'
    }
    
    # HTML elements that typically represent headings
    HEADING_ELEMENTS = {'h1', 'h2', 'h3', 'h4', 'h5', 'h6'}
    
    # HTML elements to extract as metadata
    METADATA_ELEMENTS = {
        'title', 'meta', 'link[rel="canonical"]', 'link[rel="alternate"]',
        'meta[property^="og:"]', 'meta[name^="twitter:"]', 'meta[name="description"]'
    }
    
    def __init__(self, 
                 extract_links: bool = True,
                 extract_images: bool = True,
                 extract_tables: bool = True,
                 extract_metadata: bool = True,
                 clean_html: bool = True,
                 base_url: Optional[str] = None,
                 **kwargs):
        """
        Initialize the HTML processor.
        
        Args:
            extract_links: Whether to extract links from the HTML
            extract_images: Whether to extract image information
            extract_tables: Whether to extract tables
            extract_metadata: Whether to extract metadata (title, description, etc.)
            clean_html: Whether to clean the HTML (remove scripts, styles, etc.)
            base_url: Base URL for resolving relative URLs
            **kwargs: Additional options for the base processor
        """
        super().__init__(**kwargs)
        
        if not BS4_AVAILABLE:
            raise ImportError(
                "BeautifulSoup is required for HTML processing. "
                "Please install it with: pip install beautifulsoup4"
            )
        
        self.extract_links = extract_links
        self.extract_images = extract_images
        self.extract_tables = extract_tables
        self.extract_metadata = extract_metadata
        self.clean_html = clean_html
        self.base_url = base_url
    
    def process(self, content: Any, metadata: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """
        Process an HTML document.
        
        Args:
            content: HTML content (string or bytes)
            metadata: Document metadata
            **kwargs: Additional processing options
            
        Returns:
            Processing results including extracted text, structure, and links
        """
        if metadata is None:
            metadata = {}
        
        # Prepare HTML content for processing
        html_content = self._prepare_content(content)
        
        # Parse HTML with BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Clean HTML if requested
        if self.clean_html:
            self._clean_html(soup)
        
        # Extract metadata if requested
        if self.extract_metadata:
            page_metadata = self._extract_metadata(soup)
            metadata.update(page_metadata)
        
        # Extract links if requested
        links = []
        if self.extract_links:
            links = self._extract_links(soup)
            metadata['links_count'] = len(links)
        
        # Extract images if requested
        images = []
        if self.extract_images:
            images = self._extract_images(soup)
            metadata['images_count'] = len(images)
        
        # Extract tables if requested
        tables = []
        if self.extract_tables:
            tables = self._extract_tables(soup)
            metadata['tables_count'] = len(tables)
        
        # Extract text and structure
        extracted_text, structure = self._extract_text_and_structure(soup)
        
        # Create chunks based on the document structure
        chunks = self.create_chunks(extracted_text, structure=structure, **kwargs)
        
        # Prepare result
        result = {
            'chunks': chunks,
            'extracted_text': extracted_text,
            'metadata': metadata
        }
        
        # Add document structure if available
        if structure:
            result['structure'] = structure
            
        # Add links if extracted
        if links:
            result['links'] = links
            
        # Add images if extracted
        if images:
            result['images'] = images
            
        # Add tables if extracted
        if tables:
            result['tables'] = tables
        
        return result
    
    def _prepare_content(self, content: Any) -> str:
        """
        Prepare HTML content for processing.
        
        Args:
            content: HTML content (string or bytes)
            
        Returns:
            HTML content as string
        """
        if isinstance(content, bytes):
            return content.decode('utf-8', errors='replace')
        elif isinstance(content, str):
            return content
        elif hasattr(content, 'read'):
            # File-like object
            return content.read().decode('utf-8', errors='replace') if isinstance(content.read(), bytes) else content.read()
        else:
            raise ValueError(f"Unsupported content type: {type(content)}")
    
    def _clean_html(self, soup: BeautifulSoup) -> None:
        """
        Clean HTML by removing scripts, styles, and comments.
        
        Args:
            soup: BeautifulSoup object
        """
        # Remove script tags
        for script in soup.find_all('script'):
            script.decompose()
        
        # Remove style tags
        for style in soup.find_all('style'):
            style.decompose()
        
        # Remove comments
        for comment in soup.find_all(string=lambda text: isinstance(text, str) and text.strip().startswith('<!--')):
            comment.extract()
    
    def _extract_metadata(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """
        Extract metadata from HTML.
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            Dictionary of metadata
        """
        metadata = {}
        
        # Extract title
        title_tag = soup.find('title')
        if title_tag and title_tag.string:
            metadata['title'] = title_tag.string.strip()
        
        # Extract meta description
        description_tag = soup.find('meta', attrs={'name': 'description'})
        if description_tag and description_tag.get('content'):
            metadata['description'] = description_tag['content'].strip()
        
        # Extract Open Graph metadata
        og_tags = soup.find_all('meta', attrs={'property': re.compile(r'^og:')})
        if og_tags:
            og_metadata = {}
            for tag in og_tags:
                if tag.get('content'):
                    property_name = tag['property'][3:]  # Remove 'og:' prefix
                    og_metadata[property_name] = tag['content'].strip()
            
            if og_metadata:
                metadata['open_graph'] = og_metadata
        
        # Extract Twitter card metadata
        twitter_tags = soup.find_all('meta', attrs={'name': re.compile(r'^twitter:')})
        if twitter_tags:
            twitter_metadata = {}
            for tag in twitter_tags:
                if tag.get('content'):
                    property_name = tag['name'][8:]  # Remove 'twitter:' prefix
                    twitter_metadata[property_name] = tag['content'].strip()
            
            if twitter_metadata:
                metadata['twitter_card'] = twitter_metadata
        
        # Extract canonical URL
        canonical_tag = soup.find('link', attrs={'rel': 'canonical'})
        if canonical_tag and canonical_tag.get('href'):
            metadata['canonical_url'] = canonical_tag['href'].strip()
        
        return metadata
    
    def _extract_links(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        Extract links from HTML.
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            List of extracted links with metadata
        """
        links = []
        seen_urls = set()
        
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href'].strip()
            
            # Skip empty, javascript, and anchor links
            if not href or href.startswith('javascript:') or href == '#':
                continue
            
            # Resolve relative URLs if base_url is provided
            if self.base_url and not urlparse(href).netloc:
                href = urljoin(self.base_url, href)
            
            # Skip duplicates
            if href in seen_urls:
                continue
            
            seen_urls.add(href)
            
            # Extract link text and title
            text = a_tag.get_text().strip()
            title = a_tag.get('title', '').strip()
            
            links.append({
                'url': href,
                'text': text,
                'title': title if title else None,
                'is_external': bool(urlparse(href).netloc) if self.base_url else None
            })
        
        return links
    
    def _extract_images(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        Extract images from HTML.
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            List of extracted images with metadata
        """
        images = []
        seen_urls = set()
        
        for img_tag in soup.find_all('img'):
            src = img_tag.get('src', '').strip()
            
            # Skip empty sources
            if not src:
                continue
            
            # Resolve relative URLs if base_url is provided
            if self.base_url and not urlparse(src).netloc:
                src = urljoin(self.base_url, src)
            
            # Skip duplicates
            if src in seen_urls:
                continue
            
            seen_urls.add(src)
            
            # Extract image metadata
            alt = img_tag.get('alt', '').strip()
            title = img_tag.get('title', '').strip()
            width = img_tag.get('width')
            height = img_tag.get('height')
            
            images.append({
                'url': src,
                'alt': alt if alt else None,
                'title': title if title else None,
                'width': int(width) if width and width.isdigit() else None,
                'height': int(height) if height and height.isdigit() else None
            })
        
        return images
    
    def _extract_tables(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        Extract tables from HTML.
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            List of extracted tables with metadata
        """
        tables = []
        
        for i, table_tag in enumerate(soup.find_all('table')):
            # Extract table caption
            caption = table_tag.find('caption')
            caption_text = caption.get_text().strip() if caption else None
            
            # Extract headers
            headers = []
            header_row = table_tag.find('thead')
            if header_row:
                for th in header_row.find_all('th'):
                    headers.append(th.get_text().strip())
            
            # Extract rows
            rows = []
            for tr in table_tag.find_all('tr'):
                row = [td.get_text().strip() for td in tr.find_all(['td', 'th'])]
                if row:  # Skip empty rows
                    rows.append(row)
            
            # Convert table to text
            table_text = ""
            if caption_text:
                table_text += f"{caption_text}\n\n"
            
            for row in rows:
                table_text += " | ".join(row) + "\n"
            
            tables.append({
                'index': i,
                'caption': caption_text,
                'headers': headers if headers else None,
                'rows': len(rows),
                'columns': len(headers) if headers else (len(rows[0]) if rows else 0),
                'text': table_text.strip()
            })
        
        return tables
    
    def _extract_text_and_structure(self, soup: BeautifulSoup) -> Tuple[str, Dict[str, Any]]:
        """
        Extract text and structure from HTML.
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            Tuple of (extracted text, structure dict)
        """
        # Extract structure
        structure = {
            'headings': [],
            'sections': []
        }
        
        # Extract headings
        for i, heading in enumerate(soup.find_all(self.HEADING_ELEMENTS)):
            heading_text = heading.get_text().strip()
            if heading_text:
                heading_level = int(heading.name[1])  # Extract number from h1, h2, etc.
                structure['headings'].append({
                    'text': heading_text,
                    'level': heading_level,
                    'index': i
                })
        
        # Extract sections
        for i, section in enumerate(soup.find_all(self.SECTION_ELEMENTS)):
            # Skip empty sections
            if not section.get_text().strip():
                continue
                
            # Get section heading if available
            section_heading = None
            for heading in section.find_all(self.HEADING_ELEMENTS, recursive=False):
                section_heading = heading.get_text().strip()
                break
            
            # Get section ID or class for identification
            section_id = section.get('id', '')
            section_class = ' '.join(section.get('class', []))
            
            structure['sections'].append({
                'heading': section_heading,
                'id': section_id if section_id else None,
                'class': section_class if section_class else None,
                'index': i
            })
        
        # Convert HTML to plain text
        if HTML2TEXT_AVAILABLE:
            # Use html2text for better formatting
            h = html2text.HTML2Text()
            h.ignore_links = False
            h.ignore_images = False
            h.ignore_tables = False
            h.body_width = 0  # No wrapping
            text = h.handle(str(soup))
        else:
            # Fallback to BeautifulSoup's get_text
            text = soup.get_text(separator='\n\n')
        
        return text, structure
    
    def create_chunks(self, text: str, structure: Dict[str, Any] = None, **kwargs) -> List[Dict[str, Any]]:
        """
        Create chunks from HTML text, using structure information if available.
        
        Args:
            text: Extracted text
            structure: Document structure information
            **kwargs: Additional chunking options
            
        Returns:
            List of text chunks
        """
        chunks = []
        
        # If we have headings, use them for structure-aware chunking
        if structure and structure.get('headings'):
            headings = structure['headings']
            
            # Use heading text as markers to split the text
            markers = []
            for heading in headings:
                heading_text = heading['text']
                # Look for the heading text or a markdown-style heading
                patterns = [
                    re.escape(heading_text),
                    re.escape(f"# {heading_text}"),
                    re.escape(f"## {heading_text}"),
                    re.escape(f"### {heading_text}"),
                    re.escape(f"#### {heading_text}")
                ]
                for pattern in patterns:
                    matches = list(re.finditer(pattern, text))
                    if matches:
                        for match in matches:
                            markers.append({
                                'text': heading_text,
                                'position': match.start(),
                                'level': heading['level']
                            })
                        break
            
            # Sort markers by position
            markers.sort(key=lambda x: x['position'])
            
            # Split text by markers
            if markers:
                current_pos = 0
                
                for i, marker in enumerate(markers):
                    marker_pos = marker['position']
                    
                    # Add the text before this marker if it's not the first marker
                    if i > 0 and marker_pos > current_pos:
                        prev_text = text[current_pos:marker_pos].strip()
                        if prev_text:
                            chunks.append({
                                'text': prev_text,
                                'type': 'section',
                                'metadata': {
                                    'heading': markers[i-1]['text'],
                                    'level': markers[i-1]['level']
                                }
                            })
                    
                    current_pos = marker_pos + len(marker['text'])
                
                # Add the final section
                if current_pos < len(text):
                    final_text = text[current_pos:].strip()
                    if final_text:
                        chunks.append({
                            'text': final_text,
                            'type': 'section',
                            'metadata': {
                                'heading': markers[-1]['text'],
                                'level': markers[-1]['level']
                            }
                        })
                
                return chunks
        
        # Fall back to standard chunking if we couldn't use structure
        return super().create_chunks(text, **kwargs)
    
    def store_in_graph(self, document_id: str, metadata: Dict[str, Any], 
                      chunks: List[Dict[str, Any]], **kwargs) -> None:
        """
        Store HTML document in graph database.
        
        Args:
            document_id: Document ID
            metadata: Document metadata
            chunks: Document chunks
            **kwargs: Additional storage options
        """
        # Add HTML-specific metadata
        metadata['content_type'] = 'text/html'
        
        # Store document node
        doc_properties = {
            'id': document_id,
            'content_type': 'text/html',
            **metadata
        }
        
        # Create document node
        self.graph_db.create_node('Document', doc_properties)
        
        # Store each chunk
        for i, chunk in enumerate(chunks):
            chunk_id = f"{document_id}_chunk_{i}"
            chunk_type = chunk.get('type', 'text')
            
            # Create chunk node
            chunk_properties = {
                'id': chunk_id,
                'text': chunk['text'],
                'type': chunk_type,
                'index': i,
                **chunk.get('metadata', {})
            }
            self.graph_db.create_node('Chunk', chunk_properties)
            
            # Connect chunk to document
            self.graph_db.create_relationship(
                'Document', {'id': document_id},
                'HAS_CHUNK', {},
                'Chunk', {'id': chunk_id}
            )
            
            # Create special relationships based on chunk type
            if chunk_type == 'section':
                heading = chunk.get('metadata', {}).get('heading')
                if heading:
                    # Create heading node if it doesn't exist
                    heading_id = f"{document_id}_heading_{heading}"
                    self.graph_db.create_node('Heading', {
                        'id': heading_id,
                        'text': heading,
                        'level': chunk.get('metadata', {}).get('level', 1)
                    })
                    
                    # Connect chunk to heading
                    self.graph_db.create_relationship(
                        'Heading', {'id': heading_id},
                        'CONTAINS', {},
                        'Chunk', {'id': chunk_id}
                    )
                    
                    # Connect document to heading
                    self.graph_db.create_relationship(
                        'Document', {'id': document_id},
                        'HAS_HEADING', {},
                        'Heading', {'id': heading_id}
                    )
        
        # Store links if available
        if 'links' in kwargs:
            for i, link in enumerate(kwargs['links']):
                link_id = f"{document_id}_link_{i}"
                
                # Create link node
                link_properties = {
                    'id': link_id,
                    'url': link['url'],
                    'text': link.get('text', ''),
                    'title': link.get('title', ''),
                    'is_external': link.get('is_external', False)
                }
                self.graph_db.create_node('Link', link_properties)
                
                # Connect document to link
                self.graph_db.create_relationship(
                    'Document', {'id': document_id},
                    'HAS_LINK', {},
                    'Link', {'id': link_id}
                )
    
    def store_in_vector_db(self, document_id: str, metadata: Dict[str, Any],
                          chunks: List[Dict[str, Any]], **kwargs) -> None:
        """
        Store HTML document in vector database.
        
        Args:
            document_id: Document ID
            metadata: Document metadata
            chunks: Document chunks
            **kwargs: Additional storage options
        """
        # Add HTML-specific metadata
        metadata['content_type'] = 'text/html'
        
        # Store each chunk with its embeddings
        for i, chunk in enumerate(chunks):
            chunk_id = f"{document_id}_chunk_{i}"
            chunk_text = chunk['text']
            
            # Get embeddings for the chunk
            embeddings = self.get_embeddings(chunk_text)
            
            # Prepare chunk metadata
            chunk_metadata = {
                'document_id': document_id,
                'chunk_id': chunk_id,
                'chunk_index': i,
                'chunk_type': chunk.get('type', 'text'),
                **metadata,
                **chunk.get('metadata', {})
            }
            
            # Store in vector database
            self.vector_db.add_vectors(
                collection_name=kwargs.get('collection_name', 'documents'),
                vectors=[(chunk_id, embeddings, chunk_metadata)],
                batch_size=kwargs.get('batch_size', 100)
            ) 

--- app/processors/image_processor.py.txt ---
"""
Image processor.

This module provides a processor for image files.
"""
import base64
import io
import logging
import os
from typing import Dict, List, Any, Optional, Union
from pathlib import Path

from app.processors.base import BaseProcessor

logger = logging.getLogger(__name__)

class ImageProcessor(BaseProcessor):
    """
    Processor for image files.
    
    This class provides methods for processing image files,
    extracting content, and storing it in the knowledge graph and vector database.
    """
    
    async def process(self, content: Union[str, bytes, Path], **kwargs) -> Dict[str, Any]:
        """
        Process an image file.
        
        Args:
            content: Image content as file path, bytes, or base64 string
            **kwargs: Additional processing options
                - extract_text: Whether to extract text from the image (default: True)
                - extract_objects: Whether to extract objects from the image (default: True)
                - embedding_model: Model to use for image embeddings (default: clip-vit-large-patch14)
                - document_id: Optional document ID
                - metadata: Optional metadata dictionary
        
        Returns:
            Processing result with document ID and metadata
        """
        # Extract options
        extract_text = kwargs.get("extract_text", True)
        extract_objects = kwargs.get("extract_objects", True)
        embedding_model = kwargs.get("embedding_model", "clip-vit-large-patch14")
        document_id = kwargs.get("document_id")
        metadata = kwargs.get("metadata", {})
        
        # Update metadata
        metadata["content_type"] = "image"
        
        # Load image content
        image_bytes = await self._load_image_content(content)
        
        # Extract text if requested
        if extract_text:
            text = await self._extract_text_from_image(image_bytes)
            metadata["extracted_text"] = text
        else:
            text = ""
        
        # Extract objects if requested
        if extract_objects:
            objects = await self._extract_objects_from_image(image_bytes)
            metadata["detected_objects"] = objects
        
        # Generate image embedding
        image_embedding = await self._generate_image_embedding(image_bytes, model=embedding_model)
        
        # Store in vector database
        vector_id = await self.store_in_vector_db(image_embedding, metadata)
        
        # Store in knowledge graph
        kg_id = await self._store_image_in_knowledge_graph(document_id, metadata)
        
        # If text was extracted, process it as text
        text_result = None
        if extract_text and text:
            from app.processors.text_processor import TextProcessor
            text_processor = TextProcessor()
            
            # Process the extracted text
            text_result = await text_processor.process(
                text,
                document_id=f"{document_id}_text" if document_id else None,
                metadata={
                    "source_type": "image_text",
                    "source_image_id": document_id,
                    "content_type": "text/plain"
                }
            )
            
            # Create relationship between image and extracted text
            if document_id and text_result.get("document_id"):
                await self._create_image_text_relationship(document_id, text_result["document_id"])
        
        return {
            "document_id": document_id,
            "knowledge_graph_id": kg_id,
            "vector_id": vector_id,
            "metadata": metadata,
            "extracted_text": text if extract_text else None,
            "detected_objects": objects if extract_objects else None,
            "text_processing_result": text_result
        }
    
    async def _load_image_content(self, content: Union[str, bytes, Path]) -> bytes:
        """
        Load image content from various sources.
        
        Args:
            content: Image content as file path, bytes, or base64 string
            
        Returns:
            Image bytes
        """
        if isinstance(content, bytes):
            return content
        elif isinstance(content, (str, Path)):
            path = Path(content)
            if path.exists():
                # Load from file
                with open(path, "rb") as f:
                    return f.read()
            elif isinstance(content, str) and content.startswith(("data:image", "base64:")):
                # Base64 encoded image
                if content.startswith("data:image"):
                    # Extract the base64 part from data URL
                    _, base64_data = content.split(",", 1)
                else:
                    # Remove the "base64:" prefix
                    base64_data = content[7:]
                
                return base64.b64decode(base64_data)
        
        raise ValueError("Invalid image content format")
    
    async def _extract_text_from_image(self, image_bytes: bytes) -> str:
        """
        Extract text from an image using OCR.
        
        Args:
            image_bytes: Image content as bytes
            
        Returns:
            Extracted text
        """
        # Placeholder for actual OCR implementation
        # In a real implementation, this would use an OCR library or API
        logger.info("Extracting text from image (placeholder)")
        
        # Return a placeholder result
        return "Sample extracted text from image"
    
    async def _extract_objects_from_image(self, image_bytes: bytes) -> List[Dict[str, Any]]:
        """
        Extract objects from an image using object detection.
        
        Args:
            image_bytes: Image content as bytes
            
        Returns:
            List of detected objects with bounding boxes
        """
        # Placeholder for actual object detection implementation
        # In a real implementation, this would use a computer vision API
        logger.info("Extracting objects from image (placeholder)")
        
        # Return placeholder results
        return [
            {"label": "person", "confidence": 0.95, "box": [10, 10, 100, 200]},
            {"label": "car", "confidence": 0.85, "box": [150, 50, 300, 150]}
        ]
    
    async def _generate_image_embedding(self, image_bytes: bytes, model: str) -> List[float]:
        """
        Generate embedding for an image.
        
        Args:
            image_bytes: Image content as bytes
            model: Embedding model to use
            
        Returns:
            Vector embedding
        """
        # Placeholder for actual image embedding generation
        # In a real implementation, this would call an embedding API
        logger.info(f"Generating image embedding with model: {model}")
        
        # Return a mock embedding (would be replaced with actual API call)
        return [0.0] * 512  # CLIP embeddings are typically 512 dimensions
    
    async def _store_image_in_knowledge_graph(self, document_id: str, metadata: Dict[str, Any]) -> str:
        """
        Store image metadata in the knowledge graph.
        
        Args:
            document_id: Document ID
            metadata: Image metadata
            
        Returns:
            ID of the stored node
        """
        # Create an image node
        query = """
        CREATE (i:Image {
            id: $id,
            content_type: $content_type,
            width: $width,
            height: $height,
            format: $format,
            created_at: $created_at
        })
        RETURN i.id as id
        """
        
        params = {
            "id": document_id,
            "content_type": metadata.get("content_type", "image"),
            "width": metadata.get("width", 0),
            "height": metadata.get("height", 0),
            "format": metadata.get("format", "unknown"),
            "created_at": metadata.get("processed_at")
        }
        
        result = await self.neo4j_client.run_query(query, params)
        
        # Store detected objects if available
        if "detected_objects" in metadata:
            await self._store_image_objects(document_id, metadata["detected_objects"])
        
        return result[0]["id"] if result else None
    
    async def _store_image_objects(self, image_id: str, objects: List[Dict[str, Any]]) -> None:
        """
        Store detected objects in the knowledge graph.
        
        Args:
            image_id: Image ID
            objects: List of detected objects
        """
        for i, obj in enumerate(objects):
            # Create an object node
            query = """
            MATCH (i:Image {id: $image_id})
            CREATE (o:ImageObject {
                id: $id,
                image_id: $image_id,
                label: $label,
                confidence: $confidence,
                box_x: $box_x,
                box_y: $box_y,
                box_width: $box_width,
                box_height: $box_height
            })
            CREATE (i)-[:CONTAINS_OBJECT]->(o)
            """
            
            box = obj.get("box", [0, 0, 0, 0])
            
            params = {
                "id": f"{image_id}_object_{i}",
                "image_id": image_id,
                "label": obj.get("label", "unknown"),
                "confidence": obj.get("confidence", 0.0),
                "box_x": box[0] if len(box) > 0 else 0,
                "box_y": box[1] if len(box) > 1 else 0,
                "box_width": box[2] if len(box) > 2 else 0,
                "box_height": box[3] if len(box) > 3 else 0
            }
            
            await self.neo4j_client.run_query(query, params)
    
    async def _create_image_text_relationship(self, image_id: str, text_id: str) -> None:
        """
        Create a relationship between an image and extracted text.
        
        Args:
            image_id: Image ID
            text_id: Text document ID
        """
        query = """
        MATCH (i:Image {id: $image_id})
        MATCH (t:Document {id: $text_id})
        CREATE (i)-[:HAS_TEXT]->(t)
        """
        
        params = {
            "image_id": image_id,
            "text_id": text_id
        }
        
        await self.neo4j_client.run_query(query, params) 

--- app/processors/__init__.py.txt ---
"""
Document processors for various content types.

This module provides processors for different content types,
including text, markdown, JSON, CSV, PDF, HTML, images, and code.
"""
import os

# Set environment variables for Cognee before importing
# Ensure LOG_LEVEL is uppercase for Cognee
if 'LOG_LEVEL' in os.environ:
    log_level = os.environ['LOG_LEVEL']
    if log_level.lower() in ['info', 'debug', 'warning', 'error', 'critical']:
        os.environ['LOG_LEVEL'] = log_level.upper()

# Now import the processor components
# Define this at the module level before imports
MULTI_PROVIDER_AVAILABLE = False

# Import all processor classes
from .base import BaseProcessor, DatabaseAdapter, AIEnhancementLayer
from .factory import ProcessorFactory
from .text_processor import TextProcessor
from .pdf_processor import PDFProcessor
from .image_processor import ImageProcessor
from .html_processor import HTMLProcessor
from .markdown_processor import MarkdownProcessor
from .code_processor import CodeProcessor
from .csv_processor import CsvProcessor as CSVProcessor
from .json_processor import JsonProcessor as JSONProcessor
from .privacy_processor import PrivacyCompliantProcessor as PrivacyProcessor

try:
    from .processors_updated import MultiProviderProcessor
    MULTI_PROVIDER_AVAILABLE = True
except ImportError:
    # MultiProviderProcessor is optional
    pass

__all__ = [
    'BaseProcessor',
    'DatabaseAdapter',
    'AIEnhancementLayer',
    'ProcessorFactory',
    'TextProcessor',
    'PDFProcessor',
    'ImageProcessor',
    'HTMLProcessor',
    'MarkdownProcessor',
    'CodeProcessor',
    'CSVProcessor',
    'JSONProcessor',
    'PrivacyProcessor',
]

# Add MultiProviderProcessor to exports if available
if MULTI_PROVIDER_AVAILABLE:
    __all__.append("MultiProviderProcessor")

# Create convenience functions for processor factory
def get_processor(file_path=None, content_type=None, content=None, **kwargs):
    """
    Get the appropriate processor for the given file path, content type, or content.
    
    Args:
        file_path: Path to the file (if processing a file)
        content_type: MIME content type (if known)
        content: Content to process (if not processing a file)
        **kwargs: Additional options for the processor
        
    Returns:
        Appropriate processor instance
        
    Raises:
        ValueError: If no processor is available for the file type or content type
    """
    if file_path:
        return ProcessorFactory.get_processor_for_file(file_path, **kwargs)
    elif content_type:
        return ProcessorFactory.get_processor_for_content_type(content_type, **kwargs)
    elif content:
        return ProcessorFactory.get_optimal_processor(content, **kwargs)
    else:
        raise ValueError("At least one of file_path, content_type, or content must be provided")

def get_enhanced_processor(file_path=None, content_type=None, content=None, **kwargs):
    """
    Get an enhanced processor with AI capabilities.
    
    This is a convenience method that enables AI enhancements
    and Cognee integration by default.
    
    Args:
        file_path: Path to the file (if processing a file)
        content_type: MIME content type (if known)
        content: Content to process (if not processing a file)
        **kwargs: Additional options for the processor
        
    Returns:
        Enhanced processor instance
    """
    return ProcessorFactory.get_enhanced_processor(
        file_path=file_path,
        content_type=content_type,
        content=content,
        **kwargs
    ) 

--- app/processors/json_processor.py.txt ---
"""
JSON document processor.

This module provides a processor for JSON documents.
"""
import json
import logging
from typing import Dict, List, Any, Optional, Union

from app.processors.base import BaseProcessor

logger = logging.getLogger(__name__)

class JsonProcessor(BaseProcessor):
    """
    Processor for JSON documents.
    
    This class provides methods for processing JSON documents,
    extracting structured data, and storing it in the knowledge graph
    and vector database.
    """
    
    async def process(self, content: Union[str, Dict, List], **kwargs) -> Dict[str, Any]:
        """
        Process a JSON document.
        
        Args:
            content: JSON content as string or parsed object
            **kwargs: Additional processing options
                - flatten: Whether to flatten nested JSON (default: True)
                - max_depth: Maximum depth for flattening (default: 5)
                - store_schema: Whether to store JSON schema (default: True)
                - other options from BaseProcessor
        
        Returns:
            Processing result with document ID and metadata
        """
        # Extract options
        flatten = kwargs.get("flatten", True)
        max_depth = kwargs.get("max_depth", 5)
        store_schema = kwargs.get("store_schema", True)
        document_id = kwargs.get("document_id")
        metadata = kwargs.get("metadata", {})
        
        # Update metadata
        metadata["content_type"] = "application/json"
        
        # Parse JSON if needed
        if isinstance(content, str):
            try:
                parsed_json = json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON: {e}")
                raise ValueError(f"Invalid JSON: {e}")
        else:
            parsed_json = content
        
        # Extract schema if requested
        if store_schema:
            schema = self._extract_schema(parsed_json)
            metadata["schema"] = schema
        
        # Flatten JSON if requested
        if flatten:
            flattened = self._flatten_json(parsed_json, max_depth=max_depth)
            
            # Store flattened version in metadata
            metadata["flattened"] = flattened
            
            # Create text representation for embedding
            text_representation = self._json_to_text(flattened)
        else:
            # Create text representation directly
            text_representation = self._json_to_text(parsed_json)
        
        # Use the text processor to handle the text representation
        from app.processors.text_processor import TextProcessor
        text_processor = TextProcessor()
        text_processor.metadata = self.metadata
        
        # Process the text representation
        result = await text_processor.process(
            text_representation,
            document_id=document_id,
            metadata=metadata,
            **kwargs
        )
        
        # Add JSON-specific data to the result
        result["json_keys"] = self._extract_keys(parsed_json)
        
        # Store additional relationships in Neo4j
        if result.get("document_id"):
            await self._store_json_structure(result["document_id"], parsed_json)
        
        return result
    
    def _extract_schema(self, data: Union[Dict, List]) -> Dict[str, Any]:
        """
        Extract a simple schema from JSON data.
        
        Args:
            data: JSON data
            
        Returns:
            Schema description
        """
        if isinstance(data, dict):
            schema = {}
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    schema[key] = self._extract_schema(value)
                else:
                    schema[key] = type(value).__name__
            return schema
        elif isinstance(data, list):
            if not data:
                return {"type": "array", "items": {}}
            
            # Check if all items have the same type
            item_types = set(type(item).__name__ for item in data)
            
            if len(item_types) == 1:
                item_type = next(iter(item_types))
                if item_type in ("dict", "list"):
                    # Sample the first item for nested structures
                    return {"type": "array", "items": self._extract_schema(data[0])}
                else:
                    return {"type": "array", "items": {"type": item_type}}
            else:
                return {"type": "array", "items": {"type": list(item_types)}}
        else:
            return {"type": type(data).__name__}
    
    def _flatten_json(self, data: Union[Dict, List], prefix: str = "", max_depth: int = 5, 
                     current_depth: int = 0) -> Dict[str, Any]:
        """
        Flatten nested JSON into a flat dictionary.
        
        Args:
            data: JSON data
            prefix: Key prefix for nested items
            max_depth: Maximum depth to flatten
            current_depth: Current depth in recursion
            
        Returns:
            Flattened dictionary
        """
        result = {}
        
        # Stop recursion if max depth reached
        if current_depth >= max_depth:
            return {prefix: str(data)}
        
        if isinstance(data, dict):
            for key, value in data.items():
                new_key = f"{prefix}.{key}" if prefix else key
                
                if isinstance(value, (dict, list)):
                    result.update(self._flatten_json(value, new_key, max_depth, current_depth + 1))
                else:
                    result[new_key] = value
        elif isinstance(data, list):
            for i, item in enumerate(data):
                new_key = f"{prefix}[{i}]"
                
                if isinstance(item, (dict, list)):
                    result.update(self._flatten_json(item, new_key, max_depth, current_depth + 1))
                else:
                    result[new_key] = item
        else:
            result[prefix] = data
        
        return result
    
    def _json_to_text(self, data: Union[Dict, List]) -> str:
        """
        Convert JSON to a text representation for embedding.
        
        Args:
            data: JSON data
            
        Returns:
            Text representation
        """
        if isinstance(data, dict):
            lines = []
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    lines.append(f"{key}: {json.dumps(value, indent=2)}")
                else:
                    lines.append(f"{key}: {value}")
            return "\n".join(lines)
        else:
            return json.dumps(data, indent=2)
    
    def _extract_keys(self, data: Union[Dict, List], prefix: str = "") -> List[str]:
        """
        Extract all keys from JSON data.
        
        Args:
            data: JSON data
            prefix: Key prefix for nested items
            
        Returns:
            List of keys
        """
        keys = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                keys.append(full_key)
                
                if isinstance(value, (dict, list)):
                    keys.extend(self._extract_keys(value, full_key))
        elif isinstance(data, list):
            for i, item in enumerate(data):
                if isinstance(item, (dict, list)):
                    keys.extend(self._extract_keys(item, f"{prefix}[{i}]"))
        
        return keys
    
    async def _store_json_structure(self, document_id: str, data: Union[Dict, List]) -> None:
        """
        Store JSON structure in the knowledge graph.
        
        Args:
            document_id: Document ID
            data: JSON data
        """
        # Create a document node if it doesn't exist
        doc_query = """
        MERGE (d:Document {id: $id})
        ON CREATE SET d.content_type = 'application/json'
        RETURN d.id as id
        """
        
        await self.neo4j_client.run_query(doc_query, {"id": document_id})
        
        # Store the structure
        if isinstance(data, dict):
            await self._store_json_object(document_id, data, "root")
        elif isinstance(data, list):
            await self._store_json_array(document_id, data, "root")
    
    async def _store_json_object(self, document_id: str, data: Dict, path: str) -> None:
        """
        Store JSON object in the knowledge graph.
        
        Args:
            document_id: Document ID
            data: JSON object
            path: Path to the object
        """
        # Create an object node
        obj_query = """
        MATCH (d:Document {id: $document_id})
        MERGE (o:JsonObject {document_id: $document_id, path: $path})
        MERGE (d)-[:HAS_OBJECT]->(o)
        RETURN o.path as path
        """
        
        await self.neo4j_client.run_query(obj_query, {
            "document_id": document_id,
            "path": path
        })
        
        # Store each property
        for key, value in data.items():
            prop_path = f"{path}.{key}"
            
            if isinstance(value, dict):
                # Nested object
                await self._store_json_object(document_id, value, prop_path)
                
                # Create relationship
                rel_query = """
                MATCH (p:JsonObject {document_id: $document_id, path: $parent_path})
                MATCH (c:JsonObject {document_id: $document_id, path: $child_path})
                MERGE (p)-[:HAS_PROPERTY {key: $key}]->(c)
                """
                
                await self.neo4j_client.run_query(rel_query, {
                    "document_id": document_id,
                    "parent_path": path,
                    "child_path": prop_path,
                    "key": key
                })
            elif isinstance(value, list):
                # Array
                await self._store_json_array(document_id, value, prop_path)
                
                # Create relationship
                rel_query = """
                MATCH (o:JsonObject {document_id: $document_id, path: $path})
                MATCH (a:JsonArray {document_id: $document_id, path: $array_path})
                MERGE (o)-[:HAS_PROPERTY {key: $key}]->(a)
                """
                
                await self.neo4j_client.run_query(rel_query, {
                    "document_id": document_id,
                    "path": path,
                    "array_path": prop_path,
                    "key": key
                })
            else:
                # Simple value
                val_query = """
                MATCH (o:JsonObject {document_id: $document_id, path: $path})
                MERGE (v:JsonValue {
                    document_id: $document_id, 
                    path: $value_path,
                    type: $type,
                    value: $value
                })
                MERGE (o)-[:HAS_PROPERTY {key: $key}]->(v)
                """
                
                await self.neo4j_client.run_query(val_query, {
                    "document_id": document_id,
                    "path": path,
                    "value_path": prop_path,
                    "type": type(value).__name__,
                    "value": str(value),
                    "key": key
                })
    
    async def _store_json_array(self, document_id: str, data: List, path: str) -> None:
        """
        Store JSON array in the knowledge graph.
        
        Args:
            document_id: Document ID
            data: JSON array
            path: Path to the array
        """
        # Create an array node
        arr_query = """
        MATCH (d:Document {id: $document_id})
        MERGE (a:JsonArray {
            document_id: $document_id, 
            path: $path,
            length: $length
        })
        MERGE (d)-[:HAS_ARRAY]->(a)
        RETURN a.path as path
        """
        
        await self.neo4j_client.run_query(arr_query, {
            "document_id": document_id,
            "path": path,
            "length": len(data)
        })
        
        # Store each item
        for i, item in enumerate(data):
            item_path = f"{path}[{i}]"
            
            if isinstance(item, dict):
                # Object
                await self._store_json_object(document_id, item, item_path)
                
                # Create relationship
                rel_query = """
                MATCH (a:JsonArray {document_id: $document_id, path: $path})
                MATCH (o:JsonObject {document_id: $document_id, path: $item_path})
                MERGE (a)-[:HAS_ITEM {index: $index}]->(o)
                """
                
                await self.neo4j_client.run_query(rel_query, {
                    "document_id": document_id,
                    "path": path,
                    "item_path": item_path,
                    "index": i
                })
            elif isinstance(item, list):
                # Nested array
                await self._store_json_array(document_id, item, item_path)
                
                # Create relationship
                rel_query = """
                MATCH (a:JsonArray {document_id: $document_id, path: $path})
                MATCH (na:JsonArray {document_id: $document_id, path: $item_path})
                MERGE (a)-[:HAS_ITEM {index: $index}]->(na)
                """
                
                await self.neo4j_client.run_query(rel_query, {
                    "document_id": document_id,
                    "path": path,
                    "item_path": item_path,
                    "index": i
                })
            else:
                # Simple value
                val_query = """
                MATCH (a:JsonArray {document_id: $document_id, path: $path})
                MERGE (v:JsonValue {
                    document_id: $document_id, 
                    path: $item_path,
                    type: $type,
                    value: $value
                })
                MERGE (a)-[:HAS_ITEM {index: $index}]->(v)
                """
                
                await self.neo4j_client.run_query(val_query, {
                    "document_id": document_id,
                    "path": path,
                    "item_path": item_path,
                    "type": type(item).__name__,
                    "value": str(item),
                    "index": i
                }) 

--- app/processors/markdown_processor.py.txt ---
"""
Markdown document processor.

This module provides a processor for Markdown documents.
"""
import logging
import re
from typing import Dict, List, Any, Optional, Tuple

from app.processors.text_processor import TextProcessor

logger = logging.getLogger(__name__)

class MarkdownProcessor(TextProcessor):
    """
    Processor for Markdown documents.
    
    This class extends the text processor with Markdown-specific
    processing capabilities, including header extraction and structure awareness.
    """
    
    async def process(self, content: str, **kwargs) -> Dict[str, Any]:
        """
        Process a Markdown document.
        
        Args:
            content: Markdown content to process
            **kwargs: Additional processing options
                - extract_headers: Whether to extract headers (default: True)
                - structure_aware: Whether to use structure-aware chunking (default: True)
                - other options from TextProcessor
        
        Returns:
            Processing result with document ID and metadata
        """
        # Extract options
        extract_headers = kwargs.get("extract_headers", True)
        structure_aware = kwargs.get("structure_aware", True)
        
        # Update metadata
        metadata = kwargs.get("metadata", {})
        metadata["content_type"] = "text/markdown"
        kwargs["metadata"] = metadata
        
        # Extract headers if requested
        if extract_headers:
            headers = self._extract_headers(content)
            metadata["headers"] = headers
            
            # Try to determine title from headers
            if headers and not metadata.get("title"):
                metadata["title"] = headers[0][1]
        
        # Use structure-aware chunking if requested
        if structure_aware:
            # Override the default chunking with structure-aware chunking
            original_split_text = self._split_text
            self._split_text = self._structure_aware_split
            
            # Process the document
            result = await super().process(content, **kwargs)
            
            # Restore the original chunking method
            self._split_text = original_split_text
            
            return result
        
        # Use default text processing
        return await super().process(content, **kwargs)
    
    def _extract_headers(self, markdown: str) -> List[Tuple[int, str]]:
        """
        Extract headers from Markdown content.
        
        Args:
            markdown: Markdown content
            
        Returns:
            List of (level, header) tuples
        """
        # Regular expression to match Markdown headers
        header_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        
        # Find all headers
        headers = []
        for match in header_pattern.finditer(markdown):
            level = len(match.group(1))
            header = match.group(2).strip()
            headers.append((level, header))
        
        return headers
    
    def _structure_aware_split(self, text: str, chunk_size: int, chunk_overlap: int) -> List[str]:
        """
        Split Markdown text into chunks, respecting structural elements.
        
        Args:
            text: Markdown text to split
            chunk_size: Maximum chunk size
            chunk_overlap: Overlap between chunks
            
        Returns:
            List of text chunks
        """
        if len(text) <= chunk_size:
            return [text]
        
        # Define Markdown structural separators in order of precedence
        separators = [
            r'(?=^#{1,6}\s+.+$)',  # Headers
            r'(?=^---+$)',         # Horizontal rules
            r'(?=^```)',           # Code blocks
            r'(?=^\s*\n\s*$)',     # Blank lines
            r'(?=^[*-]\s+)',       # List items
            r'(?=^\d+\.\s+)'       # Numbered list items
        ]
        
        # Compile the regex pattern
        pattern = '|'.join(separators)
        regex = re.compile(pattern, re.MULTILINE)
        
        # Split the text into sections based on structural elements
        sections = regex.split(text)
        
        # Now combine sections into chunks respecting the chunk size
        chunks = []
        current_chunk = ""
        
        for section in sections:
            # If adding this section would exceed the chunk size and we already have content
            if len(current_chunk) + len(section) > chunk_size and current_chunk:
                chunks.append(current_chunk)
                # Start a new chunk with overlap
                overlap_start = max(0, len(current_chunk) - chunk_overlap)
                current_chunk = current_chunk[overlap_start:] + section
            else:
                current_chunk += section
        
        # Add the last chunk if it has content
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    async def _create_chunk_relationship(self, prev_chunk_id: str, next_chunk_id: str) -> None:
        """
        Create a relationship between consecutive chunks in the knowledge graph.
        
        Args:
            prev_chunk_id: ID of the previous chunk
            next_chunk_id: ID of the next chunk
        """
        # Call the parent method
        await super()._create_chunk_relationship(prev_chunk_id, next_chunk_id)
        
        # Add Markdown-specific relationship
        query = """
        MATCH (prev:Chunk {id: $prev_id})
        MATCH (next:Chunk {id: $next_id})
        CREATE (prev)-[:MARKDOWN_NEXT]->(next)
        """
        
        params = {
            "prev_id": prev_chunk_id,
            "next_id": next_chunk_id
        }
        
        await self.neo4j_client.run_query(query, params) 

--- app/processors/pdf_processor.py.txt ---
"""
PDF document processor.

This module provides a processor for PDF documents that extracts text,
structure, and images from PDF files.
"""
import logging
import io
import tempfile
from typing import Dict, Any, List, Optional, Tuple, BinaryIO
import base64
from pathlib import Path

# PDF processing libraries
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    
try:
    from pdfminer.high_level import extract_text as pdfminer_extract_text
    from pdfminer.layout import LAParams
    PDFMINER_AVAILABLE = True
except ImportError:
    PDFMINER_AVAILABLE = False

from .base import BaseProcessor
from .image_processor import ImageProcessor

logger = logging.getLogger(__name__)

class PDFProcessor(BaseProcessor):
    """
    Processor for PDF documents.
    
    This processor extracts text, structure, and images from PDF files.
    It uses PyMuPDF (fitz) as the primary engine with fallback to pdfminer.six.
    """
    
    def __init__(self, 
                 extract_images: bool = True,
                 ocr_images: bool = False,
                 extract_tables: bool = True,
                 detect_headers: bool = True,
                 **kwargs):
        """
        Initialize the PDF processor.
        
        Args:
            extract_images: Whether to extract images from the PDF
            ocr_images: Whether to run OCR on extracted images
            extract_tables: Whether to extract tables from the PDF
            detect_headers: Whether to detect headers and structure
            **kwargs: Additional options for the base processor
        """
        super().__init__(**kwargs)
        
        if not PYMUPDF_AVAILABLE and not PDFMINER_AVAILABLE:
            raise ImportError(
                "No PDF processing library available. "
                "Please install PyMuPDF (pip install pymupdf) or "
                "pdfminer.six (pip install pdfminer.six)."
            )
        
        self.extract_images = extract_images
        self.ocr_images = ocr_images
        self.extract_tables = extract_tables
        self.detect_headers = detect_headers
        
        # Create an image processor for handling extracted images
        if extract_images:
            self.image_processor = ImageProcessor(**kwargs)
    
    def process(self, content: Any, metadata: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """
        Process a PDF document.
        
        Args:
            content: PDF content (bytes, file-like object, or path)
            metadata: Document metadata
            **kwargs: Additional processing options
            
        Returns:
            Processing results including extracted text, structure, and images
        """
        if metadata is None:
            metadata = {}
        
        # Prepare PDF content for processing
        pdf_content = self._prepare_content(content)
        
        # Extract text and structure
        extracted_text, structure = self._extract_text_and_structure(pdf_content)
        
        # Create chunks based on the document structure
        chunks = self.create_chunks(extracted_text, structure=structure, **kwargs)
        
        # Extract and process images if enabled
        images = []
        if self.extract_images:
            images = self._extract_images(pdf_content)
            
            # Process images with OCR if enabled
            if self.ocr_images and images:
                for i, img_data in enumerate(images):
                    img_result = self.image_processor.process(
                        img_data['image'], 
                        metadata={'page': img_data['page'], 'index': i}
                    )
                    
                    # Add OCR text to the image data
                    if 'extracted_text' in img_result:
                        images[i]['ocr_text'] = img_result['extracted_text']
                        
                        # Add image text as additional chunks
                        if img_result['extracted_text'].strip():
                            img_chunk = {
                                'text': img_result['extracted_text'],
                                'type': 'image_text',
                                'metadata': {
                                    'page': img_data['page'],
                                    'image_index': i
                                }
                            }
                            chunks.append(img_chunk)
        
        # Extract tables if enabled
        tables = []
        if self.extract_tables:
            tables = self._extract_tables(pdf_content)
            
            # Add table text as additional chunks
            for i, table in enumerate(tables):
                if 'text' in table and table['text'].strip():
                    table_chunk = {
                        'text': table['text'],
                        'type': 'table',
                        'metadata': {
                            'page': table['page'],
                            'table_index': i
                        }
                    }
                    chunks.append(table_chunk)
        
        # Prepare result
        result = {
            'chunks': chunks,
            'extracted_text': extracted_text,
            'metadata': metadata
        }
        
        # Add document structure if available
        if structure:
            result['structure'] = structure
            
        # Add images if extracted
        if images:
            result['images'] = images
            
        # Add tables if extracted
        if tables:
            result['tables'] = tables
        
        return result
    
    def _prepare_content(self, content: Any) -> bytes:
        """
        Prepare PDF content for processing.
        
        Args:
            content: PDF content (bytes, file-like object, or path)
            
        Returns:
            PDF content as bytes
        """
        if isinstance(content, bytes):
            return content
        elif isinstance(content, (str, Path)):
            with open(content, 'rb') as f:
                return f.read()
        elif hasattr(content, 'read'):
            # File-like object
            return content.read()
        else:
            raise ValueError(f"Unsupported content type: {type(content)}")
    
    def _extract_text_and_structure(self, pdf_content: bytes) -> Tuple[str, Dict[str, Any]]:
        """
        Extract text and structure from a PDF document.
        
        Args:
            pdf_content: PDF content as bytes
            
        Returns:
            Tuple of (extracted text, structure dict)
        """
        if PYMUPDF_AVAILABLE:
            return self._extract_with_pymupdf(pdf_content)
        elif PDFMINER_AVAILABLE:
            return self._extract_with_pdfminer(pdf_content)
        else:
            raise ImportError("No PDF processing library available")
    
    def _extract_with_pymupdf(self, pdf_content: bytes) -> Tuple[str, Dict[str, Any]]:
        """
        Extract text and structure using PyMuPDF.
        
        Args:
            pdf_content: PDF content as bytes
            
        Returns:
            Tuple of (extracted text, structure dict)
        """
        full_text = []
        structure = {
            'pages': [],
            'toc': [],
            'headers': []
        }
        
        # Create a temporary file for PyMuPDF
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            tmp.write(pdf_content)
            tmp_path = tmp.name
        
        try:
            # Open the PDF with PyMuPDF
            doc = fitz.open(tmp_path)
            
            # Extract table of contents if available
            toc = doc.get_toc()
            if toc:
                structure['toc'] = [
                    {'level': level, 'title': title, 'page': page} 
                    for level, title, page in toc
                ]
            
            # Process each page
            for page_num, page in enumerate(doc):
                # Extract text
                page_text = page.get_text()
                full_text.append(page_text)
                
                # Extract page structure
                page_structure = {
                    'number': page_num + 1,
                    'width': page.rect.width,
                    'height': page.rect.height
                }
                
                # Detect headers if enabled
                if self.detect_headers:
                    blocks = page.get_text("dict")["blocks"]
                    headers = []
                    
                    for block in blocks:
                        if "lines" in block:
                            for line in block["lines"]:
                                if line["spans"]:
                                    # Check if this might be a header (larger font, bold, etc.)
                                    for span in line["spans"]:
                                        if span["size"] > 12 or "bold" in span.get("font", "").lower():
                                            header_text = span["text"].strip()
                                            if header_text:
                                                headers.append({
                                                    'text': header_text,
                                                    'page': page_num + 1,
                                                    'font_size': span["size"],
                                                    'is_bold': "bold" in span.get("font", "").lower()
                                                })
                                            break
                    
                    if headers:
                        page_structure['headers'] = headers
                        structure['headers'].extend(headers)
                
                structure['pages'].append(page_structure)
            
            # Close the document
            doc.close()
            
        finally:
            # Clean up the temporary file
            Path(tmp_path).unlink(missing_ok=True)
        
        return "\n\n".join(full_text), structure
    
    def _extract_with_pdfminer(self, pdf_content: bytes) -> Tuple[str, Dict[str, Any]]:
        """
        Extract text and structure using pdfminer.six.
        
        Args:
            pdf_content: PDF content as bytes
            
        Returns:
            Tuple of (extracted text, structure dict)
        """
        # Basic structure without detailed info
        structure = {
            'pages': [],
            'headers': []
        }
        
        # Extract text with pdfminer
        text = pdfminer_extract_text(io.BytesIO(pdf_content), laparams=LAParams())
        
        return text, structure
    
    def _extract_images(self, pdf_content: bytes) -> List[Dict[str, Any]]:
        """
        Extract images from a PDF document.
        
        Args:
            pdf_content: PDF content as bytes
            
        Returns:
            List of extracted images with metadata
        """
        if not PYMUPDF_AVAILABLE:
            logger.warning("PyMuPDF not available, skipping image extraction")
            return []
        
        images = []
        
        # Create a temporary file for PyMuPDF
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            tmp.write(pdf_content)
            tmp_path = tmp.name
        
        try:
            # Open the PDF with PyMuPDF
            doc = fitz.open(tmp_path)
            
            # Process each page
            for page_num, page in enumerate(doc):
                # Extract images
                image_list = page.get_images(full=True)
                
                for img_index, img in enumerate(image_list):
                    xref = img[0]
                    base_image = doc.extract_image(xref)
                    
                    if base_image:
                        image_bytes = base_image["image"]
                        image_ext = base_image["ext"]
                        
                        # Encode image as base64 for storage
                        image_b64 = base64.b64encode(image_bytes).decode('utf-8')
                        
                        images.append({
                            'page': page_num + 1,
                            'index': img_index,
                            'width': base_image.get('width'),
                            'height': base_image.get('height'),
                            'format': image_ext,
                            'image': image_bytes,  # Raw bytes for processing
                            'image_b64': image_b64  # Base64 for storage
                        })
            
            # Close the document
            doc.close()
            
        finally:
            # Clean up the temporary file
            Path(tmp_path).unlink(missing_ok=True)
        
        return images
    
    def _extract_tables(self, pdf_content: bytes) -> List[Dict[str, Any]]:
        """
        Extract tables from a PDF document.
        
        Args:
            pdf_content: PDF content as bytes
            
        Returns:
            List of extracted tables with metadata
        """
        # For now, use a simple heuristic approach
        # A more robust solution would use a dedicated PDF table extraction library
        tables = []
        
        if not PYMUPDF_AVAILABLE:
            return tables
        
        # Create a temporary file for PyMuPDF
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            tmp.write(pdf_content)
            tmp_path = tmp.name
        
        try:
            # Open the PDF with PyMuPDF
            doc = fitz.open(tmp_path)
            
            # Process each page
            for page_num, page in enumerate(doc):
                # Simple table detection based on text blocks
                blocks = page.get_text("blocks")
                
                for block_idx, block in enumerate(blocks):
                    block_text = block[4]
                    
                    # Heuristic: Check if the block might be a table
                    # (contains multiple lines with similar structure)
                    lines = block_text.split('\n')
                    if len(lines) > 3:  # At least 3 rows
                        # Check if lines have similar structure (e.g., same number of separators)
                        separators = ['\t', '  ', '|', ',']
                        
                        for sep in separators:
                            if all(sep in line for line in lines[:3]):  # First 3 lines contain separator
                                tables.append({
                                    'page': page_num + 1,
                                    'block': block_idx,
                                    'text': block_text,
                                    'separator': sep,
                                    'rows': len(lines)
                                })
                                break
            
            # Close the document
            doc.close()
            
        finally:
            # Clean up the temporary file
            Path(tmp_path).unlink(missing_ok=True)
        
        return tables
    
    def create_chunks(self, text: str, structure: Dict[str, Any] = None, **kwargs) -> List[Dict[str, Any]]:
        """
        Create chunks from PDF text, using structure information if available.
        
        Args:
            text: Extracted text
            structure: Document structure information
            **kwargs: Additional chunking options
            
        Returns:
            List of text chunks
        """
        chunks = []
        
        # If we have TOC or headers, use them for structure-aware chunking
        if structure and (structure.get('toc') or structure.get('headers')):
            # Combine TOC and headers for section detection
            sections = []
            
            if structure.get('toc'):
                for item in structure['toc']:
                    sections.append({
                        'title': item['title'],
                        'page': item['page'],
                        'level': item['level']
                    })
            
            if structure.get('headers'):
                for header in structure['headers']:
                    # Only add headers that aren't already in TOC
                    if not any(s['title'] == header['text'] for s in sections):
                        sections.append({
                            'title': header['text'],
                            'page': header['page'],
                            'level': 1  # Assume top-level for detected headers
                        })
            
            # Sort sections by page number
            sections.sort(key=lambda x: x['page'])
            
            # Split text by sections if we have sections
            if sections:
                # Use section titles as markers to split the text
                current_pos = 0
                
                for i, section in enumerate(sections):
                    section_title = section['title']
                    
                    # Find the section title in the text
                    section_pos = text.find(section_title, current_pos)
                    
                    if section_pos != -1:
                        # Add the text before this section if it's not the first section
                        if i > 0 and section_pos > current_pos:
                            prev_text = text[current_pos:section_pos].strip()
                            if prev_text:
                                chunks.append({
                                    'text': prev_text,
                                    'type': 'section',
                                    'metadata': {
                                        'section': sections[i-1]['title'],
                                        'level': sections[i-1]['level'],
                                        'page': sections[i-1]['page']
                                    }
                                })
                        
                        current_pos = section_pos + len(section_title)
                
                # Add the final section
                if current_pos < len(text):
                    final_text = text[current_pos:].strip()
                    if final_text:
                        chunks.append({
                            'text': final_text,
                            'type': 'section',
                            'metadata': {
                                'section': sections[-1]['title'] if sections else None,
                                'level': sections[-1]['level'] if sections else None,
                                'page': sections[-1]['page'] if sections else None
                            }
                        })
                
                return chunks
        
        # Fall back to standard chunking if we couldn't use structure
        return super().create_chunks(text, **kwargs)
    
    def store_in_graph(self, document_id: str, metadata: Dict[str, Any], 
                      chunks: List[Dict[str, Any]], **kwargs) -> None:
        """
        Store PDF document in graph database.
        
        Args:
            document_id: Document ID
            metadata: Document metadata
            chunks: Document chunks
            **kwargs: Additional storage options
        """
        # Add PDF-specific metadata
        metadata['content_type'] = 'application/pdf'
        
        # Store document node
        doc_properties = {
            'id': document_id,
            'content_type': 'application/pdf',
            **metadata
        }
        
        # Create document node
        self.graph_db.create_node('Document', doc_properties)
        
        # Store each chunk
        for i, chunk in enumerate(chunks):
            chunk_id = f"{document_id}_chunk_{i}"
            chunk_type = chunk.get('type', 'text')
            
            # Create chunk node
            chunk_properties = {
                'id': chunk_id,
                'text': chunk['text'],
                'type': chunk_type,
                'index': i,
                **chunk.get('metadata', {})
            }
            self.graph_db.create_node('Chunk', chunk_properties)
            
            # Connect chunk to document
            self.graph_db.create_relationship(
                'Document', {'id': document_id},
                'HAS_CHUNK', {},
                'Chunk', {'id': chunk_id}
            )
            
            # Create special relationships based on chunk type
            if chunk_type == 'section':
                section_title = chunk.get('metadata', {}).get('section')
                if section_title:
                    # Create section node if it doesn't exist
                    section_id = f"{document_id}_section_{section_title}"
                    self.graph_db.create_node('Section', {
                        'id': section_id,
                        'title': section_title,
                        'level': chunk.get('metadata', {}).get('level', 1)
                    })
                    
                    # Connect chunk to section
                    self.graph_db.create_relationship(
                        'Section', {'id': section_id},
                        'CONTAINS', {},
                        'Chunk', {'id': chunk_id}
                    )
                    
                    # Connect document to section
                    self.graph_db.create_relationship(
                        'Document', {'id': document_id},
                        'HAS_SECTION', {},
                        'Section', {'id': section_id}
                    )
            
            elif chunk_type == 'table':
                table_index = chunk.get('metadata', {}).get('table_index')
                if table_index is not None:
                    # Create table node
                    table_id = f"{document_id}_table_{table_index}"
                    self.graph_db.create_node('Table', {
                        'id': table_id,
                        'index': table_index,
                        'page': chunk.get('metadata', {}).get('page')
                    })
                    
                    # Connect chunk to table
                    self.graph_db.create_relationship(
                        'Table', {'id': table_id},
                        'HAS_CONTENT', {},
                        'Chunk', {'id': chunk_id}
                    )
                    
                    # Connect document to table
                    self.graph_db.create_relationship(
                        'Document', {'id': document_id},
                        'HAS_TABLE', {},
                        'Table', {'id': table_id}
                    )
            
            elif chunk_type == 'image_text':
                image_index = chunk.get('metadata', {}).get('image_index')
                if image_index is not None:
                    # Create image node
                    image_id = f"{document_id}_image_{image_index}"
                    self.graph_db.create_node('Image', {
                        'id': image_id,
                        'index': image_index,
                        'page': chunk.get('metadata', {}).get('page')
                    })
                    
                    # Connect chunk to image
                    self.graph_db.create_relationship(
                        'Image', {'id': image_id},
                        'HAS_TEXT', {},
                        'Chunk', {'id': chunk_id}
                    )
                    
                    # Connect document to image
                    self.graph_db.create_relationship(
                        'Document', {'id': document_id},
                        'HAS_IMAGE', {},
                        'Image', {'id': image_id}
                    )
    
    def store_in_vector_db(self, document_id: str, metadata: Dict[str, Any],
                          chunks: List[Dict[str, Any]], **kwargs) -> None:
        """
        Store PDF document in vector database.
        
        Args:
            document_id: Document ID
            metadata: Document metadata
            chunks: Document chunks
            **kwargs: Additional storage options
        """
        # Add PDF-specific metadata
        metadata['content_type'] = 'application/pdf'
        
        # Store each chunk with its embeddings
        for i, chunk in enumerate(chunks):
            chunk_id = f"{document_id}_chunk_{i}"
            chunk_text = chunk['text']
            
            # Get embeddings for the chunk
            embeddings = self.get_embeddings(chunk_text)
            
            # Prepare chunk metadata
            chunk_metadata = {
                'document_id': document_id,
                'chunk_id': chunk_id,
                'chunk_index': i,
                'chunk_type': chunk.get('type', 'text'),
                **metadata,
                **chunk.get('metadata', {})
            }
            
            # Store in vector database
            self.vector_db.add_vectors(
                collection_name=kwargs.get('collection_name', 'documents'),
                vectors=[(chunk_id, embeddings, chunk_metadata)],
                batch_size=kwargs.get('batch_size', 100)
            ) 

--- app/processors/privacy_processor.py.txt ---
"""
Privacy-compliant document processor.

This module provides a processor wrapper that ensures documents are processed
in compliance with privacy regulations (GDPR, CCPA, etc.) by identifying and
handling sensitive information.
"""
import logging
import re
from typing import Dict, Any, List, Optional, Set, Union

from app.processors.base import BaseProcessor

logger = logging.getLogger(__name__)

class PrivacyCompliantProcessor(BaseProcessor):
    """
    Privacy-compliant processor wrapper.
    
    This processor wraps another processor and ensures that sensitive information
    is properly identified, redacted, or encrypted according to privacy requirements.
    """
    
    # Common patterns for sensitive data
    PII_PATTERNS = {
        "email": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        "phone": r'\b(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b',
        "ssn": r'\b\d{3}-\d{2}-\d{4}\b',
        "credit_card": r'\b(?:\d{4}[- ]?){3}\d{4}\b',
        "ip_address": r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b',
    }
    
    def __init__(self, 
                 base_processor: BaseProcessor,
                 redact_pii: bool = True,
                 pii_types: Optional[List[str]] = None,
                 custom_patterns: Optional[Dict[str, str]] = None,
                 **kwargs):
        """
        Initialize the privacy processor.
        
        Args:
            base_processor: The underlying processor to wrap
            redact_pii: Whether to redact personally identifiable information
            pii_types: List of PII types to redact (default: all)
            custom_patterns: Custom regex patterns for additional PII types
            **kwargs: Additional options passed to the base processor
        """
        super().__init__(**kwargs)
        self.base_processor = base_processor
        self.redact_pii = redact_pii
        self.pii_types = set(pii_types) if pii_types else set(self.PII_PATTERNS.keys())
        
        # Compile regex patterns
        self.patterns = {}
        for pii_type, pattern in self.PII_PATTERNS.items():
            if pii_type in self.pii_types:
                self.patterns[pii_type] = re.compile(pattern)
        
        # Add custom patterns
        if custom_patterns:
            for pii_type, pattern in custom_patterns.items():
                self.patterns[pii_type] = re.compile(pattern)
    
    def process(self, content: Any, metadata: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """
        Process the document with privacy compliance.
        
        Args:
            content: Document content
            metadata: Document metadata
            **kwargs: Additional processing options
            
        Returns:
            Processing results with sensitive data handled appropriately
        """
        # First, let the base processor handle the content
        result = self.base_processor.process(content, metadata, **kwargs)
        
        # Then apply privacy compliance
        if self.redact_pii:
            # Track what was redacted for logging/auditing
            redacted_items = {}
            
            # Process text chunks
            if 'chunks' in result:
                for i, chunk in enumerate(result['chunks']):
                    if 'text' in chunk:
                        chunk['text'], chunk_redacted = self._redact_text(chunk['text'])
                        for pii_type, count in chunk_redacted.items():
                            redacted_items[pii_type] = redacted_items.get(pii_type, 0) + count
            
            # Process any extracted text
            if 'extracted_text' in result:
                result['extracted_text'], text_redacted = self._redact_text(result['extracted_text'])
                for pii_type, count in text_redacted.items():
                    redacted_items[pii_type] = redacted_items.get(pii_type, 0) + count
            
            # Add redaction metadata
            if redacted_items:
                if 'metadata' not in result:
                    result['metadata'] = {}
                result['metadata']['privacy'] = {
                    'redacted': True,
                    'redacted_items': redacted_items
                }
                
                logger.info(f"Redacted PII: {redacted_items}")
        
        return result
    
    def _redact_text(self, text: str) -> tuple[str, Dict[str, int]]:
        """
        Redact PII from text.
        
        Args:
            text: Text to redact
            
        Returns:
            Tuple of (redacted text, dict of redaction counts by type)
        """
        redacted_counts = {}
        
        for pii_type, pattern in self.patterns.items():
            matches = list(pattern.finditer(text))
            if matches:
                redacted_counts[pii_type] = len(matches)
                
                # Replace each match with a redaction marker
                for match in reversed(matches):  # Process in reverse to maintain indices
                    start, end = match.span()
                    replacement = f"[REDACTED:{pii_type}]"
                    text = text[:start] + replacement + text[end:]
        
        return text, redacted_counts
    
    def get_embeddings(self, text: str, **kwargs) -> List[float]:
        """
        Get embeddings for text, ensuring privacy compliance.
        
        Args:
            text: Text to embed
            **kwargs: Additional embedding options
            
        Returns:
            Text embeddings
        """
        # Redact before embedding if needed
        if self.redact_pii:
            text, _ = self._redact_text(text)
        
        return self.base_processor.get_embeddings(text, **kwargs)
    
    def create_chunks(self, text: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Create chunks from text, ensuring privacy compliance.
        
        Args:
            text: Text to chunk
            **kwargs: Additional chunking options
            
        Returns:
            List of text chunks
        """
        # Let the base processor create chunks
        chunks = self.base_processor.create_chunks(text, **kwargs)
        
        # Apply redaction to each chunk if needed
        if self.redact_pii:
            for chunk in chunks:
                if 'text' in chunk:
                    chunk['text'], _ = self._redact_text(chunk['text'])
        
        return chunks
    
    def store_in_graph(self, document_id: str, metadata: Dict[str, Any], 
                      chunks: List[Dict[str, Any]], **kwargs) -> None:
        """
        Store document in graph database with privacy compliance.
        
        Args:
            document_id: Document ID
            metadata: Document metadata
            chunks: Document chunks
            **kwargs: Additional storage options
        """
        # Add privacy metadata
        if 'privacy' not in metadata:
            metadata['privacy'] = {
                'redacted': self.redact_pii,
                'pii_types_checked': list(self.pii_types)
            }
        
        # Let the base processor handle storage
        self.base_processor.store_in_graph(document_id, metadata, chunks, **kwargs)
    
    def store_in_vector_db(self, document_id: str, metadata: Dict[str, Any],
                          chunks: List[Dict[str, Any]], **kwargs) -> None:
        """
        Store document in vector database with privacy compliance.
        
        Args:
            document_id: Document ID
            metadata: Document metadata
            chunks: Document chunks
            **kwargs: Additional storage options
        """
        # Add privacy metadata
        if 'privacy' not in metadata:
            metadata['privacy'] = {
                'redacted': self.redact_pii,
                'pii_types_checked': list(self.pii_types)
            }
        
        # Let the base processor handle storage
        self.base_processor.store_in_vector_db(document_id, metadata, chunks, **kwargs) 

--- app/processors/processors_updated.py.txt ---
"""
Updated document processor implementation with multi-provider LLM support.

This module provides an enhanced version of the BaseProcessor class 
that leverages the multi-provider LLM system for intelligent processing.
"""
import logging
import os
from typing import Dict, List, Any, Optional, Union

from app.processors.base import BaseProcessor
from app.core.enhanced_ai_layer import EnhancedAILayer, default_ai_layer
from app.config.provider_config import (
    DEFAULT_PRIORITY,
    DEFAULT_USER_TIER,
    get_recommended_providers
)

logger = logging.getLogger(__name__)

class MultiProviderProcessor(BaseProcessor):
    """
    Enhanced document processor with multi-provider LLM support.
    
    This class extends the base processor with support for multiple LLM providers,
    intelligent provider selection, fallback mechanisms, and advanced AI capabilities.
    """
    
    def __init__(
        self,
        dataset_name: str = None,
        use_cognee: bool = False,
        enable_ai: bool = True,
        ai_layer: Optional[EnhancedAILayer] = None,
        user_tier: str = DEFAULT_USER_TIER,
        priority: str = DEFAULT_PRIORITY
    ):
        """
        Initialize the multi-provider processor.
        
        Args:
            dataset_name: Name of the dataset (for Cognee integration)
            use_cognee: Whether to use Cognee for database operations
            enable_ai: Whether to enable AI enhancements
            ai_layer: Enhanced AI layer to use (uses default if None)
            user_tier: User subscription tier
            priority: Priority for provider selection
        """
        # Initialize base processor
        super().__init__(
            dataset_name=dataset_name,
            use_cognee=use_cognee,
            enable_ai=enable_ai
        )
        
        # Set up enhanced AI layer
        self.enable_ai = enable_ai
        if enable_ai:
            self.ai_layer = ai_layer or default_ai_layer
        
        # Set user tier and priority
        self.user_tier = user_tier
        self.priority = priority
        
        # Additional metadata
        self.add_metadata("processor_type", "multi_provider")
        self.add_metadata("user_tier", user_tier)
        self.add_metadata("priority", priority)
    
    async def process_with_enhancements(self, content: Any, **kwargs) -> Dict[str, Any]:
        """
        Process the document content with AI enhancements using optimal provider.
        
        Args:
            content: Document content to process
            **kwargs: Additional processing options
            
        Returns:
            Enhanced processing result
        """
        # Standard processing first
        result = await self.process(content, **kwargs)
        
        # Apply AI enhancements if enabled
        if self.enable_ai and hasattr(self, "ai_layer"):
            content_type = kwargs.get("content_type") or self._detect_content_type(content)
            enhancement_type = kwargs.get("enhancement_type", "analysis")
            
            # Determine optimal provider based on task type
            task_type = self._map_enhancement_to_task(enhancement_type)
            provider = kwargs.get("provider")
            priority = kwargs.get("priority", self.priority)
            
            # Enhanced content processing
            enhanced_content = await self.ai_layer.enhance_content(
                content=result.get("processed_content", content),
                content_type=content_type,
                enhancement_type=enhancement_type,
                provider=provider,
                priority=priority,
                task_type=task_type
            )
            
            if enhanced_content:
                result["enhanced_content"] = enhanced_content
                result["has_enhancements"] = True
                
                # Add provider info to metadata if available
                provider_health = self.ai_layer.get_provider_health()
                if provider_health:
                    available_providers = self.ai_layer.get_available_providers()
                    result["provider_info"] = {
                        "available_providers": available_providers,
                        "recommended_providers": get_recommended_providers(task_type, self.user_tier)
                    }
        
        return result
    
    def _map_enhancement_to_task(self, enhancement_type: str) -> str:
        """
        Map enhancement type to task type for provider selection.
        
        Args:
            enhancement_type: Type of enhancement
            
        Returns:
            Corresponding task type
        """
        # Map enhancement types to task types
        enhancement_to_task = {
            "analysis": "analysis",
            "summary": "summarization",
            "extract": "extraction",
            "classify": "classification",
            "generate": "generation",
            "translate": "translation",
            "research": "research"
        }
        
        return enhancement_to_task.get(enhancement_type, "chat")
    
    async def generate_embeddings(self, text: str, model: str = "text-embedding-3-large") -> List[float]:
        """
        Generate embeddings for text using optimal provider.
        
        Args:
            text: Text to generate embeddings for
            model: Embedding model to use
            
        Returns:
            Vector embedding
        """
        # Use enhanced AI layer for embeddings if available
        if self.enable_ai and hasattr(self, "ai_layer"):
            embeddings = await self.ai_layer.generate_embeddings(text, model)
            if embeddings:
                return embeddings
        
        # Fall back to base implementation
        return await super().generate_embeddings(text, model)
    
    async def analyze_document(self, content: str, content_type: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """
        Perform advanced document analysis using optimal provider.
        
        Args:
            content: Document content
            content_type: Type of content
            analysis_type: Type of analysis to perform
            
        Returns:
            Analysis results
        """
        if not self.enable_ai or not hasattr(self, "ai_layer"):
            logger.warning("Document analysis requested but AI is not enabled")
            return {"error": "AI not enabled"}
        
        # Use enhanced AI layer for document analysis
        return await self.ai_layer.analyze_document(
            content=content,
            content_type=content_type,
            analysis_type=analysis_type
        )
    
    def get_available_providers(self) -> List[str]:
        """
        Get list of available providers.
        
        Returns:
            List of available provider names
        """
        if self.enable_ai and hasattr(self, "ai_layer"):
            return self.ai_layer.get_available_providers()
        return []
    
    def get_provider_health(self) -> Dict[str, Dict[str, Any]]:
        """
        Get health status of all providers.
        
        Returns:
            Dictionary of provider health status
        """
        if self.enable_ai and hasattr(self, "ai_layer"):
            return self.ai_layer.get_provider_health()
        return {} 

--- app/processors/README.md.txt ---
# Document Processors

This directory contains document processors for the ConTXT system. Each processor is specialized for handling a specific type of document or content.

## Available Processors

- **BaseProcessor**: Enhanced base class for all processors with common functionality
- **TextProcessor**: Handles plain text documents
- **MarkdownProcessor**: Processes Markdown documents with structure-aware chunking
- **JsonProcessor**: Processes JSON documents with schema extraction
- **CsvProcessor**: Processes CSV documents with header detection
- **ImageProcessor**: Processes images with OCR and object detection
- **PDFProcessor**: Processes PDF documents with text and structure extraction
- **HTMLProcessor**: Processes HTML documents with structure and link extraction
- **CodeProcessor**: Processes source code files with language-specific handling
- **PrivacyCompliantProcessor**: Wrapper processor for privacy compliance (PII redaction)

## Enhanced Capabilities

The document processors now support optional AI enhancements and Cognee integration:

- **AI Enhancements**: Use models like Grok-4 and GPT-4o to analyze and enrich document content
- **Cognee Integration**: Integrate with Cognee for advanced graph and vector database operations
- **Feature Flags**: Enable/disable AI enhancements and Cognee integration as needed
- **Database Adapters**: Support both direct database access and Cognee abstraction

## Factory

The `ProcessorFactory` class provides methods for creating the appropriate processor based on content type or file extension.

### Enhanced Factory Methods

- **get_processor_for_file**: Get a processor for a file based on extension
- **get_processor_for_content_type**: Get a processor for a content type
- **get_optimal_processor**: Get the optimal processor based on content analysis
- **get_special_processor**: Get a special processor (e.g., privacy)
- **get_enhanced_processor**: Get a processor with AI enhancements enabled

## Configuration

The `ProcessingConfig` class provides configuration for the document processors, supporting:

- Database connections (Neo4j, Qdrant)
- AI model configuration (xAI, OpenAI)
- Feature flags (enable_ai, use_cognee)
- Document processing options (chunk_size, chunk_overlap)

## Environment Variables

Set these environment variables to configure the system:

```
# Database Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-neo4j-password
VECTOR_DB_URL=https://your-qdrant.cloud.io
VECTOR_DB_KEY=your-qdrant-key

# AI Configuration
XAI_API_KEY=your-xai-api-key
OPENAI_API_KEY=your-openai-api-key
DEFAULT_AI_MODEL=openai

# Feature Flags
USE_COGNEE=false
ENABLE_AI=false
```

## Usage

### Standard Processing

```python
from app.processors import ProcessorFactory

# Create a processor based on file extension
processor = ProcessorFactory.get_processor_for_file("document.md")

# Process a document
with open("document.md", "r") as f:
    content = f.read()
result = await processor.process(content, {"file_name": "document.md"})

# Access the processed data
chunks = result["chunks"]
metadata = result["metadata"]
```

### Enhanced Processing

```python
from app.processors import ProcessorFactory

# Create an enhanced processor with AI capabilities
processor = ProcessorFactory.get_enhanced_processor(
    file_path="document.md",
    dataset_name="my_documents"
)

# Process with AI enhancements
with open("document.md", "r") as f:
    content = f.read()
result = await processor.process_with_enhancements(content, {
    "file_name": "document.md",
    "enhancement_type": "analysis"
})

# Access the processed and enhanced data
chunks = result["chunks"]
metadata = result["metadata"]
enhanced_content = result["enhanced_content"]
```

### Direct Factory Integration

```python
from app.processors.factory import ProcessorFactory

# Process a document with enhancements
result = await ProcessorFactory.get_enhanced_processor(
    file_path="document.json"
).process_with_enhancements(content)
```

## Testing

See the `test_processors.py` script in the root directory for examples of how to use each processor.

## Documentation

For more detailed documentation:

- See `DOCUMENT_PROCESSING.md` for detailed documentation on document processing
- See `PROCESSORS_SUMMARY.md` for a summary of available processors and their capabilities 

--- app/processors/requirements.txt.txt ---
# Base dependencies
pydantic>=2.0.0
httpx>=0.23.0

# Database clients
neo4j>=5.0.0
qdrant-client>=1.1.0

# Document processing
python-magic>=0.4.27
beautifulsoup4>=4.12.2
markdown>=3.4.3
pypdf>=3.15.0
Pillow>=10.0.0
pytesseract>=0.3.10
pandas>=2.0.0

# AI integration (optional)
langchain>=0.0.267
langchain-openai>=0.0.10
langchain-xai>=0.0.1

# Cognee integration (optional)
cognee[neo4j,qdrant]>=0.1.40

# Code processing
tree-sitter>=0.20.1
pygments>=2.15.0

# Natural Language Processing
nltk>=3.8.1
spacy>=3.6.0 

--- app/processors/text_processor.py.txt ---
"""
Text document processor.

This module provides a processor for plain text documents.
"""
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

from app.processors.base import BaseProcessor

logger = logging.getLogger(__name__)

class TextProcessor(BaseProcessor):
    """
    Processor for plain text documents.
    
    This class provides methods for processing plain text documents,
    extracting content, and storing it in the knowledge graph and vector database.
    """
    
    async def process(self, content: str, **kwargs) -> Dict[str, Any]:
        """
        Process a text document.
        
        Args:
            content: Text content to process
            **kwargs: Additional processing options
                - chunk_size: Size of text chunks (default: 1024)
                - chunk_overlap: Overlap between chunks (default: 128)
                - embedding_model: Model to use for embeddings (default: text-embedding-3-large)
                - document_id: Optional document ID
                - metadata: Optional metadata dictionary
        
        Returns:
            Processing result with document ID and metadata
        """
        # Extract options
        chunk_size = kwargs.get("chunk_size", 1024)
        chunk_overlap = kwargs.get("chunk_overlap", 128)
        embedding_model = kwargs.get("embedding_model", "text-embedding-3-large")
        document_id = kwargs.get("document_id", str(uuid.uuid4()))
        metadata = kwargs.get("metadata", {})
        
        # Merge with processor metadata
        metadata.update(self.metadata)
        metadata["content_type"] = "text/plain"
        metadata["processed_at"] = datetime.now().isoformat()
        metadata["id"] = document_id
        
        # Extract text if needed
        if not isinstance(content, str):
            content = await self.extract_text(content)
        
        # Split into chunks
        chunks = self._split_text(content, chunk_size, chunk_overlap)
        logger.info(f"Split text into {len(chunks)} chunks")
        
        # Process each chunk
        chunk_ids = []
        for i, chunk in enumerate(chunks):
            # Generate embedding
            embedding = await self.generate_embeddings(chunk, model=embedding_model)
            
            # Prepare chunk metadata
            chunk_metadata = metadata.copy()
            chunk_metadata["chunk_index"] = i
            chunk_metadata["chunk_total"] = len(chunks)
            chunk_metadata["chunk_id"] = f"{document_id}_chunk_{i}"
            chunk_metadata["text_snippet"] = chunk[:100] + "..." if len(chunk) > 100 else chunk
            
            # Store in vector database
            chunk_id = await self.store_in_vector_db(embedding, chunk_metadata)
            chunk_ids.append(chunk_id)
            
            # Store relationship in knowledge graph if it's not the first chunk
            if i > 0:
                await self._create_chunk_relationship(chunk_ids[i-1], chunk_id)
        
        # Store document in knowledge graph
        kg_id = await self.store_in_knowledge_graph({
            "id": document_id,
            "title": metadata.get("title", "Text Document"),
            "content_type": "text/plain",
            "chunk_count": len(chunks),
            "chunk_ids": chunk_ids
        })
        
        return {
            "document_id": document_id,
            "knowledge_graph_id": kg_id,
            "chunk_count": len(chunks),
            "chunk_ids": chunk_ids,
            "metadata": metadata
        }
    
    def _split_text(self, text: str, chunk_size: int, chunk_overlap: int) -> List[str]:
        """
        Split text into overlapping chunks.
        
        Args:
            text: Text to split
            chunk_size: Maximum chunk size
            chunk_overlap: Overlap between chunks
            
        Returns:
            List of text chunks
        """
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            # Find the end of the chunk
            end = start + chunk_size
            
            # Adjust end to avoid splitting words
            if end < len(text):
                # Try to find a natural break point
                for separator in ["\n\n", "\n", ". ", " "]:
                    pos = text.rfind(separator, start, end)
                    if pos > start:
                        end = pos + len(separator)
                        break
            
            # Add the chunk
            chunks.append(text[start:end])
            
            # Move to next chunk with overlap
            start = end - chunk_overlap
        
        return chunks
    
    async def _create_chunk_relationship(self, prev_chunk_id: str, next_chunk_id: str) -> None:
        """
        Create a relationship between consecutive chunks in the knowledge graph.
        
        Args:
            prev_chunk_id: ID of the previous chunk
            next_chunk_id: ID of the next chunk
        """
        query = """
        MATCH (prev:Chunk {id: $prev_id})
        MATCH (next:Chunk {id: $next_id})
        CREATE (prev)-[:NEXT]->(next)
        """
        
        params = {
            "prev_id": prev_chunk_id,
            "next_id": next_chunk_id
        }
        
        await self.neo4j_client.run_query(query, params) 

--- app/schemas/auth.py.txt ---
"""
Authentication schemas for ConTXT API.
"""
from pydantic import BaseModel, EmailStr, validator, Field, constr
from typing import Optional, List, Dict, Any
from datetime import datetime
import uuid
import re

class UserRegistration(BaseModel):
    """User registration request schema."""
    email: EmailStr
    password: str = Field(..., min_length=8)
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class UserLogin(BaseModel):
    """User login request schema."""
    email: EmailStr
    password: str

class TokenResponse(BaseModel):
    """JWT token response schema."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user_id: str

class RefreshTokenRequest(BaseModel):
    """Refresh token request schema."""
    refresh_token: str

class UserProfile(BaseModel):
    """User profile response schema."""
    id: str
    email: str
    first_name: Optional[str]
    last_name: Optional[str]
    full_name: Optional[str]
    is_active: bool
    is_verified: bool
    subscription_tier: str
    created_at: datetime
    updated_at: Optional[datetime]

class UserProfileUpdate(BaseModel):
    """User profile update request schema."""
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)

class PasswordChange(BaseModel):
    """Password change request schema."""
    current_password: str
    new_password: str = Field(..., min_length=8)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class PasswordReset(BaseModel):
    """Password reset request schema."""
    token: str
    new_password: str = Field(..., min_length=8)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class OTPVerification(BaseModel):
    """OTP verification request schema."""
    email: EmailStr
    otp_code: str = Field(..., min_length=6, max_length=6)

class ForgotPasswordRequest(BaseModel):
    """Forgot password request schema."""
    email: EmailStr

class ApiKeyCreate(BaseModel):
    """API key creation request schema."""
    key_name: str = Field(..., max_length=100)
    permissions: List[str] = Field(default=["read"])
    expires_at: Optional[datetime] = None

class ApiKeyResponse(BaseModel):
    """API key response schema."""
    id: str
    key_name: str
    api_key: str
    permissions: List[str]
    is_active: bool
    expires_at: Optional[datetime]
    created_at: datetime

class ApiKeyList(BaseModel):
    """API key list response schema."""
    api_keys: List[ApiKeyResponse]

class OTPRequest(BaseModel):
    """OTP request schema."""
    email: EmailStr

class OTPVerificationNew(BaseModel):
    """OTP verification request schema."""
    email: EmailStr
    otp_code: str

    @validator('otp_code')
    def validate_otp_code(cls, v):
        if not re.match(r'^\d{6}$', v):
            raise ValueError('OTP must be exactly 6 digits')
        return v

class UserRegistrationOTP(UserRegistration):
    """User registration with OTP request schema."""
    otp_code: str

    @validator('otp_code')
    def validate_otp_code(cls, v):
        if not re.match(r'^\d{6}$', v):
            raise ValueError('OTP must be exactly 6 digits')
        return v

class PasswordResetOTP(BaseModel):
    """Password reset with OTP request schema."""
    email: EmailStr
    otp_code: str
    new_password: str

    @validator('otp_code')
    def validate_otp_code(cls, v):
        if not re.match(r'^\d{6}$', v):
            raise ValueError('OTP must be exactly 6 digits')
        return v

class OTPResponse(BaseModel):
    """OTP response schema."""
    success: bool
    message: str
    expires_in: Optional[int] = None
    created_at: datetime

class UserStats(BaseModel):
    """User statistics schema."""
    total_contexts_created: int
    total_api_calls: int
    subscription_tier: str
    account_created: datetime
    last_login: Optional[datetime]

class AuthResponse(BaseModel):
    """Generic auth response schema."""
    message: str
    success: bool = True
    data: Optional[Dict[str, Any]] = None

class ErrorResponse(BaseModel):
    """Error response schema."""
    message: str
    success: bool = False
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


--- app/schemas/context.py.txt ---
"""
Pydantic models for context engineering operations.
"""
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, HttpUrl

class Source(BaseModel):
    """A source of information for context building."""
    source_id: Optional[str] = None
    source_type: str = Field(..., description="Type of source (url, file, text, etc.)")
    content: Optional[str] = None
    url: Optional[HttpUrl] = None
    metadata: Optional[Dict[str, Any]] = None

class ContextRequest(BaseModel):
    """Request model for building engineered context."""
    sources: List[Source] = Field(..., description="List of sources to process")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens for context window")
    compression_ratio: Optional[float] = Field(None, description="Target compression ratio")
    include_metadata: bool = Field(False, description="Include metadata in response")
    tool_type: Optional[str] = Field(None, description="Target tool type (cursor, windsurf, etc.)")

class ContextBlock(BaseModel):
    """A block of engineered context."""
    block_id: str
    block_type: str = Field(..., description="Type of context block (code, text, etc.)")
    content: str
    source_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    relevance_score: Optional[float] = None

class ContextResponse(BaseModel):
    """Response model for context building operations."""
    context_id: str
    blocks: List[ContextBlock]
    token_count: int
    compression_ratio: float
    metadata: Optional[Dict[str, Any]] = None

class SystemPromptRequest(BaseModel):
    """Request model for generating a system prompt."""
    context_id: str = Field(..., description="ID of the built context")
    tool_type: str = Field(..., description="Target tool type (cursor, windsurf, etc.)")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Additional parameters") 

--- app/schemas/ingestion.py.txt ---
"""
Pydantic models for data ingestion operations.
Includes support for AI enhancements and database integration options.
"""
from typing import Dict, List, Optional, Any, Union, Literal
from pydantic import BaseModel, Field, HttpUrl

class EnhancementOptions(BaseModel):
    """Enhancement options for document processing."""
    use_cognee: bool = Field(False, description="Whether to use Cognee for database operations")
    enable_ai: bool = Field(False, description="Whether to enable AI enhancements")
    enhancement_type: Optional[str] = Field(None, description="Type of enhancement to apply (analysis, summary, etc.)")
    dataset_name: Optional[str] = Field(None, description="Name of the dataset for Cognee integration")

class UrlIngestionRequest(BaseModel):
    """Request model for ingesting content from a URL."""
    url: HttpUrl = Field(..., description="URL to ingest content from")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    options: Optional[Dict[str, Any]] = Field(None, description="Ingestion options including AI enhancements")

class FileIngestionRequest(BaseModel):
    """Request model for ingesting content from a file."""
    file_name: str = Field(..., description="Name of the file")
    file_type: str = Field(..., description="Type of the file (pdf, txt, etc.)")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    options: Optional[Dict[str, Any]] = Field(None, description="Ingestion options including AI enhancements")

class TextIngestionRequest(BaseModel):
    """Request model for ingesting raw text content."""
    text: str = Field(..., description="Text content to ingest")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    options: Optional[Dict[str, Any]] = Field(None, description="Ingestion options including AI enhancements")

class PrivacyIngestionRequest(BaseModel):
    """Request model for ingesting content with privacy compliance."""
    content: Any = Field(..., description="Content to ingest (text, URL, or file content)")
    content_type: str = Field(..., description="Type of content (text/plain, application/json, etc.)")
    redact_pii: bool = Field(True, description="Whether to redact personally identifiable information")
    pii_types: Optional[List[str]] = Field(None, description="Types of PII to redact (email, phone, etc.)")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    options: Optional[Dict[str, Any]] = Field(None, description="Ingestion options including AI enhancements")

class EnhancementResult(BaseModel):
    """Result of document enhancement."""
    has_enhancements: bool = Field(..., description="Whether enhancements were applied")
    enhancement_type: Optional[str] = Field(None, description="Type of enhancement applied")
    insights: Optional[List[str]] = Field(None, description="Generated insights")

class IngestionResponse(BaseModel):
    """Response model for ingestion operations."""
    job_id: str = Field(..., description="ID of the ingestion job")
    status: str = Field(..., description="Status of the ingestion job")
    message: Optional[str] = None

class IngestionStatus(BaseModel):
    """Status model for ingestion jobs."""
    job_id: str
    status: str = Field(..., description="Status of the job (processing, completed, failed)")
    progress: Optional[float] = Field(None, description="Progress percentage (0-100)")
    message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    created_at: str
    updated_at: str

class EnhancementOptionsResponse(BaseModel):
    """Response model for available enhancement options."""
    cognee_available: bool = Field(..., description="Whether Cognee is available")
    ai_models: Dict[str, bool] = Field(..., description="Available AI models")
    enhancement_types: List[str] = Field(..., description="Available enhancement types") 

--- app/schemas/__init__.py.txt ---
from .user import User, UserCreate, UserUpdate
from .token import Token, TokenData


--- app/schemas/knowledge.py.txt ---
"""
Pydantic models for knowledge graph operations.
"""
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field

class GraphQueryRequest(BaseModel):
    """Request model for querying the knowledge graph."""
    query: str = Field(..., description="Query text or Cypher query")
    query_type: str = Field("natural", description="Query type: 'natural' or 'cypher'")
    limit: Optional[int] = Field(10, description="Maximum number of results")
    include_metadata: bool = Field(False, description="Include metadata in response")

class GraphQueryResult(BaseModel):
    """A single result from a knowledge graph query."""
    node_id: str
    node_type: str
    properties: Dict[str, Any]
    relationships: Optional[List[Dict[str, Any]]] = None

class GraphQueryResponse(BaseModel):
    """Response model for knowledge graph queries."""
    results: List[GraphQueryResult]
    count: int = Field(..., description="Number of results returned")
    metadata: Optional[Dict[str, Any]] = None

class EntityRequest(BaseModel):
    """Request model for adding an entity to the knowledge graph."""
    entity_type: str = Field(..., description="Type of entity (e.g., 'Person', 'Document')")
    properties: Dict[str, Any] = Field(..., description="Entity properties")
    
class RelationshipRequest(BaseModel):
    """Request model for adding a relationship to the knowledge graph."""
    source_id: str = Field(..., description="ID of the source entity")
    target_id: str = Field(..., description="ID of the target entity")
    relationship_type: str = Field(..., description="Type of relationship (e.g., 'KNOWS', 'CONTAINS')")
    properties: Optional[Dict[str, Any]] = Field({}, description="Relationship properties") 

--- app/schemas/token.py.txt ---
from pydantic import BaseModel
from typing import Optional

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class TokenData(BaseModel):
    user_id: Optional[str] = None


--- app/schemas/user.py.txt ---
from pydantic import BaseModel, EmailStr
from typing import Optional
import uuid
from datetime import datetime

# Shared properties
class UserBase(BaseModel):
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = True
    full_name: Optional[str] = None

# Properties to receive via API on creation
class UserCreate(UserBase):
    email: EmailStr
    password: str

# Properties to receive via API on update
class UserUpdate(UserBase):
    password: Optional[str] = None

class UserInDBBase(UserBase):
    id: uuid.UUID
    created_at: datetime

    class Config:
        orm_mode = True

# Additional properties to return via API
class User(UserInDBBase):
    pass

# Additional properties stored in DB
class UserInDB(UserInDBBase):
    hashed_password: str


