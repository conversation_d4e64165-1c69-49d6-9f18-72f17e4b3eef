--- CONTAINERS.md.txt ---
# AI Context Builder Containers

This document provides an overview of all containers in the AI Context Builder Docker environment, explaining their purpose, configuration, and how they interact with each other.

## Architecture Diagram

```mermaid
graph TD
    Client[Client] --> API[API Container<br>backend-api:8000]
    
    subgraph "Processing Layer"
        API --> Worker[Worker Container<br>backend-worker]
        API --> Flower[Flower Container<br>backend-flower:5555]
        Worker --> Flower
    end
    
    subgraph "Data Layer"
        API --> PostgreSQL[PostgreSQL<br>postgres:15:5432]
        API --> Redis[Redis<br>redis:7:6379]
        API --> Neo4j[Neo4j<br>neo4j:5:7474/7687]
        API --> Qdrant[Qdrant<br>qdrant:latest:6333/6334]
        
        Worker --> PostgreSQL
        Worker --> Redis
        Worker --> Neo4j
        Worker --> Qdrant
        
        Flower --> Redis
    end
    
    Redis --> Worker
    
    classDef primary fill:#4CAF50,stroke:#388E3C,color:white;
    classDef secondary fill:#2196F3,stroke:#1976D2,color:white;
    classDef database fill:#FF9800,stroke:#F57C00,color:white;
    
    class API,Worker primary;
    class Flower secondary;
    class PostgreSQL,Redis,Neo4j,Qdrant database;
```

## Container Overview

The AI Context Builder backend consists of 7 containers that work together to provide a complete environment for building AI context and generating system prompts:

| Container | Image | Purpose | Ports |
|-----------|-------|---------|-------|
| api-1 | backend-api | FastAPI backend serving REST API endpoints | 8000 |
| worker-1 | backend-worker | Celery worker for processing background tasks | - |
| flower-1 | backend-flower | Celery monitoring interface | 5555 |
| postgres-1 | postgres:15 | Relational database for structured data | 5432 |
| redis-1 | redis:7 | Cache and message broker | 6379 |
| neo4j-1 | neo4j:5 | Graph database for knowledge representation | 7474, 7687 |
| qdrant-1 | qdrant/qdrant:latest | Vector database for embeddings | 6333, 6334 |

## Detailed Container Descriptions

### API Container (backend-api)

**Purpose**: Serves as the main entry point for the application, providing REST API endpoints for client interactions.

**Key Features**:
- Built with FastAPI for high-performance async API endpoints
- Provides endpoints for file upload, context building, and configuration generation
- Integrates with all other services (databases, worker, etc.)
- Implements CORS for frontend integration

**Configuration**:
- Built from custom Dockerfile in the project
- Uses uvicorn as the ASGI server
- Hot-reloading enabled for development
- Environment variables loaded from .env file

**Access**:
- API documentation: http://localhost:8000/docs
- Health check: http://localhost:8000/

### Worker Container (backend-worker)

**Purpose**: Processes background tasks asynchronously to prevent blocking the API.

**Key Features**:
- Runs Celery for task queue processing
- Handles long-running tasks like document processing, embedding generation, etc.
- Communicates with Redis for task queue management
- Accesses all databases for data processing and storage

**Configuration**:
- Built from the same Dockerfile as the API
- Configured to use Redis as broker and result backend
- Shares the same codebase as the API container

### Flower Container (backend-flower)

**Purpose**: Provides a web interface for monitoring and managing Celery tasks.

**Key Features**:
- Real-time monitoring of task execution
- Task history and statistics
- Ability to inspect and manage tasks

**Configuration**:
- Built from the same Dockerfile as the API
- Connects to Redis to monitor Celery tasks
- Provides a web interface on port 5555

**Access**:
- Web interface: http://localhost:5555

### PostgreSQL Container (postgres-1)

**Purpose**: Stores structured relational data for the application.

**Key Features**:
- Reliable relational database
- Stores user data, job metadata, and other structured information
- Persistent storage through Docker volumes

**Configuration**:
- Uses official PostgreSQL 15 image
- Database name: context_builder
- Credentials configured in .env file
- Data persisted in postgres_data volume

**Access**:
- Connection string: postgresql://postgres:postgres@localhost:5432/context_builder

### Redis Container (redis-1)

**Purpose**: Serves as both a cache and message broker for Celery.

**Key Features**:
- High-performance in-memory data store
- Used as Celery's message broker for task queue
- Also used for caching frequently accessed data
- Supports pub/sub for real-time updates

**Configuration**:
- Uses official Redis 7 image
- No authentication in development setup
- Data persisted in redis_data volume

**Access**:
- Connection string: redis://localhost:6379/0

### Neo4j Container (neo4j-1)

**Purpose**: Stores and queries graph-based knowledge representations.

**Key Features**:
- Graph database for semantic context
- Stores entities, relationships, and properties extracted from documents
- Enables complex graph queries for knowledge retrieval
- Provides a browser interface for visualization

**Configuration**:
- Uses official Neo4j 5 image
- Authentication: neo4j/password
- Memory configuration optimized for development
- Anonymous usage reporting disabled
- Data persisted in neo4j_data volume

**Access**:
- Browser interface: http://localhost:7474
- Bolt connection: bolt://localhost:7687
- Credentials: neo4j/password

### Qdrant Container (qdrant-1)

**Purpose**: Vector database for storing and querying embeddings.

**Key Features**:
- High-performance vector similarity search
- Stores embeddings generated from text and other content
- Enables semantic search capabilities
- Provides REST API and gRPC interfaces

**Configuration**:
- Uses official Qdrant latest image
- No authentication in development setup
- Data persisted in qdrant_data volume

**Access**:
- REST API: http://localhost:6333
- Web dashboard: http://localhost:6333/dashboard
- gRPC interface: localhost:6334

## Container Interactions

The containers interact in the following ways:

1. **API Container**:
   - Receives requests from clients
   - Communicates with all databases for data operations
   - Sends tasks to Worker via Redis
   - Serves responses back to clients

2. **Worker Container**:
   - Receives tasks from Redis queue
   - Processes tasks asynchronously
   - Stores results in databases
   - Updates task status in Redis

3. **Flower Container**:
   - Monitors Redis for task information
   - Provides visualization of task execution

4. **Database Containers** (PostgreSQL, Neo4j, Qdrant):
   - Store different types of data based on their specialization
   - Respond to queries from API and Worker containers

5. **Redis Container**:
   - Acts as message broker between API and Worker
   - Stores task status and results
   - Provides caching for frequently accessed data

## Development Workflow

When developing with these containers:

1. Use `docker compose up` to start all services
2. Make changes to the code and the API container will automatically reload
3. Access API documentation at http://localhost:8000/docs
4. Monitor tasks with Flower at http://localhost:5555
5. Explore the knowledge graph with Neo4j Browser at http://localhost:7474
6. Visualize vector embeddings with Qdrant Dashboard at http://localhost:6333/dashboard

## Troubleshooting

Common issues and solutions:

- **API container fails to start**: Check .env file for proper configuration, especially CORS_ORIGINS format
- **Worker not processing tasks**: Verify Redis connection and Celery configuration
- **Database connection errors**: Ensure the database containers are running and credentials are correct
- **Permission issues with volumes**: Check Docker volume permissions on the host

## Production Considerations

For production deployment:

- Enable proper authentication for all services
- Configure SSL/TLS for secure communication
- Implement proper backup strategies for all databases
- Consider using managed services for databases in cloud environments
- Optimize container resources based on workload requirements
- Implement monitoring and alerting for all services 

--- current-work.md.txt ---
# Complete ConTXT SAAS Endpoint Inventory & Roadmap

Based on our previous discussion, here's a **comprehensive breakdown** of all endpoints - what you currently have implemented and what you need to build for a production-ready SAAS. Each endpoint includes its purpose and security implications.

## **✅ Current Implemented Endpoints**

### **Core Application**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/` | GET | Health check and API status | Public |
| `/ws/{user_id}` | WebSocket | Real-time communication with token auth | User Auth |

### **Context Engineering** (Prefix: `/context`)
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/context/build` | POST | Build engineered context from sources | User Auth |
| `/context/generate-system-prompt` | POST | Generate AI tool prompts (.cursorrules, Windsurf) | User Auth |
| `/context/status/{context_id}` | GET | Check context building operation status | User Auth |

### **Data Ingestion** (Prefix: `/ingestion`)
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/ingestion/url` | POST | Ingest content from URLs | User Auth |
| `/ingestion/file` | POST | Upload and process documents (PDF, DOCX, etc.) | User Auth |
| `/ingestion/text` | POST | Process raw text content | User Auth |
| `/ingestion/privacy` | POST | Ingest content with PII redaction | User Auth |
| `/ingestion/status/{job_id}` | GET | Check ingestion job status | User Auth |
| `/ingestion/enhancement-options` | GET | Available AI enhancement options | User Auth |

### **Knowledge Graph** (Prefix: `/knowledge`)
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/knowledge/query` | POST | Query knowledge graph with natural language | User Auth |
| `/knowledge/entity` | POST | Add new entity nodes to graph | User Auth |
| `/knowledge/relationship` | POST | Create relationships between entities | User Auth |
| `/knowledge/stats` | GET | Basic graph statistics | User Auth |
| `/knowledge/graph/nodes` | GET | Get all nodes with metadata (paginated) | User Auth |
| `/knowledge/graph/relationships` | GET | Get all relationships (paginated) | User Auth |
| `/knowledge/graph/full` | GET | Complete graph data for visualization | User Auth |
| `/knowledge/graph/node/{node_id}/neighbors` | GET | Get node neighbors with depth control | User Auth |
| `/knowledge/analytics/stats` | GET | Advanced graph analytics | User Auth |

## **🔧 Critical Missing Endpoints (High Priority)**

### **Authentication & User Management**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/auth/register` | POST | User registration with email verification | Public |
| `/auth/login` | POST | User login with JWT token generation | Public |
| `/auth/forgot-password` | POST | Password reset request | Public |
| `/auth/reset-password` | POST | Password reset confirmation | Public |
| `/auth/verify-email` | POST | Email verification | Public |
| `/auth/refresh-token` | POST | Refresh JWT tokens | Public |
| `/user/profile` | GET/PUT | Get/update user profile | User Auth |
| `/user/change-password` | POST | Change user password | User Auth |
| `/user/api-keys` | GET/POST/DELETE | Manage user API keys | User Auth |

### **Subscription & Billing** (SAAS Core)
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/user/subscription` | GET | Current subscription details | User Auth |
| `/user/upgrade` | POST | Upgrade subscription tier | User Auth |
| `/user/usage` | GET | Usage statistics and limits | User Auth |
| `/user/invoices` | GET | Billing history | User Auth |
| `/user/payment-method` | POST/PUT | Update payment method | User Auth |
| `/webhooks/stripe` | POST | Stripe payment webhooks | Internal |

### **Project & Workspace Management**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/projects` | GET/POST | List/create user projects | User Auth |
| `/projects/{project_id}` | GET/PUT/DELETE | Manage individual projects | User Auth |
| `/projects/{project_id}/documents` | GET/POST | Project document management | User Auth |
| `/projects/{project_id}/duplicate` | POST | Duplicate existing project | User Auth |
| `/documents/{doc_id}` | DELETE | Delete specific document | User Auth |
| `/documents/{doc_id}/reprocess` | POST | Reprocess document with new settings | User Auth |

## **🤖 AI Rules Generation (Core SAAS Feature)**

### **Rule Generation Endpoints**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/rules/generate-cursorrules` | POST | Generate .cursorrules file for Cursor IDE | User Auth |
| `/rules/generate-windsurf` | POST | Generate Windsurf configuration | User Auth |
| `/rules/generate-gemini-cli` | POST | Generate Gemini CLI setup | User Auth |
| `/rules/templates` | GET | List available rule templates | User Auth |
| `/rules/custom-template` | POST | Create custom rule template | User Auth |

### **Conversation Importers** (Key Differentiator)
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/import/chatgpt` | POST | Import ChatGPT conversation history | User Auth |
| `/import/claude` | POST | Import Claude conversation history | User Auth |
| `/import/gemini` | POST | Import Gemini conversation history | User Auth |
| `/import/status/{job_id}` | GET | Check import job status | User Auth |
| `/import/history` | GET | List import history | User Auth |

## **🧠 Advanced AI & Graph Intelligence**

### **LLM Agent Management**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/agents/create` | POST | Create custom AI agent | Premium User |
| `/agents` | GET | List user's AI agents | Premium User |
| `/agents/{agent_id}` | PUT/DELETE | Update/delete AI agent | Premium User |
| `/agents/{agent_id}/execute` | POST | Execute agent task | Premium User |
| `/agents/{agent_id}/history` | GET | Agent execution history | Premium User |
| `/agents/{agent_id}/train` | POST | Train agent on project data | Premium User |
| `/agents/workflows` | POST | Create multi-agent workflow | Premium User |

### **Intelligent Search & Query**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/graph/query/natural` | POST | Natural language graph queries | User Auth |
| `/graph/query/semantic` | POST | Semantic similarity search | User Auth |
| `/graph/query/path-finding` | POST | Find relationships between entities | User Auth |
| `/search/intelligent` | POST | AI-powered search across all data | User Auth |
| `/search/contextual` | POST | Context-aware document search | User Auth |
| `/search/code` | POST | Semantic code search | User Auth |
| `/search/conversations` | POST | Search imported AI conversations | User Auth |
| `/search/hybrid` | POST | Vector + keyword hybrid search | User Auth |

### **Real-Time WebSocket Connections**
| Endpoint | Type | Purpose | Security Level |
|----------|------|---------|----------------|
| `/ws/graph/{project_id}` | WebSocket | Live graph changes | User Auth |
| `/ws/search/{user_id}` | WebSocket | Real-time search results | User Auth |
| `/ws/agents/{agent_id}` | WebSocket | Agent execution updates | Premium User |
| `/ws/processing/{job_id}` | WebSocket | Document processing status | User Auth |
| `/ws/collaboration/{project}` | WebSocket | Team collaboration updates | Team Auth |

## **⚙️ Configuration & Model Management**

### **User-Level Configuration**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/user/models/available` | GET | List available models per provider | User Auth |
| `/user/models/preferences` | GET/POST | Get/set user's preferred models | User Auth |
| `/user/providers/status` | GET | Check provider availability | User Auth |
| `/user/providers/api-keys` | PUT | Update user's API keys (encrypted) | User Auth |
| `/user/providers/limits` | GET | Current usage limits per provider | User Auth |
| `/user/providers/costs` | GET | Real-time cost comparison | User Auth |

### **Routing & Optimization**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/config/routing/cost-limits` | GET/POST | Get/set per-user cost limits | User Auth |
| `/config/routing/optimization` | PUT | Update cost optimization rules | User Auth |
| `/config/routing/free-tier-max` | PUT | Maximize free tier usage | User Auth |
| `/config/providers/routing` | GET/POST | Configure intelligent routing rules | User Auth |
| `/config/providers/fallback` | PUT | Update fallback mechanisms | User Auth |

## **👨‍💼 Admin & Team Management**

### **Organization Administration**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/admin/team/members` | GET | List team members | Team Admin |
| `/admin/team/invite` | POST | Invite team member | Team Admin |
| `/admin/team/members/{user_id}` | DELETE | Remove team member | Team Admin |
| `/admin/organization` | GET/PUT | Get/update org settings | Team Admin |
| `/admin/analytics/usage` | GET | Team usage analytics | Team Admin |
| `/admin/projects` | GET | List all org projects | Team Admin |
| `/admin/models/team-usage` | GET | Team model usage analytics | Team Admin |

### **Super Admin Platform Management**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/superadmin/users` | GET | List all platform users | Super Admin |
| `/superadmin/users/{id}/status` | PUT | Enable/disable users | Super Admin |
| `/superadmin/subscriptions` | GET | All subscriptions | Super Admin |
| `/superadmin/config/system` | GET/PUT | System configuration | Super Admin |
| `/superadmin/analytics/platform` | GET | Platform-wide analytics | Super Admin |
| `/superadmin/providers/register` | POST | Register new provider | Super Admin |

## **🔒 System & Internal Operations**

### **Health & Monitoring**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/health` | GET | Basic health check | Public |
| `/health/detailed` | GET | Detailed system status | Internal |
| `/metrics` | GET | Prometheus metrics | Internal |
| `/version` | GET | API version info | Public |

### **Internal Operations**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/internal/jobs` | GET | List active background jobs | Internal |
| `/internal/jobs/{id}/retry` | POST | Retry failed job | Internal |
| `/internal/db/backup` | POST | Trigger database backup | Internal |
| `/internal/db/stats` | GET | Database statistics | Internal |

## **💰 Cost-Effective Production Strategy**

### **Free Tier Maximization**
| Endpoint | Method | Purpose | Security Level |
|----------|--------|---------|----------------|
| `/config/free-tier/status` | GET | Free tier usage status | User Auth |
| `/config/free-tier/optimize` | POST | Optimize for free tier usage | User Auth |
| `/config/local/enable` | POST | Enable local model processing | User Auth |
| `/config/local/status` | GET | Local processing status | User Auth |

## **🎯 Implementation Priority Matrix**

### **Phase 1: Foundation (Weeks 1-2)**
- **Authentication system** - User registration, login, JWT tokens
- **Subscription management** - Stripe integration, usage tracking
- **Basic project management** - CRUD operations for projects
- **AI rules generation** - Core SAAS differentiator

### **Phase 2: Intelligence (Weeks 3-4)**
- **Conversation importers** - ChatGPT, Claude, Gemini integration
- **Advanced search** - Semantic and hybrid search
- **Configuration management** - Model and provider configuration
- **Real-time features** - WebSocket implementations

### **Phase 3: Scale & Security (Weeks 5-6)**
- **Admin panels** - Team and organization management
- **Advanced analytics** - Usage and performance monitoring
- **Security hardening** - Rate limiting, audit trails
- **Production monitoring** - Health checks, metrics

## **🔐 Security Implementation Notes**

**Authorization Levels:**
- **Public**: No authentication required
- **User Auth**: Valid JWT token required
- **Premium User**: Paid subscription required
- **Team Admin**: Organization admin role required
- **Super Admin**: Platform administrator access
- **Internal**: Service-to-service communication only

**Rate Limiting Strategy:**
- **Free tier**: 100 requests/hour
- **Paid tier**: 1,000 requests/hour
- **Premium tier**: 5,000 requests/hour
- **Enterprise**: 10,000 requests/hour

This **comprehensive endpoint architecture** provides your ConTXT SAAS with enterprise-grade functionality while maintaining **cost-effective production deployment** through intelligent provider routing and free-tier maximization strategies.

--- .env.txt ---
# =============================================================================
# API Keys - Multi-Provider Support (Required to enable respective provider)
# =============================================================================

# Text-to-Text Providers
XAI_API_KEY=************************************************************************************
OPENAI_API_KEY="your_openai_api_key_here"
COHERE_API_KEY="843iNFI4C7xM2DMLjuCss9EYsK1To4MTuoTOjJsf"
ANTHROPIC_API_KEY="your_anthropic_api_key_here"
OPENROUTER_API_KEY="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"
HUGGINGFACE_API_KEY="*************************************"
OLLAMA_API_KEY=""  # Optional for Ollama

# Additional API Keys (for other integrations)
PERPLEXITY_API_KEY="your_perplexity_api_key_here"
GOOGLE_API_KEY="your_google_api_key_here"
MISTRAL_API_KEY="your_mistral_key_here"
AZURE_OPENAI_API_KEY="your_azure_key_here"
GITHUB_API_KEY="your_github_api_key_here"

# =============================================================================
# Provider Selection Configuration
# =============================================================================

# Manual Provider Selection (optional - overrides auto-detection)
MANUAL_TEXT_PROVIDER=xai
MANUAL_EMBEDDING_PROVIDER=openai

# Auto-detected providers will be used if manual selection not set
# Priority order: xai, openai, cohere, anthropic, openrouter, huggingface, ollama

# =============================================================================
# Ollama Configuration (Endpoint Required, API Key Optional)
# =============================================================================
OLLAMA_ENDPOINT="http://localhost:11434"  # Required
OLLAMA_HOST="localhost:11434"  # Alternative to OLLAMA_ENDPOINT
OLLAMA_TEXT_MODEL="llama3.1"
OLLAMA_EMBEDDING_MODEL="nomic-embed-text"

# For Docker environments (uncomment if using Docker)
# OLLAMA_ENDPOINT="http://ollama:11434"

# =============================================================================
# Cognee Configuration (Updated for Multi-Provider)
# =============================================================================

# LLM Configuration (will use selected text provider)
LLM_API_KEY=${XAI_API_KEY}  # Default to XAI key
LLM_PROVIDER="openai"  # xAI uses OpenAI-compatible API
LLM_MODEL="grok-beta"  # or "grok-4" based on your preference

# =============================================================================
# Email Service API Keys
# =============================================================================
RESEND_API_KEY=re_d6hJvXS3_8oNvYzbdz8vs3eSPCy8vUzH3

LLM_ENDPOINT="https://api.x.ai/v1"

# Embedding Configuration (separate from LLM - xAI doesn't have embeddings)
EMBEDDING_PROVIDER="openai"  # Required: xAI doesn't provide embeddings
EMBEDDING_MODEL="text-embedding-3-large"
EMBEDDING_API_KEY="your_openai_api_key_here"  # Required for embeddings

# Processing Configuration
CHUNK_SIZE=1024
CHUNK_OVERLAP=128
USE_COGNEE=true
ENABLE_AI=true

# =============================================================================
# Graph Database Configuration (Neo4j)
# =============================================================================
GRAPH_DATABASE_PROVIDER=neo4j
NEO4J_URI=bolt://neo4j:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password

# Local development override (uncomment for local development)
# NEO4J_URI=bolt://localhost:7687

# =============================================================================
# Vector Database Configuration (Qdrant)
# =============================================================================
VECTOR_DB_PROVIDER=qdrant
VECTOR_DB_URL=http://qdrant:6333
VECTOR_DB_KEY=""  # Empty for local development

# Local development override (uncomment for local development)
# VECTOR_DB_URL=http://localhost:6333

# =============================================================================
# Relational Database Configuration (PostgreSQL)
# =============================================================================
DB_PROVIDER=postgres
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_NAME=document_processor
DB_SSL_MODE=disable

# Local development override (uncomment for local development)
# DB_HOST=localhost
# DB_USERNAME=cognee
# DB_PASSWORD=cognee
# DB_NAME=cognee_db

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=""

# Local development override (uncomment for local development)
# REDIS_URL=redis://localhost:6379/0

# =============================================================================
# FastAPI Application Configuration
# =============================================================================
DEBUG=true
LOG_LEVEL=info
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://localhost:5173"]

# =============================================================================
# File Processing Configuration
# =============================================================================
MAX_FILE_SIZE=104857600  # 100MB in bytes
UPLOAD_PATH=/app/uploads
PROCESSED_PATH=/app/processed
MAX_CONCURRENT_UPLOADS=10
SUPPORTED_FILE_TYPES=["json", "csv", "txt", "md", "pdf", "png", "jpg", "jpeg"]

# =============================================================================
# Celery Configuration
# =============================================================================
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=UTC
CELERY_ENABLE_UTC=true

# =============================================================================
# Security Configuration
# =============================================================================
SECRET_KEY=UC38hxSfHk81WSvF0HGtkM2GY02lz6qeQN4wvtATJI8
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# =============================================================================
# Monitoring and Logging
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_FORMAT=json
LOG_FILE=/app/logs/app.log

# =============================================================================
# Provider Model Configurations (Optional Overrides)
# =============================================================================

# Text-to-Text Model Overrides
XAI_MODEL="grok-beta"
OPENAI_MODEL="gpt-4-turbo-preview"
COHERE_MODEL="command-r-plus"
ANTHROPIC_MODEL="claude-3-5-sonnet-20241022"
OPENROUTER_MODEL="meta-llama/llama-3.1-8b-instruct:free"
HUGGINGFACE_MODEL="microsoft/DialoGPT-medium"
OLLAMA_MODEL="llama3.1"

# Embedding Model Overrides
OPENAI_EMBEDDING_MODEL="text-embedding-3-large"
OPENROUTER_EMBEDDING_MODEL="text-embedding-3-large"
HUGGINGFACE_EMBEDDING_MODEL="sentence-transformers/all-MiniLM-L6-v2"
OLLAMA_EMBEDDING_MODEL="nomic-embed-text"

# =============================================================================
# Provider Endpoint Overrides (Optional)
# =============================================================================
XAI_ENDPOINT="https://api.x.ai/v1"
OPENAI_ENDPOINT="https://api.openai.com/v1"
COHERE_ENDPOINT="https://api.cohere.ai/v1"
ANTHROPIC_ENDPOINT="https://api.anthropic.com/v1"
OPENROUTER_ENDPOINT="https://openrouter.ai/api/v1"
HUGGINGFACE_ENDPOINT="https://api-inference.huggingface.co/models"

# =============================================================================
# Production Overrides (uncomment for production)
# =============================================================================
# DEBUG=false
# ENVIRONMENT=production
# LOG_LEVEL=warning
# NEO4J_URI=neo4j+s://your-production-instance.databases.neo4j.io:7687
# VECTOR_DB_URL=https://your-cluster.cloud.qdrant.io:6333
# VECTOR_DB_KEY=your-production-qdrant-key
# DB_HOST=your-production-db-host
# DB_SSL_MODE=require

# =============================================================================
# Development Features (uncomment for specific testing)
# =============================================================================
# ENABLE_DEBUG_LOGGING=true
# DISABLE_AUTHENTICATION=true
# MOCK_EXTERNAL_APIS=false
# ENABLE_PROFILING=false


# ====
# Resend for sending OTP
# ====

RESEND_API_KEY=re_d6hJvXS3_8oNvYzbdz8vs3eSPCy8vUzH3

--- __init__.py.txt ---
"""
AI Context Engineering Agent Backend.
"""

__version__ = "0.1.0" 

--- PROCESSORS_MERGED.md.txt ---
# Document Processors Merger

This document explains the merger of the two document processing systems in the ConTXT project:
- The original `/app/processors/` system 
- The experimental `/app/doc_process/` system

## Overview

The ConTXT project originally contained two separate document processing systems:

1. **Original Processors System** (`/app/processors/`): A comprehensive system with processors for various file types, integrated with the ingestion system. This system had direct database integrations and a robust base architecture.

2. **Experimental Doc Process System** (`/app/doc_process/`): An experimental system focused on enhanced document processing capabilities with AI integration and the Cognee abstraction layer. This system offered advanced features but was not fully integrated into the main application.

## Merger Approach

The merger combined the best aspects of both systems:

1. **Enhanced Base Architecture**:
   - Kept the robust structure from the original processors
   - Integrated AI enhancement capabilities from the experimental system
   - Created a database adapter to support both direct database access and Cognee abstraction

2. **Feature-Flag-Driven Design**:
   - Made AI enhancements optional through `enable_ai` flag
   - Made Cognee integration optional through `use_cognee` flag
   - Ensured backward compatibility with existing code

3. **Comprehensive Configuration**:
   - Created a unified configuration system
   - Added support for multiple AI model providers
   - Implemented feature flags through environment variables

4. **Updated Factory Pattern**:
   - Enhanced the processor factory to support AI and Cognee options
   - Added convenience methods for enhanced processing
   - Maintained backward compatibility with existing code

## Merged Components

### 1. Base Processor

- **Enhanced BaseProcessor**: Combined features from both base implementations
- **DatabaseAdapter**: Added support for both direct database access and Cognee abstraction
- **AIEnhancementLayer**: Added optional AI enhancement capabilities

### 2. Factory

- **Updated ProcessorFactory**: Enhanced with options for AI and Cognee
- **New Factory Methods**: Added methods for enhanced processing and configuration

### 3. Ingestion Integration

- **Updated IngestionManager**: Enhanced with support for AI and Cognee
- **Updated Endpoints**: Added options for AI enhancements and Cognee integration

### 4. Configuration

- **ProcessingConfig**: Added unified configuration system
- **Environment Variables**: Added support for feature flags and configuration options

## Usage Examples

### Standard Processing (Backward Compatible)

```python
from app.processors import ProcessorFactory

# Create a processor based on file extension (works as before)
processor = ProcessorFactory.get_processor_for_file("document.md")

# Process a document
with open("document.md", "r") as f:
    content = f.read()
result = await processor.process(content, {"file_name": "document.md"})
```

### Enhanced Processing (New Capabilities)

```python
from app.processors import ProcessorFactory

# Create an enhanced processor with AI capabilities
processor = ProcessorFactory.get_enhanced_processor(
    file_path="document.md",
    dataset_name="my_documents"
)

# Process with AI enhancements
result = await processor.process_with_enhancements(content)
```

## Configuration Options

### Environment Variables

```
# Feature Flags
USE_COGNEE=false
ENABLE_AI=false

# AI Configuration
XAI_API_KEY=your-xai-api-key
OPENAI_API_KEY=your-openai-api-key
DEFAULT_AI_MODEL=openai
```

### API Options

The API endpoints now support options for AI enhancements:

```json
{
  "url": "https://example.com/document.pdf",
  "metadata": {
    "source": "web"
  },
  "options": {
    "use_cognee": true,
    "enable_ai": true,
    "enhancement_type": "analysis",
    "dataset_name": "web_documents"
  }
}
```

## Benefits of the Merger

1. **Enhanced Capabilities**: AI-powered document processing with entity extraction, insight generation, and more
2. **Optional Features**: Use advanced features only when needed
3. **Compatibility**: Maintains backward compatibility with existing code
4. **Flexibility**: Support for both direct database access and Cognee abstraction
5. **Unified Architecture**: Single, cohesive document processing system

## Future Improvements

1. **Additional AI Providers**: Add support for more AI providers (Claude, Gemini, etc.)
2. **Enhanced Workflows**: Integrate LangGraph workflows for complex processing pipelines
3. **More File Types**: Add support for additional file types and formats
4. **Performance Optimization**: Optimize processing for large documents and high throughput 

--- README.md.txt ---
# ConTXT - AI Context Engineering Agent Backend

The backend component of the ConTXT application, providing APIs for document processing, knowledge extraction, and context engineering.

## Features

- Document processing for various file types (text, markdown, JSON, CSV, PDF, HTML, images, code)
- Knowledge graph integration with Neo4j
- Vector search with Qdrant
- Cognee integration for advanced data operations
- Multi-provider LLM integration (OpenAI, Anthropic, Google, XAI/Grok, and more)
- Container-based deployment with Docker
- Asynchronous processing with Celery

## Docker Setup

This project is fully containerized using Docker for easy deployment and development. The Docker configuration includes:

- FastAPI application container
- Neo4j graph database
- Qdrant vector database
- PostgreSQL relational database
- Redis cache
- Celery worker for background processing (optional)
- Flower for monitoring Celery tasks (optional)

### Prerequisites

- Docker and Docker Compose installed
- API keys for LLM services (xAI, OpenAI, etc.)

### Getting Started

1. Create a `.env` file in the Backend directory with your configuration (see `.env.example` for reference):

```bash
# Minimal example .env file
XAI_API_KEY=your-xai-api-key-here
OPENAI_API_KEY=your-openai-api-key-here
```

2. Start the Docker containers:

```bash
# Navigate to the Backend directory
cd Backend

# Start with helper script
./scripts/docker_start.sh

# Or manually with docker-compose
docker-compose up -d
```

3. The following services will be available:

- **API Documentation**: http://localhost:8000/docs
- **Neo4j Browser**: http://localhost:7474 (neo4j/password)
- **Qdrant Dashboard**: http://localhost:6333/dashboard
- **Celery Flower** (if enabled): http://localhost:5555

### Testing the System

A comprehensive test script is provided to verify your setup:

```bash
# Install requests library if not present
pip install requests

# Run the test script
cd Backend
./scripts/test_docker_system.py
```

The test script will:
1. Check if the API is responding
2. Test database connections
3. Create test files
4. Upload files to the system
5. Wait for processing
6. Query the processed data

### Development Workflow

For development with hot-reloading:

```bash
# Start only databases
docker-compose up neo4j qdrant postgres redis

# Run the app locally
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Stopping the System

To stop the system:

```bash
# Use the helper script
./scripts/docker_stop.sh

# Or manually
docker-compose down
```

To stop and remove all data volumes:

```bash
docker-compose down -v
```

## API Endpoints

### Ingestion API

- `POST /api/ingestion/upload`: Upload a document
- `POST /api/ingestion/url`: Process a URL
- `GET /api/ingestion/{job_id}`: Get status of ingestion job

### Context API

- `POST /api/context/build`: Build context from sources
- `GET /api/context/{context_id}`: Get context by ID
- `DELETE /api/context/{context_id}`: Delete context

### Knowledge API

- `POST /api/knowledge/search`: Search knowledge graph
- `POST /api/knowledge/query`: Execute knowledge graph query

## Development

### Project Structure

```
Backend/
├── app/
│   ├── api/              # API endpoints
│   ├── config/           # Configuration
│   ├── core/             # Core business logic
│   ├── db/               # Database clients
│   ├── models/           # Data models
│   ├── processors/       # Document processors
│   ├── schemas/          # Pydantic schemas
│   └── utils/            # Utilities
├── scripts/              # Helper scripts
├── docker-compose.yml    # Docker Compose configuration
├── Dockerfile            # Docker configuration
└── requirements.txt      # Python dependencies
```

### Running Tests

```bash
# Run all tests
pytest

# Run specific test
pytest tests/test_ingestion.py
```

## Troubleshooting

### Common Issues

1. **Services not starting**: Check Docker logs and ensure all environment variables are set
   ```bash
   docker-compose logs app
   ```

2. **Database connection errors**: Verify database containers are running and accessible
   ```bash
   docker-compose ps
   python -m Backend.test_connections
   ```

3. **File upload failures**: Check file permissions and upload directory exists
   ```bash
   docker-compose exec app ls -la /app/uploads
   ```

4. **Memory issues**: Increase Docker memory limits or adjust database configurations in docker-compose.yml 

## Multi-Provider LLM Integration

The backend includes a comprehensive multi-provider LLM integration system that provides:

- Unified interface for multiple LLM providers (OpenRouter, OpenAI, Anthropic, Google, etc.)
- Intelligent provider selection based on task type, priority, and user tier
- Automatic fallback mechanisms for provider reliability
- Cost optimization strategies for efficient resource usage

For detailed information, see [Multi-Provider LLM Documentation](docs/MULTI_PROVIDER_LLM.md).

## Environment Variables

### AI Integration

```
# OpenRouter (Primary Gateway)
OPENROUTER_API_KEY=your_openrouter_key

# OpenAI
OPENAI_API_KEY=your_openai_key

# Anthropic Claude
ANTHROPIC_API_KEY=your_anthropic_key

# Google AI (Gemini)
GOOGLE_API_KEY=your_google_key

# XAI (Grok)
XAI_API_KEY=your_xai_key

# Optional Additional Providers
GROQ_API_KEY=your_groq_key
MISTRAL_API_KEY=your_mistral_key
PERPLEXITY_API_KEY=your_perplexity_key
AZURE_OPENAI_API_KEY=your_azure_openai_key
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint
OLLAMA_BASE_URL=http://localhost:11434  # For local Ollama deployment
``` 

--- requirements.txt.txt ---
# Core dependencies
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
resend
python-dotenv>=1.0.0

# Security
python-jose[cryptography]
passlib[bcrypt]
bcrypt>=4.0.1
PyJWT>=2.8.0

# Rate Limiting
slowapi
redis

# Database
SQLAlchemy
psycopg2-binary
asyncpg>=0.29.0

pydantic>=2.4.2
httpx>=0.25.0

# Background task processing
celery>=5.3.4
flower>=2.0.1

# LangGraph and LLM utilities
langgraph>=0.1.11
langchain-core>=0.2.22
langchain>=0.0.335
langchain-community>=0.0.27

# LangChain Provider Integrations
langchain-openai>=0.0.5  # OpenAI and OpenRouter
langchain-anthropic>=0.1.1  # Anthropic Claude
langchain-google-genai>=0.1.1  # Google Gemini
langchain-xai>=0.0.1  # Grok API
langchain-groq>=0.1.0  # Groq fast inference
langchain-mistralai>=0.1.12  # Mistral AI
langchain-together>=0.1.0  # Together AI
langchain-perplexity>=0.0.1  # Perplexity
langchain-pinecone>=0.1.2  # Pinecone for vector store

# Vector database clients
qdrant-client>=1.6.4
neo4j>=5.13.0

# Data processing
numpy>=1.26.1
pandas>=2.1.2

# Async support
asyncio>=3.4.3
aiohttp>=3.8.6

# Document processing
Pillow>=10.1.0  # Image processing
pytesseract>=0.3.10  # OCR for images
PyMuPDF>=1.23.7  # PDF processing (fitz)
pdfminer.six>=20221105  # Alternative PDF processing
beautifulsoup4>=4.12.2  # HTML processing
html2text>=2020.1.16  # HTML to text conversion
pygments>=2.16.1  # Syntax highlighting for code

# Testing
pytest>=7.4.3
pytest-asyncio>=0.21.1

# Email Services
resend>=0.7.0



--- run.py.txt ---
"""
Entry point for the AI Context Engineering Agent.

This script starts the FastAPI application using uvicorn.
"""
import uvicorn
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

if __name__ == "__main__":
    # Start the FastAPI application
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    ) 

--- start_services.sh.txt ---
#!/bin/bash

# Start Docker services
echo "Starting Neo4j and Qdrant services..."
docker compose up -d

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 5

# Check if services are running
echo "Checking services status..."
docker compose ps

echo "Services should now be available at:"
echo "- Neo4j: http://localhost:7474 (Browser interface)"
echo "- Qdrant: http://localhost:6333/dashboard (Web dashboard)"

echo "You can now run the application with:"
echo "python run.py"

echo "To test database connections, run:"
echo "python test_connections.py" 

--- test_connections.py.txt ---
"""
Test script to verify database connections.

This script tests connections to Neo4j and Qdrant databases
to ensure the basic setup is working correctly.
"""
import asyncio
import logging
from dotenv import load_dotenv

from app.db.neo4j_client import Neo4jClient
from app.db.qdrant_client import QdrantClient
from app.config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

async def test_neo4j_connection():
    """Test connection to Neo4j database."""
    logger.info(f"Testing Neo4j connection to {settings.NEO4J_URI}...")
    try:
        client = Neo4jClient()
        driver = await client.get_driver()
        
        # Run a simple query
        result = await client.run_query("RETURN 'Connected to Neo4j!' as message")
        logger.info("Neo4j test result: %s", result[0]["message"])
        
        # Close connection
        await client.close()
        logger.info("Neo4j connection test successful!")
        return True
    except Exception as e:
        logger.error("Neo4j connection test failed: %s", str(e))
        return False

async def test_qdrant_connection():
    """Test connection to Qdrant database."""
    logger.info(f"Testing Qdrant connection to {settings.QDRANT_HOST}:{settings.QDRANT_PORT}...")
    try:
        client = QdrantClient()
        qdrant = client.get_client()
        
        # Check if client is connected
        collections = qdrant.get_collections()
        logger.info("Qdrant collections: %s", [c.name for c in collections.collections])
        
        # Close connection
        client.close()
        logger.info("Qdrant connection test successful!")
        return True
    except Exception as e:
        logger.error("Qdrant connection test failed: %s", str(e))
        return False

async def main():
    """Run all tests."""
    logger.info("Starting connection tests...")
    logger.info(f"Environment configuration: NEO4J_HOST={settings.NEO4J_HOST}, QDRANT_HOST={settings.QDRANT_HOST}")
    
    neo4j_success = await test_neo4j_connection()
    qdrant_success = await test_qdrant_connection()
    
    if neo4j_success and qdrant_success:
        logger.info("All connection tests passed!")
    else:
        logger.warning("Some connection tests failed!")
        if not neo4j_success:
            logger.warning("Neo4j connection failed. Check if Neo4j is running and credentials are correct.")
        if not qdrant_success:
            logger.warning("Qdrant connection failed. Check if Qdrant is running.")

if __name__ == "__main__":
    asyncio.run(main()) 

--- test_docker.sh.txt ---
#!/bin/bash

# Set environment variables for Docker testing
export NEO4J_HOST=neo4j
export QDRANT_HOST=qdrant
export NEO4J_PORT=7687
export QDRANT_PORT=6333

# Display configuration
echo "Testing with Docker configuration:"
echo "- NEO4J_HOST: $NEO4J_HOST"
echo "- QDRANT_HOST: $QDRANT_HOST"
echo "- NEO4J_PORT: $NEO4J_PORT"
echo "- QDRANT_PORT: $QDRANT_PORT"
echo ""

# Run the connection test
echo "Running connection tests with Docker configuration..."
echo "Note: This will only work when running inside Docker network!"
python test_connections.py 

--- test_local.sh.txt ---
#!/bin/bash

# Set environment variables for local testing
export NEO4J_HOST=localhost
export QDRANT_HOST=localhost
export NEO4J_PORT=7687
export QDRANT_PORT=6333

# Display configuration
echo "Testing with configuration:"
echo "- NEO4J_HOST: $NEO4J_HOST"
echo "- QDRANT_HOST: $QDRANT_HOST"
echo "- NEO4J_PORT: $NEO4J_PORT"
echo "- QDRANT_PORT: $QDRANT_PORT"
echo ""

# Run the connection test
echo "Running connection tests with localhost configuration..."
python test_connections.py 

--- test_merged_processors.py.txt ---
"""
Test file for demonstrating the merged document processors.

This file shows how to use both standard and enhanced processing capabilities.
Run with: python test_merged_processors.py
"""
import os
import asyncio
import json
from pathlib import Path

# Set environment variables for testing (optional)
os.environ["ENABLE_AI"] = "false"  # Set to "true" to enable AI enhancements
os.environ["USE_COGNEE"] = "false"  # Set to "true" to use Cognee abstraction

# Import processors
from app.processors import (
    get_processor,
    get_enhanced_processor,
    ProcessorFactory,
    ProcessingConfig
)

# Sample data
SAMPLE_TEXT = """
# Sample Document

This is a sample document for testing document processing capabilities.

## Features

- Standard processing
- Enhanced processing with AI
- Cognee integration for knowledge graphs
"""

SAMPLE_JSON = """
{
    "name": "Document Processors",
    "features": [
        "Standard processing",
        "Enhanced processing",
        "Cognee integration"
    ],
    "capabilities": {
        "ai": true,
        "graph": true,
        "vector": true
    }
}
"""

async def test_standard_processing():
    """Test standard document processing."""
    print("\n=== Testing Standard Processing ===")
    
    # Create a text processor
    text_processor = get_processor(content_type="text/markdown")
    
    # Process text
    print("Processing markdown text...")
    result = await text_processor.process(SAMPLE_TEXT, metadata={"source": "test"})
    
    # Print results
    print(f"Processed {len(result.get('chunks', []))} chunks")
    
    # Create a JSON processor
    json_processor = get_processor(content_type="application/json")
    
    # Process JSON
    print("Processing JSON data...")
    result = await json_processor.process(SAMPLE_JSON, metadata={"source": "test"})
    
    # Print results
    print(f"Processed {len(result.get('chunks', []))} chunks")
    print(f"Extracted {len(result.get('metadata', {}).get('entities', []))} entities")

async def test_enhanced_processing():
    """Test enhanced document processing with AI."""
    print("\n=== Testing Enhanced Processing ===")
    
    # Check if AI is enabled
    config = ProcessingConfig()
    if not config.enable_ai or not config.available_models:
        print("AI enhancements not available (set ENABLE_AI=true and configure an AI model)")
        return
    
    # Create an enhanced text processor
    enhanced_processor = get_enhanced_processor(
        content_type="text/markdown",
        enhancement_type="analysis"
    )
    
    # Process with enhancements
    print("Processing markdown text with AI enhancements...")
    result = await enhanced_processor.process_with_enhancements(
        SAMPLE_TEXT, 
        metadata={"source": "test"}
    )
    
    # Print results
    print(f"Processed {len(result.get('chunks', []))} chunks")
    if result.get("has_enhancements"):
        print("Enhanced content:")
        print("-" * 40)
        print(result.get("enhanced_content", "No enhanced content"))
        print("-" * 40)
    else:
        print("No enhancements applied")

async def test_file_processing():
    """Test file processing if sample files exist."""
    print("\n=== Testing File Processing ===")
    
    # Check for sample files
    samples_dir = Path("samples")
    if not samples_dir.exists():
        print("No sample directory found, creating it with sample files...")
        samples_dir.mkdir(exist_ok=True)
        
        # Create sample files
        with open(samples_dir / "sample.md", "w") as f:
            f.write(SAMPLE_TEXT)
        
        with open(samples_dir / "sample.json", "w") as f:
            f.write(SAMPLE_JSON)
    
    # Process markdown file
    if (samples_dir / "sample.md").exists():
        print("Processing sample markdown file...")
        md_processor = get_processor(file_path=str(samples_dir / "sample.md"))
        with open(samples_dir / "sample.md", "r") as f:
            result = await md_processor.process(f.read())
        print(f"Processed markdown file: {len(result.get('chunks', []))} chunks")
    
    # Process JSON file with enhancements
    if (samples_dir / "sample.json").exists():
        print("Processing sample JSON file with enhancements...")
        config = ProcessingConfig()
        if config.enable_ai and config.available_models:
            json_processor = get_enhanced_processor(file_path=str(samples_dir / "sample.json"))
            with open(samples_dir / "sample.json", "r") as f:
                result = await json_processor.process_with_enhancements(f.read())
            print(f"Processed JSON file: {len(result.get('chunks', []))} chunks")
            if result.get("has_enhancements"):
                print("AI enhancements applied")
        else:
            print("AI enhancements not available for JSON file processing")

async def test_configuration():
    """Test configuration options."""
    print("\n=== Testing Configuration ===")
    
    # Create configuration
    config = ProcessingConfig()
    
    # Print configuration
    print("Current configuration:")
    print(f"- AI enabled: {config.enable_ai}")
    print(f"- Cognee enabled: {config.use_cognee}")
    print(f"- Available AI models: {config.available_models}")
    print(f"- Default model: {config.default_model}")
    
    # Try to get AI model
    model = config.get_ai_model()
    if model:
        print(f"- AI model loaded: {model.__class__.__name__}")
    else:
        print("- No AI model available")
    
    # Print database configuration
    print("\nDatabase configuration:")
    for db_name, db_config in config.databases.items():
        print(f"- {db_name}: {db_config.get('provider')}")
    
    # Print to_dict output
    config_dict = config.to_dict()
    print("\nConfiguration as dict (excluding sensitive data):")
    print(json.dumps(config_dict, indent=2))

async def main():
    """Run tests."""
    # Print processor information
    print("=== Document Processors Test ===")
    print("This test demonstrates both standard and enhanced processing capabilities.")
    
    # Print available processors
    print("\nAvailable processors:")
    for ext in sorted(list(ProcessorFactory.EXTENSION_MAPPING.keys())):
        proc = ProcessorFactory.EXTENSION_MAPPING[ext].__name__
        print(f"- {ext}: {proc}")
    
    # Run tests
    await test_standard_processing()
    await test_enhanced_processing()
    await test_file_processing()
    await test_configuration()
    
    print("\n=== Testing Complete ===")

if __name__ == "__main__":
    asyncio.run(main()) 

--- test_resend.py.txt ---
import os
import resend

# It's better to get the key from an environment variable
api_key = os.environ.get("RESEND_API_KEY")

if not api_key:
    print("Error: RESEND_API_KEY environment variable not set.")
    exit(1)

resend.api_key = api_key

params = {
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Test Email from ConTXT Backend",
    "html": "<strong>This is a test email to verify resend functionality.</strong>",
}

try:
    print("Attempting to send email...")
    email = resend.Emails.send(params)
    print("Email sent successfully!")
    print(email)
except Exception as e:
    print(f"Failed to send email. Error: {e}")


