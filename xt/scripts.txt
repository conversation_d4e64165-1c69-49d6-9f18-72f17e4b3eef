--- scripts/create_health_endpoint.py.txt ---
#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add a health endpoint to the FastAPI application for Docker health checks.
This script checks if a health endpoint exists and adds one if needed.
"""
import os
import re

def add_health_endpoint():
    """
    Add a health endpoint to the FastAPI application if one doesn't exist.
    """
    # Path to main.py
    main_py_path = os.path.join("app", "main.py")
    
    # Read the current content
    with open(main_py_path, 'r') as file:
        content = file.read()
    
    # Check if a health endpoint already exists
    if "@app.get('/health')" in content or "@app.get(\"/health\")" in content:
        print("Health endpoint already exists.")
        return
    
    # Find where to insert the health endpoint
    # Add after the last route or before the if __name__ == "__main__" block
    if "if __name__ == \"__main__\"" in content:
        # Insert before the if __name__ block
        pattern = r'if __name__ == "__main__"'
        replacement = """
@app.get("/health")
async def health():
    """Health check endpoint."""
    return {"status": "ok", "service": "context_processor"}

if __name__ == "__main__"\
"""
        content = re.sub(pattern, replacement, content)
    else:
        # Append to the end of the file
        content += """

@app.get("/health")
async def health():
    """Health check endpoint."""
    return {"status": "ok", "service": "context_processor"}
"""
    
    # Add database health check endpoint
    if "@app.get('/api/health/databases')" not in content and "@app.get(\"/api/health/databases\")" not in content:
        # Add database health check after health endpoint or at the end
        health_endpoint_pattern = r'@app\.get\("\/health"\)[^\}]+\}'
        if re.search(health_endpoint_pattern, content):
            # Add after the health endpoint
            content = re.sub(
                health_endpoint_pattern,
                lambda m: m.group(0) + """

@app.get("/api/health/databases")
async def database_health():
    """Database health check endpoint."""
    health_status = {"neo4j": False, "qdrant": False, "postgres": False, "redis": False}
    
    try:
        # Check Neo4j
        from app.db.neo4j_client import Neo4jClient
        neo4j_client = Neo4jClient()
        result = await neo4j_client.run_query("RETURN 'Connected to Neo4j!' as message")
        if result and result[0].get("message") == "Connected to Neo4j!":
            health_status["neo4j"] = True
        await neo4j_client.close()
    except Exception as e:
        health_status["neo4j_error"] = str(e)
    
    try:
        # Check Qdrant
        from app.db.qdrant_client import QdrantClient
        qdrant_client = QdrantClient()
        client = qdrant_client.get_client()
        collections = client.get_collections()
        if hasattr(collections, "collections"):
            health_status["qdrant"] = True
            health_status["qdrant_collections"] = [c.name for c in collections.collections]
        qdrant_client.close()
    except Exception as e:
        health_status["qdrant_error"] = str(e)
    
    # Placeholder for PostgreSQL check (implement when PostgreSQL client is available)
    try:
        health_status["postgres"] = True  # Placeholder
    except Exception as e:
        health_status["postgres_error"] = str(e)
    
    # Placeholder for Redis check (implement when Redis client is available)
    try:
        health_status["redis"] = True  # Placeholder
    except Exception as e:
        health_status["redis_error"] = str(e)
    
    status_code = 200 if all([health_status["neo4j"], health_status["qdrant"]]) else 503
    return health_status""",
                content
            )
        else:
            # Add to the end
            content += """

@app.get("/api/health/databases")
async def database_health():
    """Database health check endpoint."""
    health_status = {"neo4j": False, "qdrant": False, "postgres": False, "redis": False}
    
    try:
        # Check Neo4j
        from app.db.neo4j_client import Neo4jClient
        neo4j_client = Neo4jClient()
        result = await neo4j_client.run_query("RETURN 'Connected to Neo4j!' as message")
        if result and result[0].get("message") == "Connected to Neo4j!":
            health_status["neo4j"] = True
        await neo4j_client.close()
    except Exception as e:
        health_status["neo4j_error"] = str(e)
    
    try:
        # Check Qdrant
        from app.db.qdrant_client import QdrantClient
        qdrant_client = QdrantClient()
        client = qdrant_client.get_client()
        collections = client.get_collections()
        if hasattr(collections, "collections"):
            health_status["qdrant"] = True
            health_status["qdrant_collections"] = [c.name for c in collections.collections]
        qdrant_client.close()
    except Exception as e:
        health_status["qdrant_error"] = str(e)
    
    # Placeholder for PostgreSQL check (implement when PostgreSQL client is available)
    try:
        health_status["postgres"] = True  # Placeholder
    except Exception as e:
        health_status["postgres_error"] = str(e)
    
    # Placeholder for Redis check (implement when Redis client is available)
    try:
        health_status["redis"] = True  # Placeholder
    except Exception as e:
        health_status["redis_error"] = str(e)
    
    status_code = 200 if all([health_status["neo4j"], health_status["qdrant"]]) else 503
    return health_status
"""
    
    # Write the updated content
    with open(main_py_path, 'w') as file:
        file.write(content)
    
    print("Health endpoints added successfully.")

if __name__ == "__main__":
    add_health_endpoint() 

--- scripts/deploy_auth.py.txt ---
#!/usr/bin/env python3
"""
Deployment script for ConTXT Authentication System.
Sets up database schema, creates admin user, and validates the system.
"""
import asyncio
import asyncpg
import bcrypt
import os
import sys
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from app.config.settings import settings
from app.db.postgres_client import PostgreSQLClient
from app.core.auth_service import AuthService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AuthDeployment:
    """Handles authentication system deployment."""
    
    def __init__(self):
        self.postgres_client = PostgreSQLClient()
        self.auth_service = None
    
    async def setup_database(self):
        """Set up database schema and initial data."""
        logger.info("🔧 Setting up authentication database...")
        
        try:
            # Connect to database
            await self.postgres_client.connect()
            pool = await self.postgres_client.get_pool()
            self.auth_service = AuthService(pool)
            
            # Execute schema creation script
            schema_path = Path(__file__).parent / "create_auth_tables.sql"
            if schema_path.exists():
                logger.info("📋 Executing database schema...")
                await self.postgres_client.execute_script(str(schema_path))
                logger.info("✅ Database schema created successfully")
            else:
                logger.error(f"❌ Schema file not found: {schema_path}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Database setup failed: {e}")
            return False
    
    async def create_admin_user(self):
        """Create default admin user if it doesn't exist."""
        logger.info("👤 Setting up admin user...")
        
        try:
            async with self.postgres_client.get_connection() as conn:
                # Check if admin user exists
                admin_exists = await conn.fetchrow(
                    "SELECT id FROM users WHERE email = $1", 
                    "<EMAIL>"
                )
                
                if admin_exists:
                    logger.info("✅ Admin user already exists")
                    return True
                
                # Create admin user
                admin_password = os.getenv("ADMIN_PASSWORD", "AdminPass123!")
                password_hash = bcrypt.hashpw(
                    admin_password.encode('utf-8'), 
                    bcrypt.gensalt()
                ).decode('utf-8')
                
                admin_id = await conn.fetchval("""
                    INSERT INTO users (
                        email, password_hash, first_name, last_name, 
                        is_active, is_verified, subscription_tier
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    RETURNING id
                """, "<EMAIL>", password_hash, "System", "Administrator", 
                    True, True, "enterprise")
                
                # Create admin preferences
                await conn.execute("""
                    INSERT INTO user_preferences (user_id, email_notifications, marketing_emails)
                    VALUES ($1, $2, $3)
                """, admin_id, True, False)
                
                logger.info("✅ Admin user created successfully")
                logger.info(f"📧 Admin email: <EMAIL>")
                logger.info(f"🔑 Admin password: {admin_password}")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ Admin user creation failed: {e}")
            return False
    
    async def validate_system(self):
        """Validate the authentication system is working."""
        logger.info("🔍 Validating authentication system...")
        
        try:
            # Test database connectivity
            if not await self.postgres_client.health_check():
                logger.error("❌ Database health check failed")
                return False
            
            # Test admin login
            from app.schemas.auth import UserLogin
            login_data = UserLogin(
                email="<EMAIL>",
                password=os.getenv("ADMIN_PASSWORD", "AdminPass123!")
            )
            
            try:
                token_response = await self.auth_service.login_user(login_data)
                if token_response.access_token:
                    logger.info("✅ Admin login test successful")
                else:
                    logger.error("❌ Admin login test failed - no token")
                    return False
            except Exception as e:
                logger.error(f"❌ Admin login test failed: {e}")
                return False
            
            # Test token validation
            try:
                import jwt
                payload = jwt.decode(
                    token_response.access_token,
                    settings.JWT_SECRET_KEY,
                    algorithms=[settings.JWT_ALGORITHM]
                )
                if payload.get("type") == "access":
                    logger.info("✅ JWT token validation successful")
                else:
                    logger.error("❌ JWT token validation failed")
                    return False
            except Exception as e:
                logger.error(f"❌ JWT token validation failed: {e}")
                return False
            
            logger.info("🎉 Authentication system validation complete!")
            return True
            
        except Exception as e:
            logger.error(f"❌ System validation failed: {e}")
            return False
    
    async def cleanup_expired_data(self):
        """Clean up expired tokens and sessions."""
        logger.info("🧹 Cleaning up expired data...")
        
        try:
            async with self.postgres_client.get_connection() as conn:
                # Clean up expired tokens
                await conn.execute("SELECT cleanup_expired_tokens()")
                logger.info("✅ Expired tokens cleaned up")
            
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")
    
    async def deploy(self):
        """Execute full deployment process."""
        logger.info("🚀 Starting ConTXT Authentication System deployment...")
        
        try:
            # Setup database
            if not await self.setup_database():
                logger.error("❌ Database setup failed")
                return False
            
            # Create admin user
            if not await self.create_admin_user():
                logger.error("❌ Admin user creation failed")
                return False
            
            # Validate system
            if not await self.validate_system():
                logger.error("❌ System validation failed")
                return False
            
            # Cleanup expired data
            await self.cleanup_expired_data()
            
            logger.info("🎉 ConTXT Authentication System deployed successfully!")
            logger.info("📋 System Summary:")
            logger.info("   • Database schema created")
            logger.info("   • Admin user configured")
            logger.info("   • JWT authentication working")
            logger.info("   • Rate limiting enabled")
            logger.info("   • Health checks active")
            logger.info("   • Metrics collection enabled")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Deployment failed: {e}")
            return False
        
        finally:
            await self.postgres_client.disconnect()

async def main():
    """Main deployment function."""
    deployment = AuthDeployment()
    
    # Check environment variables
    required_vars = ["DB_HOST", "DB_USERNAME", "DB_PASSWORD", "DB_NAME"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {missing_vars}")
        sys.exit(1)
    
    # Run deployment
    success = await deployment.deploy()
    
    if success:
        logger.info("✅ Deployment completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Deployment failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())


--- scripts/docker-compose.prod.yml.txt ---
version: '3.8'

# Production overrides for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

services:
  # Production configuration for app
  app:
    restart: always
    environment:
      # Disable development/debug settings
      - DEBUG=false
      - LOG_LEVEL=warning
      # CORS settings for production
      - CORS_ORIGINS=["https://your-production-domain.com"]
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    # Use build arguments for production
    build:
      context: .
      args:
        - ENVIRONMENT=production
    # Disable volume mount of source code
    volumes:
      - ./uploads:/app/uploads
      - ./processed:/app/processed
      - ./logs:/app/logs
    # No code reload in production
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4

  # Production configuration for neo4j
  neo4j:
    restart: always
    environment:
      - NEO4J_dbms_memory_pagecache_size=4G
      - NEO4J_dbms.memory.heap.initial_size=4G
      - NEO4J_dbms_memory_heap_max__size=8G
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 10G
        reservations:
          cpus: '1'
          memory: 6G

  # Production configuration for qdrant
  qdrant:
    restart: always
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G

  # Production configuration for postgres
  postgres:
    restart: always
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          memory: 1G

  # Production configuration for redis
  redis:
    restart: always
    command: redis-server --maxmemory 1gb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1.5G
        reservations:
          memory: 512M

  # Production configuration for worker
  worker:
    restart: always
    command: celery -A app.core.worker worker --loglevel=warning --concurrency=4
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          memory: 1G

  # Production configuration for flower - OPTIONAL
  flower:
    restart: always
    environment:
      - FLOWER_BASIC_AUTH=${FLOWER_USER}:${FLOWER_PASSWORD}
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 512M 

--- scripts/docker_start.sh.txt ---
#!/bin/bash
# Docker start script for ConTXT document processing system

# Color outputs
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== ConTXT Document Processing System ===${NC}"

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${RED}Error: .env file not found. Please create .env file with required configuration.${NC}"
    echo -e "You can use .env.example as a template."
    exit 1
fi

# Check if Docker is running
docker info > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

echo -e "${GREEN}Starting Docker containers...${NC}"
docker compose up -d

# Wait for services to start
echo -e "${YELLOW}Waiting for services to start...${NC}"
sleep 5

# Check container status
echo -e "\n${YELLOW}Checking container status:${NC}"
docker compose ps

echo -e "\n${YELLOW}API will be available at:${NC} http://localhost:8000"
echo -e "${YELLOW}API Documentation:${NC} http://localhost:8000/docs"
echo -e "${YELLOW}Neo4j Browser:${NC} http://localhost:7474 (neo4j/password)"
echo -e "${YELLOW}Qdrant Dashboard:${NC} http://localhost:6333/dashboard"

if docker compose ps | grep -q "contxt_flower"; then
    echo -e "${YELLOW}Celery Flower:${NC} http://localhost:5555"
fi

echo -e "\n${GREEN}System started successfully!${NC}"
echo -e "Run the following command to view logs:"
echo -e "  docker compose logs -f"
echo -e "To stop the system:"
echo -e "  docker compose down"
echo -e "To run tests:"
echo -e "  ./scripts/test_docker_system.py" 

--- scripts/docker_stop.sh.txt ---
#!/bin/bash
# Docker stop script for ConTXT document processing system

# Color outputs
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Stopping ConTXT Document Processing System ===${NC}"

# Check if Docker is running
docker info > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Docker is not running.${NC}"
    exit 1
fi

# Check if test data directory exists and clean it up
if [ -d "test_data" ]; then
    echo -e "${YELLOW}Cleaning up test data...${NC}"
    rm -rf test_data
fi

# Ask if volumes should be removed
read -p "Do you want to remove all data volumes as well? (y/N): " remove_volumes

if [ "$remove_volumes" = "y" ] || [ "$remove_volumes" = "Y" ]; then
    echo -e "${YELLOW}Stopping containers and removing volumes...${NC}"
    docker compose down -v
    echo -e "${GREEN}Containers stopped and volumes removed.${NC}"
else
    echo -e "${YELLOW}Stopping containers...${NC}"
    docker compose down
    echo -e "${GREEN}Containers stopped. Data volumes preserved.${NC}"
fi

echo -e "\n${GREEN}System shutdown complete!${NC}" 

--- scripts/setup_env.sh.txt ---
#!/bin/bash
# Environment setup script for ConTXT Document Processing System

# Color outputs
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo -e "${BLUE}${BOLD}=== ConTXT Document Processing System - Environment Setup ===${NC}"
echo -e "This script will help you set up the environment configuration for the ConTXT Document Processing System.\n"

# Check if .env already exists
if [ -f ".env" ]; then
    echo -e "${YELLOW}A .env file already exists.${NC}"
    read -p "Do you want to overwrite it? (y/N): " overwrite
    if [ "$overwrite" != "y" ] && [ "$overwrite" != "Y" ]; then
        echo -e "${GREEN}Setup aborted. Your .env file remains unchanged.${NC}"
        exit 0
    fi
fi

echo -e "\n${YELLOW}Setting up .env file with default values...${NC}"

# Create .env file
cat > .env << EOL
# =============================================================================
# API Keys (Required to enable respective provider)
# =============================================================================
ANTHROPIC_API_KEY="your_anthropic_api_key_here"
PERPLEXITY_API_KEY="your_perplexity_api_key_here"
OPENAI_API_KEY="your_openai_api_key_here"
GOOGLE_API_KEY="your_google_api_key_here"
MISTRAL_API_KEY="your_mistral_key_here"
XAI_API_KEY="your_xai_api_key_here"
AZURE_OPENAI_API_KEY="your_azure_key_here"
OLLAMA_API_KEY="your_ollama_api_key_here"
GITHUB_API_KEY="your_github_api_key_here"

# =============================================================================
# Cognee Configuration
# =============================================================================
LLM_API_KEY=\${XAI_API_KEY}  # Default to XAI key
LLM_PROVIDER="openai"  # xAI uses OpenAI-compatible API
LLM_MODEL="grok-beta"  # or "grok-4" based on your preference
LLM_ENDPOINT="https://api.x.ai/v1"

# Processing Configuration
CHUNK_SIZE=1024
CHUNK_OVERLAP=128
EMBEDDING_MODEL="text-embedding-3-large"

# =============================================================================
# Graph Database Configuration (Neo4j)
# =============================================================================
GRAPH_DATABASE_PROVIDER=neo4j
# For Docker: use service name, for local: use localhost
NEO4J_URI=bolt://neo4j:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password

# Local development override (uncomment for local development)
# NEO4J_URI=bolt://localhost:7687

# =============================================================================
# Vector Database Configuration (Qdrant)
# =============================================================================
VECTOR_DB_PROVIDER=qdrant
# For Docker: use service name, for local: use localhost
VECTOR_DB_URL=http://qdrant:6333
VECTOR_DB_KEY=""  # Empty for local development

# Local development override (uncomment for local development)
# VECTOR_DB_URL=http://localhost:6333

# =============================================================================
# Relational Database Configuration (PostgreSQL)
# =============================================================================
DB_PROVIDER=postgres
# For Docker: use service name, for local: use localhost
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_NAME=document_processor
DB_SSL_MODE=disable

# Local development override (uncomment for local development)
# DB_HOST=localhost
# DB_USERNAME=cognee
# DB_PASSWORD=cognee
# DB_NAME=cognee_db

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=""

# Local development override (uncomment for local development)
# REDIS_URL=redis://localhost:6379/0

# =============================================================================
# FastAPI Application Configuration
# =============================================================================
DEBUG=true
LOG_LEVEL=info
ENVIRONMENT=development

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://localhost:5173"]

# =============================================================================
# File Processing Configuration
# =============================================================================
MAX_FILE_SIZE=104857600  # 100MB in bytes
UPLOAD_PATH=/app/uploads
PROCESSED_PATH=/app/processed
MAX_CONCURRENT_UPLOADS=10
SUPPORTED_FILE_TYPES=["json", "csv", "txt", "md", "pdf", "png", "jpg", "jpeg"]

# =============================================================================
# Celery Configuration
# =============================================================================
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=UTC
CELERY_ENABLE_UTC=true

# =============================================================================
# Security Configuration
# =============================================================================
SECRET_KEY=your-secret-key-here-make-it-long-and-random
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# =============================================================================
# Monitoring and Logging
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_FORMAT=json
LOG_FILE=/app/logs/app.log

# =============================================================================
# Production Overrides (uncomment for production)
# =============================================================================
# DEBUG=false
# ENVIRONMENT=production
# LOG_LEVEL=warning
# NEO4J_URI=neo4j+s://your-production-instance.databases.neo4j.io:7687
# VECTOR_DB_URL=https://your-cluster.cloud.qdrant.io:6333
# VECTOR_DB_KEY=your-production-qdrant-key
# DB_HOST=your-production-db-host
# DB_SSL_MODE=require
EOL
