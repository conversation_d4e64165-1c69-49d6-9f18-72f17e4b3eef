--- tests/conftest.py.txt ---
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


--- tests/load_test_auth.py.txt ---
"""
Load testing for ConTXT authentication endpoints using Locust.
Tests authentication system under concurrent load.
"""
from locust import HttpUser, task, between
import random
import string
import json

class AuthLoadTestUser(HttpUser):
    """Load test user for authentication endpoints."""
    
    wait_time = between(1, 3)
    
    def on_start(self):
        """Setup user data for load testing."""
        self.email = f"loadtest_{random.randint(1000, 9999)}@example.com"
        self.password = "LoadTest123!"
        self.access_token = None
        self.user_id = None
    
    @task(1)
    def register_user(self):
        """Test user registration under load."""
        response = self.client.post("/auth/register", json={
            "email": self.email,
            "password": self.password,
            "first_name": "Load",
            "last_name": "Test"
        })
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success") and "user_id" in data.get("data", {}):
                self.user_id = data["data"]["user_id"]
                print(f"✓ Registered user: {self.email}")
        elif response.status_code == 429:
            print(f"⚠ Rate limited during registration: {self.email}")
        else:
            print(f"✗ Registration failed for {self.email}: {response.status_code}")
    
    @task(3)
    def login_user(self):
        """Test user login under load."""
        if not self.access_token:
            response = self.client.post("/auth/login", json={
                "email": self.email,
                "password": self.password
            })
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                self.user_id = data.get("user_id")
                print(f"✓ Logged in user: {self.email}")
            elif response.status_code == 429:
                print(f"⚠ Rate limited during login: {self.email}")
            elif response.status_code == 401:
                # User might not exist yet, try to register
                self.register_user()
            else:
                print(f"✗ Login failed for {self.email}: {response.status_code}")
    
    @task(2)
    def access_profile(self):
        """Test accessing protected profile endpoint."""
        if self.access_token:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.client.get("/auth/me", headers=headers)
            
            if response.status_code == 200:
                print(f"✓ Profile accessed for: {self.email}")
            elif response.status_code == 401:
                # Token might be expired
                self.access_token = None
                print(f"⚠ Token expired for: {self.email}")
            else:
                print(f"✗ Profile access failed for {self.email}: {response.status_code}")
    
    @task(1)
    def update_profile(self):
        """Test profile update under load."""
        if self.access_token:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.client.put("/auth/me", 
                headers=headers,
                json={
                    "first_name": f"Updated{random.randint(1, 100)}",
                    "last_name": "LoadTest"
                }
            )
            
            if response.status_code == 200:
                print(f"✓ Profile updated for: {self.email}")
            elif response.status_code == 401:
                self.access_token = None
                print(f"⚠ Token expired during profile update: {self.email}")
    
    @task(1)
    def test_password_reset_request(self):
        """Test password reset request under load."""
        response = self.client.post("/auth/forgot-password", json={
            "email": self.email
        })
        
        if response.status_code == 200:
            print(f"✓ Password reset requested for: {self.email}")
        elif response.status_code == 429:
            print(f"⚠ Rate limited during password reset: {self.email}")
    
    @task(1)
    def create_api_key(self):
        """Test API key creation under load."""
        if self.access_token:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.client.post("/auth/api-keys",
                headers=headers,
                json={
                    "key_name": f"LoadTest-{random.randint(1, 1000)}",
                    "permissions": ["read", "write"]
                }
            )
            
            if response.status_code == 200:
                print(f"✓ API key created for: {self.email}")
            elif response.status_code == 401:
                self.access_token = None
    
    @task(1)
    def list_api_keys(self):
        """Test API key listing under load."""
        if self.access_token:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.client.get("/auth/api-keys", headers=headers)
            
            if response.status_code == 200:
                print(f"✓ API keys listed for: {self.email}")
            elif response.status_code == 401:
                self.access_token = None
    
    @task(1)
    def health_check(self):
        """Test health check endpoint."""
        response = self.client.get("/auth/health")
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "healthy":
                print("✓ Auth service healthy")
        else:
            print(f"✗ Health check failed: {response.status_code}")

class HighVolumeAuthUser(HttpUser):
    """High-volume authentication testing."""
    
    wait_time = between(0.1, 0.5)  # Much faster requests
    
    def on_start(self):
        """Setup for high-volume testing."""
        self.email = f"highvol_{random.randint(10000, 99999)}@example.com"
        self.password = "HighVol123!"
        self.access_token = None
    
    @task(5)
    def rapid_login_attempts(self):
        """Test rapid login attempts."""
        response = self.client.post("/auth/login", json={
            "email": self.email,
            "password": self.password
        })
        
        if response.status_code == 429:
            print("✓ Rate limiting working correctly")
        elif response.status_code == 401:
            # Expected for non-existent users
            pass
    
    @task(2)
    def rapid_registration_attempts(self):
        """Test rapid registration attempts."""
        unique_email = f"rapid_{random.randint(100000, 999999)}@example.com"
        response = self.client.post("/auth/register", json={
            "email": unique_email,
            "password": self.password
        })
        
        if response.status_code == 429:
            print("✓ Registration rate limiting working")

class SecurityTestUser(HttpUser):
    """Security-focused load testing."""
    
    wait_time = between(0.5, 2)
    
    @task(1)
    def test_invalid_tokens(self):
        """Test with invalid JWT tokens."""
        invalid_tokens = [
            "invalid.token.here",
            "Bearer fake_token",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid",
            ""
        ]
        
        for token in invalid_tokens:
            headers = {"Authorization": f"Bearer {token}"}
            response = self.client.get("/auth/me", headers=headers)
            
            if response.status_code == 401:
                print("✓ Invalid token properly rejected")
            else:
                print(f"✗ Security issue: Invalid token accepted: {response.status_code}")
    
    @task(1)
    def test_sql_injection_attempts(self):
        """Test SQL injection in authentication."""
        malicious_inputs = [
            "<EMAIL>'; DROP TABLE users; --",
            "<EMAIL>' OR '1'='1",
            "<EMAIL>' UNION SELECT * FROM users --",
            "'; DELETE FROM users WHERE '1'='1"
        ]
        
        for malicious_email in malicious_inputs:
            response = self.client.post("/auth/login", json={
                "email": malicious_email,
                "password": "password"
            })
            
            # Should get validation error or 401, not 500
            if response.status_code in [401, 422]:
                print("✓ SQL injection attempt blocked")
            elif response.status_code == 500:
                print(f"⚠ Potential SQL injection vulnerability: {malicious_email}")
    
    @task(1)
    def test_password_brute_force(self):
        """Test password brute force protection."""
        common_passwords = [
            "password", "123456", "admin", "password123",
            "qwerty", "letmein", "welcome", "monkey"
        ]
        
        for password in common_passwords:
            response = self.client.post("/auth/login", json={
                "email": "<EMAIL>",
                "password": password
            })
            
            if response.status_code == 429:
                print("✓ Brute force protection active")
                break
            elif response.status_code == 423:
                print("✓ Account locked due to failed attempts")
                break

# Custom load test scenarios
class PeakLoadUser(HttpUser):
    """Simulate peak load conditions."""
    
    wait_time = between(0.1, 1)
    weight = 3  # Higher weight for peak load simulation
    
    def on_start(self):
        """Setup for peak load testing."""
        self.user_batch = random.randint(1, 1000)
        self.email = f"peak_{self.user_batch}@example.com"
        self.password = "PeakLoad123!"
    
    @task(10)
    def simulate_active_user(self):
        """Simulate an active user session."""
        # Login
        login_response = self.client.post("/auth/login", json={
            "email": self.email,
            "password": self.password
        })
        
        if login_response.status_code == 200:
            token = login_response.json().get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
            
            # Simulate user activity
            activities = [
                lambda: self.client.get("/auth/me", headers=headers),
                lambda: self.client.get("/auth/api-keys", headers=headers),
                lambda: self.client.get("/auth/health"),
            ]
            
            # Perform random activities
            for _ in range(random.randint(1, 5)):
                activity = random.choice(activities)
                activity()

# Usage instructions:
"""
Run load tests with different scenarios:

1. Basic load test:
   locust -f load_test_auth.py --host=http://localhost:8000

2. High volume test:
   locust -f load_test_auth.py --host=http://localhost:8000 -u 100 -r 10 -t 300s

3. Security test:
   locust -f load_test_auth.py --host=http://localhost:8000 -u 50 -r 5 --only-summary

4. Peak load simulation:
   locust -f load_test_auth.py --host=http://localhost:8000 -u 200 -r 20 -t 600s

5. Specific user class:
   locust -f load_test_auth.py SecurityTestUser --host=http://localhost:8000

Environment variables for testing:
- LOAD_TEST_USERS: Number of concurrent users (default: 10)
- LOAD_TEST_DURATION: Test duration in seconds (default: 300)
- LOAD_TEST_SPAWN_RATE: User spawn rate per second (default: 1)
"""


--- tests/test_auth.py.txt ---
"""
Comprehensive authentication tests for ConTXT API.
Tests user registration, login, email verification, password management, and API key management.
"""
import pytest
import asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
import asyncpg
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import jwt
import bcrypt

from app.main import app
from app.core.auth_service import AuthService
from app.schemas.auth import UserRegistration, UserLogin
from app.config.settings import settings

# Test database setup
TEST_DATABASE_URL = "postgresql://test_user:test_pass@localhost/test_contxt"

@pytest.fixture
async def test_db():
    """Create test database connection."""
    try:
        db_pool = await asyncpg.create_pool(TEST_DATABASE_URL)
        yield db_pool
        await db_pool.close()
    except Exception:
        # Mock database if test DB not available
        mock_pool = AsyncMock()
        yield mock_pool

@pytest.fixture
def auth_service(test_db):
    """Create auth service instance."""
    return AuthService(test_db)

@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)

@pytest.fixture
async def async_client():
    """Create async test client."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

class TestUserRegistration:
    """Test user registration functionality."""
    
    async def test_register_valid_user(self, auth_service):
        """Test successful user registration."""
        user_data = UserRegistration(
            email="<EMAIL>",
            password="TestPass123",
            first_name="Test",
            last_name="User"
        )
        
        with patch.object(auth_service, '_send_verification_email') as mock_email:
            with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
                mock_conn = AsyncMock()
                mock_acquire.return_value.__aenter__.return_value = mock_conn
                mock_conn.fetchrow.return_value = None  # No existing user
                mock_conn.fetchval.return_value = "test-user-id"
                
                result = await auth_service.register_user(user_data)
                
                assert "user_id" in result
                assert "verification" in result["message"].lower()
                mock_email.assert_called_once()
    
    async def test_register_duplicate_email(self, auth_service):
        """Test registration with existing email."""
        user_data = UserRegistration(
            email="<EMAIL>",
            password="TestPass123"
        )
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = {"id": "existing-user-id"}
            
            with pytest.raises(Exception) as exc_info:
                await auth_service.register_user(user_data)
            
            assert "already registered" in str(exc_info.value.detail)
    
    def test_register_weak_password(self, client):
        """Test registration with weak password."""
        response = client.post("/auth/register", json={
            "email": "<EMAIL>",
            "password": "weak"
        })
        
        assert response.status_code == 422
        assert "at least 8 characters" in str(response.json())

class TestUserLogin:
    """Test user login functionality."""
    
    async def test_login_valid_credentials(self, auth_service):
        """Test successful login."""
        login_data = UserLogin(
            email="<EMAIL>",
            password="TestPass123"
        )
        
        # Mock user data
        mock_user = {
            'id': 'test-user-id',
            'email': '<EMAIL>',
            'password_hash': bcrypt.hashpw(b'TestPass123', bcrypt.gensalt()).decode('utf-8'),
            'is_active': True,
            'is_verified': True,
            'subscription_tier': 'free',
            'failed_login_attempts': 0,
            'locked_until': None
        }
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = mock_user
            
            result = await auth_service.login_user(login_data)
            
            assert result.access_token
            assert result.refresh_token
            assert result.token_type == "bearer"
            assert result.user_id
    
    async def test_login_invalid_credentials(self, auth_service):
        """Test login with invalid credentials."""
        login_data = UserLogin(
            email="<EMAIL>",
            password="WrongPass123"
        )
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = None
            
            with pytest.raises(Exception) as exc_info:
                await auth_service.login_user(login_data)
            
            assert exc_info.value.status_code == 401
            assert "Invalid email or password" in str(exc_info.value.detail)

class TestEmailVerification:
    """Test email verification functionality."""
    
    async def test_verify_valid_token(self, auth_service):
        """Test email verification with valid token."""
        token = "valid_verification_token"
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = {"user_id": "test-user-id"}
            
            result = await auth_service.verify_email(token)
            assert "verified successfully" in result["message"]
    
    async def test_verify_invalid_token(self, auth_service):
        """Test email verification with invalid token."""
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = None
            
            with pytest.raises(Exception) as exc_info:
                await auth_service.verify_email("invalid_token")
            
            assert exc_info.value.status_code == 400
            assert "Invalid or expired" in str(exc_info.value.detail)

class TestPasswordManagement:
    """Test password reset and change functionality."""
    
    async def test_password_reset_request(self, auth_service):
        """Test password reset request."""
        email = "<EMAIL>"
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = {"id": "test-user-id"}
            
            with patch.object(auth_service, '_send_password_reset_email') as mock_email:
                result = await auth_service.request_password_reset(email)
                
                assert "reset link has been sent" in result["message"]
                mock_email.assert_called_once()
    
    async def test_password_change(self, auth_service):
        """Test password change with valid current password."""
        user_id = "test-user-id"
        current_password = "OldPass123"
        new_password = "NewPass123"
        
        mock_user = {
            'password_hash': bcrypt.hashpw(current_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        }
        
        with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
            mock_conn = AsyncMock()
            mock_acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetchrow.return_value = mock_user
            
            from app.schemas.auth import PasswordChange
            password_data = PasswordChange(
                current_password=current_password,
                new_password=new_password
            )
            
            result = await auth_service.change_password(user_id, password_data)
            assert "Password changed successfully" in result["message"]

class TestJWTTokens:
    """Test JWT token functionality."""
    
    def test_create_access_token(self, auth_service):
        """Test access token creation."""
        data = {"sub": "test-user-id", "email": "<EMAIL>"}
        token = auth_service._create_access_token(data)
        
        # Decode token to verify
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        assert payload["sub"] == "test-user-id"
        assert payload["email"] == "<EMAIL>"
        assert payload["type"] == "access"
    
    def test_create_refresh_token(self, auth_service):
        """Test refresh token creation."""
        data = {"sub": "test-user-id"}
        token = auth_service._create_refresh_token(data)
        
        # Decode token to verify
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        assert payload["sub"] == "test-user-id"
        assert payload["type"] == "refresh"

class TestAPIIntegration:
    """Test API endpoint integration."""
    
    @pytest.mark.asyncio
    async def test_complete_user_flow(self, async_client):
        """Test complete user registration and login flow."""
        # Mock database operations
        with patch('app.core.auth_dependencies.get_db_pool') as mock_get_pool:
            mock_pool = AsyncMock()
            mock_get_pool.return_value = mock_pool
            
            with patch('app.core.auth_service.AuthService') as mock_auth_service:
                mock_service = AsyncMock()
                mock_auth_service.return_value = mock_service
                
                # Mock registration
                mock_service.register_user.return_value = {
                    "user_id": "test-user-id",
                    "message": "Registration successful. Please check your email for verification."
                }
                
                # Test registration
                register_response = await async_client.post("/auth/register", json={
                    "email": "<EMAIL>",
                    "password": "TestPass123",
                    "first_name": "Integration",
                    "last_name": "Test"
                })
                
                assert register_response.status_code == 200
                data = register_response.json()
                assert data["success"] is True
                assert "user_id" in data["data"]

class TestRateLimiting:
    """Test rate limiting functionality."""
    
    @pytest.mark.asyncio
    async def test_login_rate_limiting(self, async_client):
        """Test rate limiting on login endpoint."""
        with patch('app.core.auth_dependencies.get_db_pool') as mock_get_pool:
            mock_pool = AsyncMock()
            mock_get_pool.return_value = mock_pool
            
            # Attempt multiple rapid logins
            for i in range(3):
                response = await async_client.post("/auth/login", json={
                    "email": "<EMAIL>",
                    "password": "wrong_password"
                })
                
                # Should get auth errors, not rate limit errors for first few attempts
                assert response.status_code in [401, 422, 500]  # Auth errors

class TestSecurity:
    """Test security features."""
    
    async def test_password_hashing(self, auth_service):
        """Test password hashing security."""
        password = "TestPassword123"
        hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
        
        # Verify password can be checked
        assert bcrypt.checkpw(password.encode('utf-8'), hashed)
        assert not bcrypt.checkpw(b'wrong_password', hashed)
    
    def test_jwt_token_expiration(self, auth_service):
        """Test JWT token expiration."""
        data = {"sub": "test-user-id"}
        
        # Create token with short expiration
        with patch.object(auth_service, 'access_token_expire', 0):  # Immediate expiration
            token = auth_service._create_access_token(data)
            
            # Token should be expired
            with pytest.raises(jwt.ExpiredSignatureError):
                jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])

# Performance and Load Testing Helpers
class TestPerformance:
    """Test performance characteristics."""
    
    @pytest.mark.asyncio
    async def test_concurrent_registrations(self, auth_service):
        """Test handling concurrent user registrations."""
        async def register_user(email_suffix):
            user_data = UserRegistration(
                email=f"user{email_suffix}@example.com",
                password="TestPass123"
            )
            
            with patch.object(auth_service.db_pool, 'acquire') as mock_acquire:
                mock_conn = AsyncMock()
                mock_acquire.return_value.__aenter__.return_value = mock_conn
                mock_conn.fetchrow.return_value = None
                mock_conn.fetchval.return_value = f"user-id-{email_suffix}"
                
                with patch.object(auth_service, '_send_verification_email'):
                    return await auth_service.register_user(user_data)
        
        # Test concurrent registrations
        tasks = [register_user(i) for i in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed
        for result in results:
            assert not isinstance(result, Exception)
            assert "user_id" in result

# Fixtures for test data
@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "TestPass123",
        "first_name": "Test",
        "last_name": "User"
    }

@pytest.fixture
def sample_login_data():
    """Sample login data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "TestPass123"
    }

# Run tests with: pytest tests/test_auth.py -v


--- tests/test_email_notifications.py.txt ---
import pytest
from unittest.mock import AsyncMock, patch

from app.core.auth_service import AuthService
from app.core.otp_service import OTPService
from app.schemas.auth import UserLogin, OTPVerification
from app.core.email_service import EmailService

@pytest.fixture
def mock_db_pool():
    """Provides a mock database pool."""
    return AsyncMock()

@pytest.fixture
def mock_email_service():
    """Provides a mock EmailService."""
    return AsyncMock(spec=EmailService)

@pytest.fixture
def otp_service(mock_db_pool, mock_email_service):
    """Provides an OTPService instance with a mock email service."""
    return OTPService(db_pool=mock_db_pool, email_service=mock_email_service)

@pytest.fixture
def auth_service(mock_db_pool, otp_service, mock_email_service):
    """Provides an AuthService instance with mock dependencies."""
    return AuthService(db_pool=mock_db_pool, otp_service=otp_service, email_service=mock_email_service)

@pytest.mark.asyncio
async def test_welcome_email_sent_on_verify(otp_service, mock_email_service):
    """Test that a welcome email is sent upon successful OTP verification."""
    # Arrange
    email = "<EMAIL>"
    otp_code = "123456"
    user_id = "test-user-id-welcome"
    first_name = "Welcome"

    mock_otp_record = {
        "id": "otp-record-id",
        "user_id": user_id,
        "attempts": 0,
        "max_attempts": 3,
        "is_used": False
    }
    mock_user_record = {"email": email, "first_name": first_name}

    with patch.object(otp_service.db_pool, 'acquire') as mock_acquire:
        mock_conn = AsyncMock()
        mock_acquire.return_value.__aenter__.return_value = mock_conn
        # Mock finding a valid OTP record
        mock_conn.fetchrow.side_effect = [mock_otp_record, mock_user_record]

        # Act
        await otp_service.verify_email_otp(email, otp_code)

        # Assert
        # Check that user is verified and activated
        mock_conn.execute.assert_any_call("UPDATE users SET is_verified = true, is_active = true WHERE id = $1", user_id)
        # Check that the welcome email was sent with the correct details
        mock_email_service.send_welcome_email.assert_called_once_with(email, first_name)

@pytest.mark.asyncio
async def test_account_locked_email_sent(auth_service, mock_email_service):
    """Test that an account locked email is sent after 5 failed login attempts."""
    # Arrange
    login_data = UserLogin(email="<EMAIL>", password="wrong-password")
    user_id = "test-user-id-locked"
    first_name = "Locked"

    # Mock user data for a user who is about to be locked
    mock_user = {
        'id': user_id,
        'email': login_data.email,
        'password_hash': '$2b$12$abcdefghijklmnopqrstuv.w.xyz.ABCDEFGHIJKL', # Dummy hash
        'is_active': True,
        'is_verified': True,
        'first_name': first_name,
        'subscription_tier': 'free',
        'failed_login_attempts': 4, # 4 previous failed attempts
        'locked_until': None
    }

    with patch.object(auth_service.db_pool, 'acquire') as mock_acquire, \
         patch('bcrypt.checkpw', return_value=False): # Mock password check to fail
        mock_conn = AsyncMock()
        mock_acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetchrow.return_value = mock_user

        # Act
        try:
            await auth_service.login_user(login_data)
        except Exception:
            pass # Expecting an HTTPException for unauthorized

        # Assert
        # Check that the account lock email was sent
        mock_email_service.send_account_locked_email.assert_called_once_with(login_data.email, first_name)
        # Check that failed attempts and locked_until are updated
        update_query = [call for call in mock_conn.execute.call_args_list if 'UPDATE users' in call.args[0]][0]
        assert 'failed_login_attempts = $1' in update_query.args[0]
        assert update_query.args[1] == 5 # 4 + 1
        assert update_query.args[2] is not None # locked_until is set


