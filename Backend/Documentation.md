# ConTXT - AI Context Engineering Agent Backend Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Project Overview](#project-overview)
3. [Architecture](#architecture)
4. [Setup and Installation](#setup-and-installation)
5. [API Documentation](#api-documentation)
6. [Core Components](#core-components)
7. [Database Integration](#database-integration)
8. [Document Processing](#document-processing)
9. [Authentication System](#authentication-system)
10. [AI Integration](#ai-integration)
11. [Security](#security)
12. [Configuration](#configuration)
13. [Development Guide](#development-guide)
14. [Testing](#testing)
15. [Deployment](#deployment)
16. [Monitoring and Logging](#monitoring-and-logging)
17. [Performance Optimization](#performance-optimization)
18. [Troubleshooting](#troubleshooting)
19. [Roadmap](#roadmap)
20. [Contributing](#contributing)

---

## Introduction

The ConTXT Backend represents a cutting-edge enterprise-grade system designed for AI-driven context engineering. This sophisticated platform seamlessly processes documents, extracts knowledge, and integrates with multiple large language models (LLMs) to provide intelligent context generation for modern AI applications.

ConTXT addresses the critical challenge of context optimization in AI workflows by providing a unified platform that can understand, process, and synthesize information from diverse sources into actionable insights.

### Key Benefits

- **Intelligence Amplification**: Enhances human decision-making through advanced AI processing
- **Multi-Modal Processing**: Handles text, images, documents, and structured data uniformly
- **Scalable Architecture**: Designed to handle enterprise-level workloads
- **Cost Optimization**: Intelligent provider selection reduces operational costs
- **Security-First**: Built with enterprise security requirements in mind

---

## Project Overview

### Mission Statement

To democratize access to advanced AI context engineering capabilities, enabling organizations to extract maximum value from their data assets through intelligent processing and knowledge synthesis.

### Core Objectives

1. **Document Intelligence**: Transform unstructured data into structured knowledge
2. **Context Optimization**: Generate optimal contexts for AI model consumption
3. **Knowledge Discovery**: Uncover hidden relationships and insights in data
4. **Workflow Automation**: Streamline AI-powered business processes
5. **Cost Efficiency**: Optimize AI resource utilization across multiple providers

### Features

#### Document Processing Engine
- **Multi-Format Support**: Processes text, markdown, JSON, CSV, PDF, HTML, images, and code files
- **Intelligent Parsing**: Context-aware extraction of content and metadata
- **Quality Assessment**: Automatic quality scoring and content validation
- **Batch Processing**: Efficient handling of large document collections
- **Real-time Processing**: Stream processing for time-sensitive applications

#### Knowledge Graph Management
- **Neo4j Integration**: Advanced graph database operations
- **Relationship Discovery**: Automatic identification of entity relationships
- **Semantic Linking**: Context-aware entity disambiguation
- **Graph Analytics**: Complex queries and pattern recognition
- **Knowledge Evolution**: Dynamic graph updates and versioning

#### Vector Search Capabilities
- **Qdrant Integration**: High-performance vector similarity search
- **Multi-Vector Support**: Multiple embedding models for different use cases
- **Hybrid Search**: Combining semantic and keyword-based search
- **Real-time Indexing**: Immediate availability of processed content
- **Similarity Tuning**: Configurable similarity thresholds and algorithms

#### Multi-Provider LLM Orchestra
- **Provider Diversity**: OpenAI, Anthropic, Google, XAI/Grok, Groq, Mistral, and more
- **Intelligent Routing**: Task-aware provider selection
- **Fallback Systems**: Automatic failover for reliability
- **Cost Optimization**: Real-time cost analysis and optimization
- **Performance Monitoring**: Provider latency and quality tracking

#### Enterprise Infrastructure
- **Containerization**: Full Docker containerization for easy deployment
- **Microservices**: Modular architecture for scalability
- **Async Processing**: Celery-based task queue for background operations
- **Load Balancing**: Built-in load distribution capabilities
- **Health Monitoring**: Comprehensive system health checks

---

## Architecture

### System Overview

The ConTXT Backend follows a microservices architecture pattern, designed for scalability, maintainability, and fault tolerance. The system is composed of several interconnected services that work together to provide comprehensive AI context engineering capabilities.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Load Balancer │    │   API Gateway   │
│   Application   │◄──►│   (nginx/traefik)│◄──►│   (FastAPI)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                        ┌───────────────────────────────┼───────────────────────────────┐
                        │                               │                               │
                        ▼                               ▼                               ▼
              ┌─────────────────┐            ┌─────────────────┐            ┌─────────────────┐
              │   Auth Service  │            │ Context Engine  │            │ Ingestion Mgr   │
              │                 │            │                 │            │                 │
              └─────────────────┘            └─────────────────┘            └─────────────────┘
                        │                               │                               │
                        ▼                               ▼                               ▼
              ┌─────────────────┐            ┌─────────────────┐            ┌─────────────────┐
              │   PostgreSQL    │            │     Neo4j       │            │     Qdrant      │
              │   (User Data)   │            │ (Knowledge Graph)│            │ (Vector Store)  │
              └─────────────────┘            └─────────────────┘            └─────────────────┘
                                                        │                               │
                                                        ▼                               ▼
                                              ┌─────────────────┐            ┌─────────────────┐
                                              │     Redis       │            │     Celery      │
                                              │    (Cache)      │            │   (Task Queue)  │
                                              └─────────────────┘            └─────────────────┘
                                                        │
                                                        ▼
                                              ┌─────────────────┐
                                              │   LLM Providers │
                                              │  (OpenAI, etc.) │
                                              └─────────────────┘
```

### Component Breakdown

#### API Layer
- **FastAPI Framework**: High-performance async API framework
- **Auto-documentation**: Swagger/OpenAPI integration
- **Request Validation**: Pydantic-based request/response validation
- **Middleware Stack**: CORS, security headers, rate limiting
- **WebSocket Support**: Real-time communication capabilities

#### Business Logic Layer
- **Service Classes**: Encapsulated business logic
- **Domain Models**: Rich domain objects with behavior
- **Use Cases**: Application-specific orchestration
- **Event Handling**: Domain event processing
- **Validation Rules**: Business rule enforcement

#### Data Access Layer
- **Repository Pattern**: Abstracted data access
- **Connection Pooling**: Efficient database connections
- **Transaction Management**: ACID property guarantees
- **Caching Strategy**: Multi-level caching implementation
- **Data Consistency**: Eventual consistency handling

#### External Integrations
- **LLM Provider APIs**: Unified interface for multiple providers
- **Email Services**: Transactional email handling
- **File Storage**: Local and cloud storage options
- **Monitoring Services**: Application performance monitoring
- **Authentication Providers**: OAuth2/OIDC integration

---

## Setup and Installation

### System Requirements

#### Minimum Requirements
- **CPU**: 4 cores, 2.4GHz
- **RAM**: 8GB
- **Storage**: 20GB available space
- **Network**: Stable internet connection for API access
- **OS**: Linux (Ubuntu 20.04+), macOS (10.15+), Windows 10+

#### Recommended Requirements
- **CPU**: 8 cores, 3.0GHz+
- **RAM**: 16GB+
- **Storage**: 100GB SSD
- **Network**: High-bandwidth connection for large document processing
- **OS**: Linux (Ubuntu 22.04 LTS)

### Prerequisites

Before installation, ensure these components are available:

#### Required Software
- **Docker**: Version 20.10+ with Docker Compose V2
- **Git**: Version control system
- **Python**: 3.11+ (for local development)
- **Node.js**: 18+ (for frontend integration)

#### API Keys and Credentials
- **OpenAI API Key**: For GPT model access
- **Anthropic API Key**: For Claude model access
- **Google AI API Key**: For Gemini model access
- **XAI API Key**: For Grok model access
- **Email Service**: Resend, SendGrid, or SMTP credentials

### Docker-Based Installation

The recommended installation method uses Docker for containerization:

#### Step 1: Repository Setup

```bash
# Clone the repository
git clone https://github.com/your_username/ConTXT.git
cd ConTXT/Backend

# Verify directory structure
ls -la
```

#### Step 2: Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
vim .env
```

**Essential Environment Variables:**

```bash
# Core Application
APP_NAME="ConTXT Backend"
APP_VERSION="1.0.0"
ENVIRONMENT="development"
DEBUG=true
LOG_LEVEL="INFO"

# Security
SECRET_KEY="your-secret-key-here"
JWT_SECRET_KEY="your-jwt-secret-here"
JWT_ALGORITHM="HS256"
JWT_EXPIRE_MINUTES=30
JWT_REFRESH_EXPIRE_DAYS=7

# Database Configuration
POSTGRES_HOST="postgres"
POSTGRES_PORT=5432
POSTGRES_DB="contxt_db"
POSTGRES_USER="contxt_user"
POSTGRES_PASSWORD="secure_password"

NEO4J_URI="bolt://neo4j:7687"
NEO4J_USERNAME="neo4j"
NEO4J_PASSWORD="password"

QDRANT_HOST="qdrant"
QDRANT_PORT=6333

REDIS_URL="redis://redis:6379/0"

# LLM Provider APIs
OPENAI_API_KEY="your-openai-key"
ANTHROPIC_API_KEY="your-anthropic-key"
GOOGLE_API_KEY="your-google-key"
XAI_API_KEY="your-xai-key"
GROQ_API_KEY="your-groq-key"
MISTRAL_API_KEY="your-mistral-key"

# Email Configuration
RESEND_API_KEY="your-resend-key"
FROM_EMAIL="<EMAIL>"

# Feature Flags
ENABLE_AI_ENHANCEMENT=true
ENABLE_COGNEE_INTEGRATION=true
ENABLE_RATE_LIMITING=true
ENABLE_METRICS=true
```

#### Step 3: Container Orchestration

```bash
# Start all services
docker-compose up -d

# Verify service status
docker-compose ps

# Check logs
docker-compose logs -f app
```

#### Step 4: Service Verification

```bash
# Health check script
./scripts/test_docker_system.py

# Manual verification
curl http://localhost:8000/health
```

### Native Installation (Development)

For development purposes, you may prefer a native installation:

#### Step 1: Python Environment

```bash
# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate  # Windows

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt
```

#### Step 2: Database Setup

```bash
# Start only databases
docker-compose up -d postgres neo4j qdrant redis

# Run database migrations
python scripts/setup_database.py
```

#### Step 3: Application Startup

```bash
# Start development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Service Endpoints

After successful installation, these services will be available:

- **API Documentation**: http://localhost:8000/docs
- **API Alternative Docs**: http://localhost:8000/redoc
- **Neo4j Browser**: http://localhost:7474 (neo4j/password)
- **Qdrant Dashboard**: http://localhost:6333/dashboard
- **Celery Flower**: http://localhost:5555 (if enabled)
- **Prometheus Metrics**: http://localhost:8000/metrics

---

## API Documentation

The ConTXT Backend provides a comprehensive RESTful API designed for enterprise integration. All endpoints follow OpenAPI 3.0 specifications and include detailed documentation.

### Authentication Flow

All API endpoints (except public ones) require JWT authentication:

```http
Authorization: Bearer <your-jwt-token>
```

### Base URL Structure

```
https://api.contxt.com/api/v1/{endpoint}
```

### Response Format

All API responses follow a consistent structure:

```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req_123456789"
}
```

### Authentication Endpoints

#### User Registration

**Endpoint**: `POST /auth/register`

**Description**: Register a new user account with email verification.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "first_name": "John",
  "last_name": "Doe",
  "company": "ACME Corp",
  "role": "developer"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Registration successful. Please check your email for verification.",
  "data": {
    "user_id": "usr_123456789",
    "verification_required": true
  }
}
```

**Security Features**:
- Password strength validation
- Email format validation
- Rate limiting (5 requests per hour per IP)
- Duplicate email prevention
- Automatic verification email

#### User Login

**Endpoint**: `POST /auth/login`

**Description**: Authenticate user and receive JWT tokens.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "remember_me": false
}
```

**Response**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": "usr_123456789",
    "email": "<EMAIL>",
    "first_name": "John",
    "subscription_tier": "pro"
  }
}
```

**Security Features**:
- Bcrypt password hashing
- JWT token generation
- Session tracking
- Failed attempt lockout
- IP-based rate limiting

#### Token Refresh

**Endpoint**: `POST /auth/refresh-token`

**Description**: Refresh expired access token using refresh token.

**Request Body**:
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

#### Email Verification

**Endpoint**: `POST /auth/verify-email-otp`

**Description**: Verify user email using OTP code.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "verified": true,
    "verified_at": "2024-01-01T12:00:00Z"
  }
}
```

### Document Ingestion Endpoints

#### URL Ingestion

**Endpoint**: `POST /ingestion/url`

**Description**: Process content from a URL and extract knowledge.

**Request Body**:
```json
{
  "url": "https://example.com/article",
  "metadata": {
    "source": "web",
    "category": "research",
    "tags": ["ai", "technology"]
  },
  "options": {
    "use_cognee": true,
    "enable_ai": true,
    "extract_images": false,
    "follow_links": false,
    "max_depth": 1
  }
}
```

**Response**:
```json
{
  "job_id": "job_987654321",
  "status": "processing",
  "estimated_completion": "2024-01-01T12:05:00Z",
  "processing_steps": [
    "url_validation",
    "content_extraction",
    "ai_enhancement",
    "knowledge_indexing"
  ]
}
```

#### File Upload

**Endpoint**: `POST /ingestion/file`

**Description**: Upload and process document files.

**Request** (multipart/form-data):
- `file`: Document file (PDF, DOCX, TXT, etc.)
- `metadata`: JSON string with metadata
- `use_cognee`: Boolean flag
- `enable_ai`: Boolean flag
- `dataset_name`: Optional dataset identifier

**Response**:
```json
{
  "job_id": "job_987654322",
  "status": "processing",
  "file_info": {
    "filename": "document.pdf",
    "size": 1048576,
    "type": "application/pdf",
    "pages": 25
  }
}
```

#### Text Processing

**Endpoint**: `POST /ingestion/text`

**Description**: Process raw text content directly.

**Request Body**:
```json
{
  "text": "This is the content to be processed...",
  "metadata": {
    "title": "Sample Text",
    "author": "John Doe",
    "created_at": "2024-01-01T12:00:00Z"
  },
  "options": {
    "enable_ai": true,
    "enhancement_type": "analysis",
    "extract_entities": true
  }
}
```

**Response**:
```json
{
  "job_id": "job_987654323",
  "status": "processing",
  "content_info": {
    "character_count": 1250,
    "word_count": 200,
    "estimated_tokens": 300
  }
}
```

#### Privacy-Compliant Processing

**Endpoint**: `POST /ingestion/privacy`

**Description**: Process content with automatic PII redaction.

**Request Body**:
```json
{
  "content": "John Doe's <NAME_EMAIL> and phone is 555-1234",
  "content_type": "text",
  "redact_pii": true,
  "pii_types": ["email", "phone", "ssn", "credit_card"],
  "metadata": {
    "privacy_level": "high",
    "retention_policy": "30_days"
  }
}
```

**Response**:
```json
{
  "job_id": "job_987654324",
  "status": "processing",
  "privacy_info": {
    "pii_detected": true,
    "pii_types_found": ["email", "phone"],
    "redaction_applied": true
  }
}
```

#### Ingestion Status

**Endpoint**: `GET /ingestion/status/{job_id}`

**Description**: Check the status of an ingestion job.

**Response**:
```json
{
  "job_id": "job_987654321",
  "status": "completed",
  "progress": 100,
  "started_at": "2024-01-01T12:00:00Z",
  "completed_at": "2024-01-01T12:03:30Z",
  "results": {
    "documents_created": 1,
    "entities_extracted": 45,
    "relationships_found": 23,
    "embeddings_generated": true
  },
  "errors": []
}
```

### Context Engineering Endpoints

#### Build Context

**Endpoint**: `POST /context/build`

**Description**: Build optimized context from multiple sources.

**Request Body**:
```json
{
  "sources": [
    {
      "type": "document",
      "id": "doc_123",
      "weight": 1.0
    },
    {
      "type": "search_query",
      "query": "machine learning algorithms",
      "weight": 0.8
    }
  ],
  "max_tokens": 128000,
  "compression_ratio": 0.5,
  "optimization_strategy": "relevance",
  "include_metadata": true
}
```

**Response**:
```json
{
  "context_id": "ctx_456789123",
  "status": "completed",
  "context": {
    "content": "Optimized context content...",
    "token_count": 45000,
    "compression_achieved": 0.52,
    "relevance_score": 0.89
  },
  "sources_used": 15,
  "processing_time_ms": 2340
}
```

#### Generate System Prompt

**Endpoint**: `POST /context/generate-system-prompt`

**Description**: Generate AI tool-specific system prompts.

**Request Body**:
```json
{
  "context_id": "ctx_456789123",
  "tool_type": "cursor",
  "parameters": {
    "language": "python",
    "framework": "fastapi",
    "style": "enterprise",
    "include_examples": true
  }
}
```

**Response**:
```json
{
  "system_prompt": "You are an expert Python developer...",
  "tool_config": {
    "cursor_rules": "...",
    "file_patterns": ["*.py", "*.yaml"],
    "ignore_patterns": ["__pycache__", "*.pyc"]
  },
  "metadata": {
    "generated_at": "2024-01-01T12:00:00Z",
    "version": "1.0",
    "context_id": "ctx_456789123"
  }
}
```

### Knowledge Management Endpoints

#### Search Knowledge

**Endpoint**: `POST /knowledge/search`

**Description**: Perform semantic search across the knowledge base.

**Request Body**:
```json
{
  "query": "machine learning best practices",
  "search_type": "hybrid",
  "filters": {
    "content_type": ["document", "article"],
    "date_range": {
      "start": "2024-01-01",
      "end": "2024-12-31"
    },
    "tags": ["ai", "ml"]
  },
  "limit": 20,
  "include_snippets": true
}
```

**Response**:
```json
{
  "results": [
    {
      "id": "doc_789",
      "title": "Machine Learning Best Practices Guide",
      "relevance_score": 0.95,
      "snippet": "Best practices for machine learning include...",
      "metadata": {
        "author": "Jane Smith",
        "created_at": "2024-01-15T10:00:00Z",
        "tags": ["ml", "best-practices"]
      }
    }
  ],
  "total_results": 147,
  "search_time_ms": 23,
  "query_understanding": {
    "intent": "information_seeking",
    "entities": ["machine learning"],
    "confidence": 0.92
  }
}
```

#### Graph Query

**Endpoint**: `POST /knowledge/query`

**Description**: Execute complex graph queries using Cypher or natural language.

**Request Body**:
```json
{
  "query": "Find all documents related to 'artificial intelligence' and their authors",
  "query_type": "natural_language",
  "return_format": "json",
  "limit": 50,
  "visualization": true
}
```

**Response**:
```json
{
  "results": {
    "nodes": [
      {
        "id": "doc_123",
        "labels": ["Document"],
        "properties": {
          "title": "AI Ethics Guide",
          "created_at": "2024-01-01T12:00:00Z"
        }
      },
      {
        "id": "author_456",
        "labels": ["Author"],
        "properties": {
          "name": "Dr. Alice Johnson",
          "expertise": "AI Ethics"
        }
      }
    ],
    "relationships": [
      {
        "id": "rel_789",
        "type": "AUTHORED_BY",
        "start_node": "doc_123",
        "end_node": "author_456",
        "properties": {}
      }
    ]
  },
  "execution_time_ms": 45,
  "cypher_query": "MATCH (d:Document)-[:AUTHORED_BY]->(a:Author)..."
}
```

---

## Core Components

### Overview of Modules
Each module in the system is designed to manage different aspects of the backend functionality.

#### `main.py`

- Initializes the FastAPI application.
- Configures middleware for security and CORS.
- Manages application lifespan, including database connection initialization and cleanup.

#### Router Setup (`router.py`)

- Includes all API endpoint routers.
- Manages routing for context, ingestion, authentication, and more.

### Core Business Logic

#### Enhanced AI Layer (`enhanced_ai_layer.py`)

This module serves as the center for AI enhancements, integrating with multi-provider LLM systems.

1. **Initialization**
   - Initializes the enhanced AI infrastructure with provider selection and preference.

2. **Content Enhancement**
   - Enhances content using various AI techniques like analysis, summarization, extraction.

3. **Provider Management**
   - Manages provider selection, fallback systems, and monitors costs for efficiency.

#### Context Engine (`context_engine.py`)

Responsible for building and maintaining AI contexts:

- Processes input sources and builds optimized contexts for AI consumption.
- Provides a framework to check status and ongoing operations.

#### Ingestion Manager (`ingestion.py`)

Manages ingestion of various data types:

- Content ingestion from URLs, files, and text.
- Optional privacy compliance through automatic PII redaction.

---

## Advanced Features

### Multi-Provider LLM Integration

The backend supports multiple LLM providers, facilitating flexible language processing:

- **Unified Interface**: Offers a single interface to access different LLMs.
- **Intelligent Provider Selection**: Optimizes provider choice based on task type and cost.
- **Fallback Mechanisms & Cost Optimization**: Automatic fallback ensures reliability, while cost strategies enhance efficiency.

### Security

- **JWT Tokens**: Utilizes JWT for secure user authentication and session management.
- **Email Verification**: Enforces email verification for all user registrations.
- **Rate Limiting**: Protects against brute force attacks by limiting access rates.

### Configuration

- **Config Files**: Contains all configuration settings for environment variables, default providers, etc.
- **Provider Configurations**: Settings for provider management, optimization rules, and fallback setups.

---

## Development Practices

### Code Structure

- **App Module**: Contains core application code such as APIs, routers, and core logic.
- **Configurations**: Manages various application and provider settings.
- **Utilities**: Includes helper functions and shared utilities.

### Testing

- Comprehensive test scripts ensure the reliability and integrity of the system.
- Uses pytest for running tests, with extensions for asyncio tasks.

```bash
pytest tests/test_ingestion.py
```

---

## Roadmap and Future Enhancements

### Missing Features

- **Complete Authentication System**: Finalize user management and security features like password resets and profile updating.
- **Subscription Management**: Implement subscription and billing integrations.
- **Project Management**: Enhance project CRUD operations within the application.

### Scalability Plans

- **Performance Optimization**: Further enhance database performance and query handling.
- **Enhanced Security Measures**: Plan to implement more robust auditing and monitoring tools.
- **Global Availability**: Expand multi-region deployments using container orchestration.

---

## Troubleshooting

- **Docker Issues**: Ensure correct Docker and Compose versions.
- **Database Connections**: Verify all database services are up and configured correctly.
- **File Permissions**: Confirm proper permissions for file uploads and downloads.

---

## Community and Contributions

Contributions to the ConTXT project are welcome and valued! Please follow our contribution guidelines set out in the project's GitHub repository.

