# ConTXT Backend Architecture Documentation

## 1. System Overview

The ConTXT Backend is an enterprise-grade AI context engineering system designed to process documents, extract knowledge, and generate optimized contexts for AI consumption. Built with Python and FastAPI, it follows a microservices architecture pattern for scalability, maintainability, and fault tolerance.

### 1.1 Mission Statement

To democratize access to advanced AI context engineering capabilities, enabling organizations to extract maximum value from their data assets through intelligent processing and knowledge synthesis.

### 1.2 Core Objectives

1. **Document Intelligence**: Transform unstructured data into structured knowledge
2. **Context Optimization**: Generate optimal contexts for AI model consumption
3. **Knowledge Discovery**: Uncover hidden relationships and insights in data
4. **Workflow Automation**: Streamline AI-powered business processes
5. **Cost Efficiency**: Optimize AI resource utilization across multiple providers

## 2. Architecture Patterns

### 2.1 Microservices Architecture

The system is composed of several interconnected services that work together to provide comprehensive AI context engineering capabilities:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Load Balancer │    │   API Gateway   │
│   Application   │◄──►│   (nginx/traefik)│◄──►│   (FastAPI)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                        ┌───────────────────────────────┼───────────────────────────────┐
                        │                               │                               │
                        ▼                               ▼                               ▼
              ┌─────────────────┐            ┌─────────────────┐            ┌─────────────────┐
              │   Auth Service  │            │ Context Engine  │            │ Ingestion Mgr   │
              │                 │            │                 │            │                 │
              └─────────────────┘            └─────────────────┘            └─────────────────┘
                        │                               │                               │
                        ▼                               ▼                               ▼
              ┌─────────────────┐            ┌─────────────────┐            ┌─────────────────┐
              │   PostgreSQL    │            │     Neo4j       │            │     Qdrant      │
              │   (User Data)   │            │ (Knowledge Graph)│            │ (Vector Store)  │
              └─────────────────┘            └─────────────────┘            └─────────────────┘
                                                        │                               │
                                                        ▼                               ▼
                                              ┌─────────────────┐            ┌─────────────────┐
                                              │     Redis       │            │     Celery      │
                                              │    (Cache)      │            │   (Task Queue)  │
                                              └─────────────────┘            └─────────────────┘
                                                        │
                                                        ▼
                                              ┌─────────────────┐
                                              │   LLM Providers │
                                              │  (OpenAI, etc.) │
                                              └─────────────────┘
```

### 2.2 Container-Based Deployment

The system is fully containerized using Docker for easy deployment and development:

| Container | Image | Purpose | Ports |
|-----------|-------|---------|-------|
| api-1 | backend-api | FastAPI backend serving REST API endpoints | 8000 |
| worker-1 | backend-worker | Celery worker for processing background tasks | - |
| flower-1 | backend-flower | Celery monitoring interface | 5555 |
| postgres-1 | postgres:15 | Relational database for structured data | 5432 |
| redis-1 | redis:7 | Cache and message broker | 6379 |
| neo4j-1 | neo4j:5 | Graph database for knowledge representation | 7474, 7687 |
| qdrant-1 | qdrant/qdrant:latest | Vector database for embeddings | 6333, 6334 |

## 3. Component Breakdown

### 3.1 API Layer

The API layer is built with FastAPI, providing:

- High-performance async API framework
- Auto-generated Swagger/OpenAPI documentation
- Pydantic-based request/response validation
- Middleware stack for CORS, security headers, and rate limiting
- WebSocket support for real-time communication

### 3.2 Business Logic Layer

This layer contains the core application services:

- **Service Classes**: Encapsulated business logic for each domain
- **Domain Models**: Rich domain objects with behavior
- **Use Cases**: Application-specific orchestration
- **Event Handling**: Domain event processing
- **Validation Rules**: Business rule enforcement

### 3.3 Data Access Layer

The data access layer provides:

- Repository pattern for abstracted data access
- Connection pooling for efficient database connections
## Core Services

The ConTXT backend implements several core services that handle specific functional areas of the system:

### 1. Authentication Service

The authentication service (`app/core/auth_service.py`) handles all user authentication and authorization functions:
- User registration with email verification
- Secure login with JWT token generation
- Password reset functionality with OTP verification
- Email verification workflows
- Session management with refresh tokens
- API key management (planned)
- Account lockout mechanisms for security
- Comprehensive authentication event logging

### 2. Ingestion Manager

The ingestion manager (`app/core/ingestion.py`) handles processing various data sources:
- URL content ingestion with HTTP fetching
- File upload processing with multiple format support
- Raw text ingestion
- Privacy-compliant ingestion with PII redaction
- Asynchronous job processing with status tracking
- Integration with document processors for content extraction
- Support for AI-enhanced processing features
- Cognee integration for alternative database operations

### 3. Context Engine

## Database Integrations

The ConTXT backend uses multiple specialized databases for different purposes:

### 1. PostgreSQL

PostgreSQL (`app/db/postgres_client.py`) serves as the primary relational database for structured data:
- User authentication and session management
- User preferences and settings
- Document metadata and processing status
- Audit logs and system events
- Connection pooling for efficient resource usage
- Health checks and error handling

### 2. Neo4j

Neo4j (`app/db/neo4j_client.py`) is used as a graph database for knowledge representation:
- Knowledge graph storage with nodes and relationships
- Semantic relationships between entities
- Graph traversal and querying capabilities
- Integration with APOC procedures for advanced operations
- Visualization support for graph exploration

### 3. Qdrant

Qdrant (`app/db/qdrant_client.py`) provides vector search capabilities:
- Vector storage and similarity search
- Document embeddings for semantic search
- Filtering and metadata storage
- Collection management and configuration
## API Endpoints

The ConTXT backend exposes a comprehensive REST API with the following endpoint groups:

### 1. Authentication Endpoints (`/api/auth`)

Comprehensive user authentication and management:
- **POST `/register`** - User registration with email verification
- **POST `/login`** - User authentication with JWT token generation
- **POST `/refresh-token`** - Refresh access tokens using refresh tokens
- **POST `/logout`** - User logout and session invalidation
- **POST `/verify-email-otp`** - Email verification with OTP codes
- **POST `/resend-verification-otp`** - Resend email verification codes
- **POST `/request-password-reset-otp`** - Request password reset OTP
- **POST `/reset-password-with-otp`** - Reset password with OTP verification
- **POST `/change-password`** - Authenticated password changes
- **GET `/me`** - Retrieve current user profile
- **PUT `/me`** - Update user profile information
- **POST `/api-keys`** - Create new API keys for programmatic access
- **GET `/api-keys`** - List user's API keys
- **DELETE `/api-keys/{key_id}`** - Delete/deactivate API keys
- **GET `/health`** - Authentication service health check

### 2. Ingestion Endpoints (`/api/ingestion`)

Data ingestion from various sources:
- **POST `/url`** - Ingest content from URLs
- **POST `/file`** - Ingest uploaded files (PDF, text, etc.)
- **POST `/text`** - Ingest raw text content
- **POST `/privacy`** - Ingest content with privacy compliance (PII redaction)
- **GET `/status/{job_id}`** - Check ingestion job status
- **GET `/enhancement-options`** - Get available enhancement options

### 3. Context Engineering Endpoints (`/api/context`)

Context curation and system prompt generation:
- **POST `/build`** - Build engineered context from sources
- **POST `/generate-system-prompt`** - Generate system prompts for AI tools
- **GET `/status/{context_id}`** - Check context building status

### 4. Knowledge Graph Endpoints (`/api/knowledge`)

Knowledge graph operations and visualization:
- **POST `/query`** - Query the knowledge graph
- **POST `/entity`** - Add entities to the knowledge graph
- **POST `/relationship`** - Add relationships between entities
- **GET `/stats`** - Get knowledge graph statistics
- **GET `/graph/nodes`** - Retrieve nodes for visualization
- **GET `/graph/relationships`** - Retrieve relationships for visualization
- **GET `/graph/full`** - Get complete graph data for visualization
- **GET `/graph/node/{node_id}/neighbors`** - Get neighbors of a specific node
- **GET `/analytics/stats`** - Get graph analytics and statistics
- Scalable vector database operations

### 4. Redis

Redis is used for caching and message brokering:
- Celery task broker for background processing
- Task result storage for distributed workers
- Caching layer for improved performance
## Configuration and Settings

The ConTXT backend uses a comprehensive configuration system based on Pydantic settings with environment variable support:

### Environment Configuration

- **ENV/ENVIRONMENT**: Application environment (dev, staging, prod)
- **DEBUG**: Debug mode toggle
- **LOG_LEVEL**: Logging level (debug, info, warning, error)
- **LOG_FORMAT**: Log format (json, text)
- **LOG_FILE**: Log file path

### Database Configuration

#### PostgreSQL
- **DB_HOST**: Database host (default: postgres)
- **DB_PORT**: Database port (default: 5432)
- **DB_USERNAME**: Database username (default: postgres)
- **DB_PASSWORD**: Database password (default: postgres)
- **DB_NAME**: Database name (default: document_processor)
- **DB_SSL_MODE**: SSL mode (default: disable)

#### Neo4j
- **NEO4J_HOST**: Neo4j host (default: neo4j)
- **NEO4J_PORT**: Neo4j port (default: 7687)
- **NEO4J_USER**: Neo4j username (default: neo4j)
- **NEO4J_PASSWORD**: Neo4j password (default: password)
- **NEO4J_URI**: Neo4j connection URI (default: bolt://neo4j:7687)

#### Qdrant
- **QDRANT_HOST**: Qdrant host (default: qdrant)
- **QDRANT_PORT**: Qdrant port (default: 6333)
- **QDRANT_COLLECTION**: Qdrant collection name (default: context_vectors)
- **VECTOR_DB_URL**: Vector database URL (default: http://qdrant:6333)

#### Redis
- **REDIS_URL**: Redis connection URL (default: redis://redis:6379/0)
- **CELERY_BROKER_URL**: Celery broker URL (default: redis://redis:6379/0)
- **CELERY_RESULT_BACKEND**: Celery result backend (default: redis://redis:6379/0)

### LLM Configuration

Multi-provider LLM support with API keys for various services:
- **OPENAI_API_KEY**: OpenAI API key
- **ANTHROPIC_API_KEY**: Anthropic API key
- **XAI_API_KEY**: X.AI API key
- **GOOGLE_API_KEY**: Google API key
- **MISTRAL_API_KEY**: Mistral API key
- **AZURE_OPENAI_API_KEY**: Azure OpenAI API key
- **OLLAMA_API_KEY**: Ollama API key

Model configuration:
- **LLM_PROVIDER**: Default LLM provider (default: openai)
- **LLM_MODEL**: Default LLM model (default: gpt-4o)
- **DEFAULT_LLM_MODEL**: Fallback LLM model (default: gpt-4o)
- **EMBEDDING_MODEL**: Text embedding model (default: text-embedding-3-large)

### Security Configuration

- **SECRET_KEY**: Application secret key
- **JWT_SECRET_KEY**: JWT signing key
- **JWT_ALGORITHM**: JWT algorithm (default: HS256)
- **ACCESS_TOKEN_EXPIRE_MINUTES**: Access token expiration (default: 30)
- **REFRESH_TOKEN_EXPIRE_DAYS**: Refresh token expiration (default: 7)
- **ENABLE_HSTS**: HTTP Strict Transport Security toggle
- **CSP_POLICY_OVERRIDES**: Content Security Policy overrides

### Email Configuration

- **EMAIL_PROVIDER**: Email provider (console, sendgrid, ses, resend)
- **SENDGRID_API_KEY**: SendGrid API key
- **SENDGRID_FROM_EMAIL**: Sender email address
- **AWS_SES_REGION**: AWS SES region
- **AWS_ACCESS_KEY_ID**: AWS access key
- **AWS_SECRET_ACCESS_KEY**: AWS secret key
- **RESEND_API_KEY**: Resend API key

### Rate Limiting

- **RATE_LIMIT_ENABLED**: Enable rate limiting (default: true)
- **LOGIN_RATE_LIMIT**: Login rate limit (default: 5/minute)
- **REGISTER_RATE_LIMIT**: Registration rate limit (default: 3/hour)
- **VERIFY_EMAIL_RATE_LIMIT**: Email verification rate limit (default: 5/minute)
- **PASSWORD_RESET_RATE_LIMIT**: Password reset rate limit (default: 3/hour)

### File Processing

- **MAX_FILE_SIZE**: Maximum file size (default: 100MB)
- **UPLOAD_PATH**: File upload directory
- **PROCESSED_PATH**: Processed files directory
- **CHUNK_SIZE**: Text chunk size (default: 1024)
- **CHUNK_OVERLAP**: Text chunk overlap (default: 128)
- **SUPPORTED_FILE_TYPES**: Supported file extensions
- Session storage (planned)
- Rate limiting (planned)
The context engine (`app/core/context_engine.py`) implements the core context engineering functionality:
- Content selection from various sources
- Context compression and optimization
- Context ordering and structuring
- System prompt generation for different tools
- Integration with LangGraph for orchestrated workflows
- Context window management for LLM interactions

### 4. Knowledge Graph Manager

The knowledge graph manager (`app/core/knowledge_graph.py`) handles operations on the Neo4j knowledge graph:
- Node and relationship retrieval for visualization
- Graph analytics and statistics
- Neighbor discovery with configurable depth
- Graph querying capabilities
- Transaction management with ACID property guarantees
- Multi-level caching implementation
- Eventual consistency handling

### 3.4 External Integrations

The system integrates with various external services:

- **LLM Provider APIs**: Unified interface for multiple providers
- **Email Services**: Transactional email handling
- **File Storage**: Local and cloud storage options
- **Monitoring Services**: Application performance monitoring
- **Authentication Providers**: OAuth2/OIDC integration

## 4. Key Features

### 4.1 Document Processing Engine

- **Multi-Format Support**: Processes text, markdown, JSON, CSV, PDF, HTML, images, and code files
- **Intelligent Parsing**: Context-aware extraction of content and metadata
- **Quality Assessment**: Automatic quality scoring and content validation
- **Batch Processing**: Efficient handling of large document collections
- **Real-time Processing**: Stream processing for time-sensitive applications

### 4.2 Knowledge Graph Management

- **Neo4j Integration**: Advanced graph database operations
- **Relationship Discovery**: Automatic identification of entity relationships
- **Semantic Linking**: Context-aware entity disambiguation
- **Graph Analytics**: Complex queries and pattern recognition
## Document Processors and Handling

The ConTXT backend implements a flexible document processing system with support for multiple file formats and AI-enhanced processing capabilities:

### Processor Architecture

The document processing system is built around a factory pattern that dynamically selects the appropriate processor based on file type or content:

1. **BaseProcessor**: Abstract base class defining the processor interface
2. **Specialized Processors**: Format-specific implementations for text, PDF, images, code, etc.
3. **MultiProviderProcessor**: Enhanced processor with multi-provider LLM support
4. **PrivacyCompliantProcessor**: Special processor for privacy-sensitive content

### Supported File Formats

The system supports processing of various document types:
- **Text Documents**: Plain text, Markdown, JSON, CSV
- **Office Documents**: PDF files
- **Web Content**: HTML documents
- **Images**: PNG, JPEG, GIF, BMP, WebP
- **Code Files**: Python, JavaScript, TypeScript, Java, C/C++, Go, Rust, Ruby, PHP, Swift, Kotlin, C#

### Processing Pipeline

Document processing follows a standardized pipeline:
1. **Content Extraction**: Extract text content from source documents
2. **Chunking**: Split large documents into manageable chunks
3. **Embedding Generation**: Create vector embeddings for semantic search
4. **Knowledge Graph Storage**: Store document metadata and relationships
5. **Vector Database Storage**: Store embeddings for similarity search
6. **AI Enhancement**: Optional AI analysis and processing

### AI-Enhanced Processing

Processors can be configured with AI enhancements:
- **Content Analysis**: Extract key topics, themes, and insights
- **Summarization**: Generate concise document summaries
- **Entity Extraction**: Identify and extract important entities
- **Classification**: Categorize documents by type or topic
- **Translation**: Translate content between languages

### Database Integration

Processed documents are stored in multiple databases:
- **Neo4j**: Document metadata, relationships, and graph structure
- **Qdrant**: Vector embeddings for semantic search
- **Cognee** (optional): Alternative knowledge graph and vector storage
- **Knowledge Evolution**: Dynamic graph updates and versioning

### 4.3 Vector Search Capabilities

- **Qdrant Integration**: High-performance vector similarity search
- **Multi-Vector Support**: Multiple embedding models for different use cases
- **Hybrid Search**: Combining semantic and keyword-based search
- **Real-time Indexing**: Immediate availability of processed content
- **Similarity Tuning**: Configurable similarity thresholds and algorithms

### 4.4 Multi-Provider LLM Orchestra

- **Provider Diversity**: OpenAI, Anthropic, Google, XAI/Grok, Groq, Mistral, and more
- **Intelligent Routing**: Task-aware provider selection
- **Fallback Systems**: Automatic failover for reliability
- **Cost Optimization**: Real-time cost analysis and optimization
- **Performance Monitoring**: Provider latency and quality tracking

### 4.5 Enterprise Infrastructure

- **Containerization**: Full Docker containerization for easy deployment
- **Microservices**: Modular architecture for scalability
- **Async Processing**: Celery-based task queue for background operations
- **Load Balancing**: Built-in load distribution capabilities
- **Health Monitoring**: Comprehensive system health checks

## 5. System Requirements

### 5.1 Minimum Requirements

- **CPU**: 4 cores, 2.4GHz
- **RAM**: 8GB
- **Storage**: 20GB available space
- **Network**: Stable internet connection for API access
- **OS**: Linux (Ubuntu 20.04+), macOS (10.15+), Windows 10+

### 5.2 Recommended Requirements

- **CPU**: 8 cores, 3.0GHz+
- **RAM**: 16GB+
- **Storage**: 100GB SSD
- **Network**: High-bandwidth connection for large document processing
- **OS**: Linux (Ubuntu 22.04 LTS)

## 6. Technology Stack

### 6.1 Core Technologies

- **Python 3.11+**: Primary programming language
- **FastAPI**: High-performance async web framework
- **Pydantic**: Data validation and settings management
- **Uvicorn**: ASGI server implementation
- **Celery**: Distributed task queue
- **Redis**: Message broker and cache
- **Docker**: Containerization platform
- **Docker Compose**: Multi-container orchestration

### 6.2 Database Technologies

- **PostgreSQL 15**: Relational database for structured data
- **Neo4j 5**: Graph database for knowledge representation
- **Qdrant**: Vector database for embeddings
- **AsyncPG**: PostgreSQL client for Python

### 6.3 AI and Machine Learning

- **LangChain**: Framework for developing applications with LLMs
- **OpenAI API**: GPT models
- **Anthropic API**: Claude models
- **Google AI API**: Gemini models
- **XAI API**: Grok models
- **Various other LLM providers**: Groq, Mistral, Perplexity, etc.

### 6.4 Development and Testing

- **Pytest**: Testing framework
- **Black**: Code formatting
- **Flake8**: Code linting
- **Pre-commit**: Git hooks for code quality
- **Docker**: Containerization for consistent environments